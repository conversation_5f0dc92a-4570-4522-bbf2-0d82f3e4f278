"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./src/hooks/useMediaStream.ts":
/*!*************************************!*\
  !*** ./src/hooks/useMediaStream.ts ***!
  \*************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n/**\n * useMediaStream Hook\n * \n * React hook for managing camera and microphone access via getUserMedia.\n * Provides camera device selection and stream management.\n */ \nconst useMediaStream = ()=>{\n    const [stream, setStream] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(null);\n    const [isStreaming, setIsStreaming] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(false);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(null);\n    const [devices, setDevices] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)([]);\n    const [selectedVideoDevice, setSelectedVideoDevice] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)('');\n    const [selectedAudioDevice, setSelectedAudioDevice] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)('');\n    const [isSupported, setIsSupported] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(false);\n    const streamRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(null);\n    // Timeout wrapper for getUserMedia calls\n    const getUserMediaWithTimeout = function(constraints) {\n        let timeoutMs = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : 10000;\n        return new Promise((resolve, reject)=>{\n            const timeoutId = setTimeout(()=>{\n                reject(new Error(\"getUserMedia timeout after \".concat(timeoutMs, \"ms\")));\n            }, timeoutMs);\n            navigator.mediaDevices.getUserMedia(constraints).then((stream)=>{\n                clearTimeout(timeoutId);\n                resolve(stream);\n            }).catch((error)=>{\n                clearTimeout(timeoutId);\n                reject(error);\n            });\n        });\n    };\n    // Progressive constraint fallback to prevent timeout errors\n    const tryGetUserMedia = async (options)=>{\n        const constraints = [\n            // First attempt: Full constraints with exact device IDs\n            options,\n            // Second attempt: Ideal constraints without exact device IDs\n            {\n                video: options.video ? {\n                    width: {\n                        ideal: 1280\n                    },\n                    height: {\n                        ideal: 720\n                    },\n                    frameRate: {\n                        ideal: 30\n                    }\n                } : false,\n                audio: options.audio ? {\n                    echoCancellation: true,\n                    noiseSuppression: true,\n                    autoGainControl: true\n                } : false\n            },\n            // Third attempt: Basic constraints\n            {\n                video: options.video ? {\n                    width: {\n                        ideal: 640\n                    },\n                    height: {\n                        ideal: 480\n                    }\n                } : false,\n                audio: options.audio ? true : false\n            },\n            // Final attempt: Minimal constraints\n            {\n                video: options.video ? true : false,\n                audio: options.audio ? true : false\n            }\n        ];\n        let lastError = null;\n        for(let i = 0; i < constraints.length; i++){\n            try {\n                console.log(\"Attempting getUserMedia with constraint set \".concat(i + 1, \"/\").concat(constraints.length));\n                const stream = await navigator.mediaDevices.getUserMedia(constraints[i]);\n                if (i > 0) {\n                    console.log(\"Successfully obtained media stream using fallback constraint set \".concat(i + 1));\n                }\n                return stream;\n            } catch (error) {\n                lastError = error;\n                console.warn(\"Constraint set \".concat(i + 1, \" failed:\"), error);\n                // If it's a permission error, don't try fallbacks\n                if (lastError.name === 'NotAllowedError') {\n                    throw lastError;\n                }\n            }\n        }\n        // If all attempts failed, throw the last error\n        throw lastError || new Error('Failed to access media devices');\n    };\n    // Refresh available media devices\n    const refreshDevices = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useMediaStream.useCallback[refreshDevices]\": async ()=>{\n            if (!isSupported) return;\n            try {\n                const deviceList = await navigator.mediaDevices.enumerateDevices();\n                const mediaDevices = deviceList.map({\n                    \"useMediaStream.useCallback[refreshDevices].mediaDevices\": (device)=>({\n                            deviceId: device.deviceId,\n                            label: device.label || \"\".concat(device.kind, \" \").concat(device.deviceId.slice(0, 8)),\n                            kind: device.kind\n                        })\n                }[\"useMediaStream.useCallback[refreshDevices].mediaDevices\"]);\n                setDevices(mediaDevices);\n                // Set default devices if none selected (only on first load)\n                if (!selectedVideoDevice && mediaDevices.length > 0) {\n                    const videoDevice = mediaDevices.find({\n                        \"useMediaStream.useCallback[refreshDevices].videoDevice\": (d)=>d.kind === 'videoinput'\n                    }[\"useMediaStream.useCallback[refreshDevices].videoDevice\"]);\n                    if (videoDevice) {\n                        setSelectedVideoDevice(videoDevice.deviceId);\n                    }\n                }\n                if (!selectedAudioDevice && mediaDevices.length > 0) {\n                    const audioDevice = mediaDevices.find({\n                        \"useMediaStream.useCallback[refreshDevices].audioDevice\": (d)=>d.kind === 'audioinput'\n                    }[\"useMediaStream.useCallback[refreshDevices].audioDevice\"]);\n                    if (audioDevice) {\n                        setSelectedAudioDevice(audioDevice.deviceId);\n                    }\n                }\n            } catch (err) {\n                console.error('Failed to enumerate media devices:', err);\n                setError('Failed to enumerate media devices');\n            }\n        }\n    }[\"useMediaStream.useCallback[refreshDevices]\"], [\n        isSupported\n    ]); // Remove selectedVideoDevice and selectedAudioDevice from dependencies\n    // Check for getUserMedia support\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)({\n        \"useMediaStream.useEffect\": ()=>{\n            if ( true && navigator.mediaDevices && typeof navigator.mediaDevices.getUserMedia === 'function') {\n                setIsSupported(true);\n            } else {\n                setIsSupported(false);\n                setError('Media devices are not supported in this browser');\n            }\n        }\n    }[\"useMediaStream.useEffect\"], []);\n    // Refresh devices when supported status changes\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)({\n        \"useMediaStream.useEffect\": ()=>{\n            if (isSupported) {\n                refreshDevices();\n            }\n        }\n    }[\"useMediaStream.useEffect\"], [\n        isSupported,\n        refreshDevices\n    ]);\n    // Start media stream\n    const startStream = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useMediaStream.useCallback[startStream]\": async function() {\n            let options = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : {};\n            if (!isSupported) {\n                setError('Media devices are not supported');\n                return;\n            }\n            if (isStreaming) {\n                return;\n            }\n            const defaultOptions = {\n                video: {\n                    deviceId: selectedVideoDevice ? {\n                        exact: selectedVideoDevice\n                    } : undefined,\n                    width: {\n                        ideal: 1280\n                    },\n                    height: {\n                        ideal: 720\n                    },\n                    frameRate: {\n                        ideal: 30\n                    }\n                },\n                audio: {\n                    deviceId: selectedAudioDevice ? {\n                        exact: selectedAudioDevice\n                    } : undefined,\n                    echoCancellation: true,\n                    noiseSuppression: true,\n                    autoGainControl: true\n                }\n            };\n            const streamOptions = {\n                ...defaultOptions,\n                ...options\n            };\n            try {\n                setError(null);\n                const mediaStream = await tryGetUserMedia(streamOptions);\n                streamRef.current = mediaStream;\n                setStream(mediaStream);\n                setIsStreaming(true);\n                // Refresh devices after getting permission (labels will be available)\n                await refreshDevices();\n            } catch (err) {\n                const error = err;\n                console.error('Failed to access media devices:', err);\n                // Provide more specific error messages\n                let errorMessage = 'Failed to access media devices';\n                if (error.name === 'NotAllowedError') {\n                    errorMessage = 'Camera/microphone access denied. Please allow permissions and try again.';\n                } else if (error.name === 'NotFoundError') {\n                    errorMessage = 'No camera or microphone found. Please connect a device and try again.';\n                } else if (error.name === 'NotReadableError') {\n                    errorMessage = 'Camera/microphone is already in use by another application.';\n                } else if (error.name === 'OverconstrainedError') {\n                    errorMessage = 'Camera/microphone constraints cannot be satisfied. Try different settings.';\n                } else if (error.message) {\n                    errorMessage = \"Media access error: \".concat(error.message);\n                }\n                setError(errorMessage);\n                setIsStreaming(false);\n            }\n        }\n    }[\"useMediaStream.useCallback[startStream]\"], [\n        isSupported,\n        isStreaming,\n        selectedVideoDevice,\n        selectedAudioDevice,\n        refreshDevices\n    ]);\n    // Stop media stream\n    const stopStream = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useMediaStream.useCallback[stopStream]\": ()=>{\n            if (streamRef.current) {\n                streamRef.current.getTracks().forEach({\n                    \"useMediaStream.useCallback[stopStream]\": (track)=>{\n                        track.stop();\n                    }\n                }[\"useMediaStream.useCallback[stopStream]\"]);\n                streamRef.current = null;\n                setStream(null);\n                setIsStreaming(false);\n            }\n        }\n    }[\"useMediaStream.useCallback[stopStream]\"], []);\n    // Switch video device\n    const switchVideoDevice = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useMediaStream.useCallback[switchVideoDevice]\": async (deviceId)=>{\n            setSelectedVideoDevice(deviceId);\n            if (isStreaming) {\n                // Stop current stream and start with new device\n                stopStream();\n                await startStream({\n                    video: {\n                        deviceId: {\n                            exact: deviceId\n                        },\n                        width: {\n                            ideal: 1280\n                        },\n                        height: {\n                            ideal: 720\n                        },\n                        frameRate: {\n                            ideal: 30\n                        }\n                    },\n                    audio: {\n                        deviceId: selectedAudioDevice ? {\n                            exact: selectedAudioDevice\n                        } : undefined,\n                        echoCancellation: true,\n                        noiseSuppression: true,\n                        autoGainControl: true\n                    }\n                });\n            }\n        }\n    }[\"useMediaStream.useCallback[switchVideoDevice]\"], [\n        isStreaming,\n        selectedAudioDevice,\n        stopStream,\n        startStream\n    ]);\n    // Switch audio device\n    const switchAudioDevice = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useMediaStream.useCallback[switchAudioDevice]\": async (deviceId)=>{\n            setSelectedAudioDevice(deviceId);\n            if (isStreaming) {\n                // Stop current stream and start with new device\n                stopStream();\n                await startStream({\n                    video: {\n                        deviceId: selectedVideoDevice ? {\n                            exact: selectedVideoDevice\n                        } : undefined,\n                        width: {\n                            ideal: 1280\n                        },\n                        height: {\n                            ideal: 720\n                        },\n                        frameRate: {\n                            ideal: 30\n                        }\n                    },\n                    audio: {\n                        deviceId: {\n                            exact: deviceId\n                        },\n                        echoCancellation: true,\n                        noiseSuppression: true,\n                        autoGainControl: true\n                    }\n                });\n            }\n        }\n    }[\"useMediaStream.useCallback[switchAudioDevice]\"], [\n        isStreaming,\n        selectedVideoDevice,\n        stopStream,\n        startStream\n    ]);\n    // Cleanup on unmount\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)({\n        \"useMediaStream.useEffect\": ()=>{\n            return ({\n                \"useMediaStream.useEffect\": ()=>{\n                    stopStream();\n                }\n            })[\"useMediaStream.useEffect\"];\n        }\n    }[\"useMediaStream.useEffect\"], [\n        stopStream\n    ]);\n    return {\n        stream,\n        isStreaming,\n        error,\n        devices,\n        selectedVideoDevice,\n        selectedAudioDevice,\n        startStream,\n        stopStream,\n        switchVideoDevice,\n        switchAudioDevice,\n        refreshDevices,\n        isSupported\n    };\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (useMediaStream);\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL3NyYy9ob29rcy91c2VNZWRpYVN0cmVhbS50cyIsIm1hcHBpbmdzIjoiOzs7Ozs7QUFBQTs7Ozs7Q0FLQyxHQUVnRTtBQTRCakUsTUFBTUksaUJBQWlCO0lBQ3JCLE1BQU0sQ0FBQ0MsUUFBUUMsVUFBVSxHQUFHTiwrQ0FBUUEsQ0FBcUI7SUFDekQsTUFBTSxDQUFDTyxhQUFhQyxlQUFlLEdBQUdSLCtDQUFRQSxDQUFVO0lBQ3hELE1BQU0sQ0FBQ1MsT0FBT0MsU0FBUyxHQUFHViwrQ0FBUUEsQ0FBZ0I7SUFDbEQsTUFBTSxDQUFDVyxTQUFTQyxXQUFXLEdBQUdaLCtDQUFRQSxDQUFnQixFQUFFO0lBQ3hELE1BQU0sQ0FBQ2EscUJBQXFCQyx1QkFBdUIsR0FBR2QsK0NBQVFBLENBQVM7SUFDdkUsTUFBTSxDQUFDZSxxQkFBcUJDLHVCQUF1QixHQUFHaEIsK0NBQVFBLENBQVM7SUFDdkUsTUFBTSxDQUFDaUIsYUFBYUMsZUFBZSxHQUFHbEIsK0NBQVFBLENBQVU7SUFFeEQsTUFBTW1CLFlBQVlsQiw2Q0FBTUEsQ0FBcUI7SUFFN0MseUNBQXlDO0lBQ3pDLE1BQU1tQiwwQkFBMEIsU0FBQ0M7WUFBcUNDLDZFQUFvQjtRQUN4RixPQUFPLElBQUlDLFFBQVEsQ0FBQ0MsU0FBU0M7WUFDM0IsTUFBTUMsWUFBWUMsV0FBVztnQkFDM0JGLE9BQU8sSUFBSUcsTUFBTSw4QkFBd0MsT0FBVk4sV0FBVTtZQUMzRCxHQUFHQTtZQUVITyxVQUFVQyxZQUFZLENBQUNDLFlBQVksQ0FBQ1YsYUFDakNXLElBQUksQ0FBQzNCLENBQUFBO2dCQUNKNEIsYUFBYVA7Z0JBQ2JGLFFBQVFuQjtZQUNWLEdBQ0M2QixLQUFLLENBQUN6QixDQUFBQTtnQkFDTHdCLGFBQWFQO2dCQUNiRCxPQUFPaEI7WUFDVDtRQUNKO0lBQ0Y7SUFFQSw0REFBNEQ7SUFDNUQsTUFBTTBCLGtCQUFrQixPQUFPQztRQUM3QixNQUFNZixjQUFjO1lBQ2xCLHdEQUF3RDtZQUN4RGU7WUFDQSw2REFBNkQ7WUFDN0Q7Z0JBQ0VDLE9BQU9ELFFBQVFDLEtBQUssR0FBRztvQkFDckJDLE9BQU87d0JBQUVDLE9BQU87b0JBQUs7b0JBQ3JCQyxRQUFRO3dCQUFFRCxPQUFPO29CQUFJO29CQUNyQkUsV0FBVzt3QkFBRUYsT0FBTztvQkFBRztnQkFDekIsSUFBSTtnQkFDSkcsT0FBT04sUUFBUU0sS0FBSyxHQUFHO29CQUNyQkMsa0JBQWtCO29CQUNsQkMsa0JBQWtCO29CQUNsQkMsaUJBQWlCO2dCQUNuQixJQUFJO1lBQ047WUFDQSxtQ0FBbUM7WUFDbkM7Z0JBQ0VSLE9BQU9ELFFBQVFDLEtBQUssR0FBRztvQkFDckJDLE9BQU87d0JBQUVDLE9BQU87b0JBQUk7b0JBQ3BCQyxRQUFRO3dCQUFFRCxPQUFPO29CQUFJO2dCQUN2QixJQUFJO2dCQUNKRyxPQUFPTixRQUFRTSxLQUFLLEdBQUcsT0FBTztZQUNoQztZQUNBLHFDQUFxQztZQUNyQztnQkFDRUwsT0FBT0QsUUFBUUMsS0FBSyxHQUFHLE9BQU87Z0JBQzlCSyxPQUFPTixRQUFRTSxLQUFLLEdBQUcsT0FBTztZQUNoQztTQUNEO1FBRUQsSUFBSUksWUFBMEI7UUFFOUIsSUFBSyxJQUFJQyxJQUFJLEdBQUdBLElBQUkxQixZQUFZMkIsTUFBTSxFQUFFRCxJQUFLO1lBQzNDLElBQUk7Z0JBQ0ZFLFFBQVFDLEdBQUcsQ0FBQywrQ0FBd0Q3QixPQUFUMEIsSUFBSSxHQUFFLEtBQXNCLE9BQW5CMUIsWUFBWTJCLE1BQU07Z0JBQ3RGLE1BQU0zQyxTQUFTLE1BQU13QixVQUFVQyxZQUFZLENBQUNDLFlBQVksQ0FBQ1YsV0FBVyxDQUFDMEIsRUFBRTtnQkFDdkUsSUFBSUEsSUFBSSxHQUFHO29CQUNURSxRQUFRQyxHQUFHLENBQUMsb0VBQTBFLE9BQU5ILElBQUk7Z0JBQ3RGO2dCQUNBLE9BQU8xQztZQUNULEVBQUUsT0FBT0ksT0FBTztnQkFDZHFDLFlBQVlyQztnQkFDWndDLFFBQVFFLElBQUksQ0FBQyxrQkFBd0IsT0FBTkosSUFBSSxHQUFFLGFBQVd0QztnQkFFaEQsa0RBQWtEO2dCQUNsRCxJQUFJcUMsVUFBVU0sSUFBSSxLQUFLLG1CQUFtQjtvQkFDeEMsTUFBTU47Z0JBQ1I7WUFDRjtRQUNGO1FBRUEsK0NBQStDO1FBQy9DLE1BQU1BLGFBQWEsSUFBSWxCLE1BQU07SUFDL0I7SUFFQSxrQ0FBa0M7SUFDbEMsTUFBTXlCLGlCQUFpQm5ELGtEQUFXQTtzREFBQztZQUNqQyxJQUFJLENBQUNlLGFBQWE7WUFFbEIsSUFBSTtnQkFDRixNQUFNcUMsYUFBYSxNQUFNekIsVUFBVUMsWUFBWSxDQUFDeUIsZ0JBQWdCO2dCQUNoRSxNQUFNekIsZUFBOEJ3QixXQUFXRSxHQUFHOytFQUFDQyxDQUFBQSxTQUFXOzRCQUM1REMsVUFBVUQsT0FBT0MsUUFBUTs0QkFDekJDLE9BQU9GLE9BQU9FLEtBQUssSUFBSSxHQUFrQkYsT0FBZkEsT0FBT0csSUFBSSxFQUFDLEtBQStCLE9BQTVCSCxPQUFPQyxRQUFRLENBQUNHLEtBQUssQ0FBQyxHQUFHOzRCQUNsRUQsTUFBTUgsT0FBT0csSUFBSTt3QkFDbkI7O2dCQUVBaEQsV0FBV2tCO2dCQUVYLDREQUE0RDtnQkFDNUQsSUFBSSxDQUFDakIsdUJBQXVCaUIsYUFBYWtCLE1BQU0sR0FBRyxHQUFHO29CQUNuRCxNQUFNYyxjQUFjaEMsYUFBYWlDLElBQUk7a0ZBQUNDLENBQUFBLElBQUtBLEVBQUVKLElBQUksS0FBSzs7b0JBQ3RELElBQUlFLGFBQWE7d0JBQ2ZoRCx1QkFBdUJnRCxZQUFZSixRQUFRO29CQUM3QztnQkFDRjtnQkFFQSxJQUFJLENBQUMzQyx1QkFBdUJlLGFBQWFrQixNQUFNLEdBQUcsR0FBRztvQkFDbkQsTUFBTWlCLGNBQWNuQyxhQUFhaUMsSUFBSTtrRkFBQ0MsQ0FBQUEsSUFBS0EsRUFBRUosSUFBSSxLQUFLOztvQkFDdEQsSUFBSUssYUFBYTt3QkFDZmpELHVCQUF1QmlELFlBQVlQLFFBQVE7b0JBQzdDO2dCQUNGO1lBQ0YsRUFBRSxPQUFPUSxLQUFLO2dCQUNaakIsUUFBUXhDLEtBQUssQ0FBQyxzQ0FBc0N5RDtnQkFDcER4RCxTQUFTO1lBQ1g7UUFDRjtxREFBRztRQUFDTztLQUFZLEdBQUcsdUVBQXVFO0lBRTFGLGlDQUFpQztJQUNqQ2QsZ0RBQVNBO29DQUFDO1lBQ1IsSUFBSSxLQUE2QixJQUFJMEIsVUFBVUMsWUFBWSxJQUFJLE9BQU9ELFVBQVVDLFlBQVksQ0FBQ0MsWUFBWSxLQUFLLFlBQVk7Z0JBQ3hIYixlQUFlO1lBQ2pCLE9BQU87Z0JBQ0xBLGVBQWU7Z0JBQ2ZSLFNBQVM7WUFDWDtRQUNGO21DQUFHLEVBQUU7SUFFTCxnREFBZ0Q7SUFDaERQLGdEQUFTQTtvQ0FBQztZQUNSLElBQUljLGFBQWE7Z0JBQ2ZvQztZQUNGO1FBQ0Y7bUNBQUc7UUFBQ3BDO1FBQWFvQztLQUFlO0lBSWhDLHFCQUFxQjtJQUNyQixNQUFNYyxjQUFjakUsa0RBQVdBO21EQUFDO2dCQUFPa0MsMkVBQXVDLENBQUM7WUFDN0UsSUFBSSxDQUFDbkIsYUFBYTtnQkFDaEJQLFNBQVM7Z0JBQ1Q7WUFDRjtZQUVBLElBQUlILGFBQWE7Z0JBQ2Y7WUFDRjtZQUVBLE1BQU02RCxpQkFBcUM7Z0JBQ3pDL0IsT0FBTztvQkFDTHFCLFVBQVU3QyxzQkFBc0I7d0JBQUV3RCxPQUFPeEQ7b0JBQW9CLElBQUl5RDtvQkFDakVoQyxPQUFPO3dCQUFFQyxPQUFPO29CQUFLO29CQUNyQkMsUUFBUTt3QkFBRUQsT0FBTztvQkFBSTtvQkFDckJFLFdBQVc7d0JBQUVGLE9BQU87b0JBQUc7Z0JBQ3pCO2dCQUNBRyxPQUFPO29CQUNMZ0IsVUFBVTNDLHNCQUFzQjt3QkFBRXNELE9BQU90RDtvQkFBb0IsSUFBSXVEO29CQUNqRTNCLGtCQUFrQjtvQkFDbEJDLGtCQUFrQjtvQkFDbEJDLGlCQUFpQjtnQkFDbkI7WUFDRjtZQUVBLE1BQU0wQixnQkFBZ0I7Z0JBQUUsR0FBR0gsY0FBYztnQkFBRSxHQUFHaEMsT0FBTztZQUFDO1lBRXRELElBQUk7Z0JBQ0YxQixTQUFTO2dCQUNULE1BQU04RCxjQUFjLE1BQU1yQyxnQkFBZ0JvQztnQkFFMUNwRCxVQUFVc0QsT0FBTyxHQUFHRDtnQkFDcEJsRSxVQUFVa0U7Z0JBQ1ZoRSxlQUFlO2dCQUVmLHNFQUFzRTtnQkFDdEUsTUFBTTZDO1lBQ1IsRUFBRSxPQUFPYSxLQUFLO2dCQUNaLE1BQU16RCxRQUFReUQ7Z0JBQ2RqQixRQUFReEMsS0FBSyxDQUFDLG1DQUFtQ3lEO2dCQUVqRCx1Q0FBdUM7Z0JBQ3ZDLElBQUlRLGVBQWU7Z0JBQ25CLElBQUlqRSxNQUFNMkMsSUFBSSxLQUFLLG1CQUFtQjtvQkFDcENzQixlQUFlO2dCQUNqQixPQUFPLElBQUlqRSxNQUFNMkMsSUFBSSxLQUFLLGlCQUFpQjtvQkFDekNzQixlQUFlO2dCQUNqQixPQUFPLElBQUlqRSxNQUFNMkMsSUFBSSxLQUFLLG9CQUFvQjtvQkFDNUNzQixlQUFlO2dCQUNqQixPQUFPLElBQUlqRSxNQUFNMkMsSUFBSSxLQUFLLHdCQUF3QjtvQkFDaERzQixlQUFlO2dCQUNqQixPQUFPLElBQUlqRSxNQUFNa0UsT0FBTyxFQUFFO29CQUN4QkQsZUFBZSx1QkFBcUMsT0FBZGpFLE1BQU1rRSxPQUFPO2dCQUNyRDtnQkFFQWpFLFNBQVNnRTtnQkFDVGxFLGVBQWU7WUFDakI7UUFDRjtrREFBRztRQUFDUztRQUFhVjtRQUFhTTtRQUFxQkU7UUFBcUJzQztLQUFlO0lBRXZGLG9CQUFvQjtJQUNwQixNQUFNdUIsYUFBYTFFLGtEQUFXQTtrREFBQztZQUM3QixJQUFJaUIsVUFBVXNELE9BQU8sRUFBRTtnQkFDckJ0RCxVQUFVc0QsT0FBTyxDQUFDSSxTQUFTLEdBQUdDLE9BQU87OERBQUNDLENBQUFBO3dCQUNwQ0EsTUFBTUMsSUFBSTtvQkFDWjs7Z0JBQ0E3RCxVQUFVc0QsT0FBTyxHQUFHO2dCQUNwQm5FLFVBQVU7Z0JBQ1ZFLGVBQWU7WUFDakI7UUFDRjtpREFBRyxFQUFFO0lBRUwsc0JBQXNCO0lBQ3RCLE1BQU15RSxvQkFBb0IvRSxrREFBV0E7eURBQUMsT0FBT3dEO1lBQzNDNUMsdUJBQXVCNEM7WUFFdkIsSUFBSW5ELGFBQWE7Z0JBQ2YsZ0RBQWdEO2dCQUNoRHFFO2dCQUNBLE1BQU1ULFlBQVk7b0JBQ2hCOUIsT0FBTzt3QkFDTHFCLFVBQVU7NEJBQUVXLE9BQU9YO3dCQUFTO3dCQUM1QnBCLE9BQU87NEJBQUVDLE9BQU87d0JBQUs7d0JBQ3JCQyxRQUFROzRCQUFFRCxPQUFPO3dCQUFJO3dCQUNyQkUsV0FBVzs0QkFBRUYsT0FBTzt3QkFBRztvQkFDekI7b0JBQ0FHLE9BQU87d0JBQ0xnQixVQUFVM0Msc0JBQXNCOzRCQUFFc0QsT0FBT3REO3dCQUFvQixJQUFJdUQ7d0JBQ2pFM0Isa0JBQWtCO3dCQUNsQkMsa0JBQWtCO3dCQUNsQkMsaUJBQWlCO29CQUNuQjtnQkFDRjtZQUNGO1FBQ0Y7d0RBQUc7UUFBQ3RDO1FBQWFRO1FBQXFCNkQ7UUFBWVQ7S0FBWTtJQUU5RCxzQkFBc0I7SUFDdEIsTUFBTWUsb0JBQW9CaEYsa0RBQVdBO3lEQUFDLE9BQU93RDtZQUMzQzFDLHVCQUF1QjBDO1lBRXZCLElBQUluRCxhQUFhO2dCQUNmLGdEQUFnRDtnQkFDaERxRTtnQkFDQSxNQUFNVCxZQUFZO29CQUNoQjlCLE9BQU87d0JBQ0xxQixVQUFVN0Msc0JBQXNCOzRCQUFFd0QsT0FBT3hEO3dCQUFvQixJQUFJeUQ7d0JBQ2pFaEMsT0FBTzs0QkFBRUMsT0FBTzt3QkFBSzt3QkFDckJDLFFBQVE7NEJBQUVELE9BQU87d0JBQUk7d0JBQ3JCRSxXQUFXOzRCQUFFRixPQUFPO3dCQUFHO29CQUN6QjtvQkFDQUcsT0FBTzt3QkFDTGdCLFVBQVU7NEJBQUVXLE9BQU9YO3dCQUFTO3dCQUM1QmYsa0JBQWtCO3dCQUNsQkMsa0JBQWtCO3dCQUNsQkMsaUJBQWlCO29CQUNuQjtnQkFDRjtZQUNGO1FBQ0Y7d0RBQUc7UUFBQ3RDO1FBQWFNO1FBQXFCK0Q7UUFBWVQ7S0FBWTtJQUU5RCxxQkFBcUI7SUFDckJoRSxnREFBU0E7b0NBQUM7WUFDUjs0Q0FBTztvQkFDTHlFO2dCQUNGOztRQUNGO21DQUFHO1FBQUNBO0tBQVc7SUFFZixPQUFPO1FBQ0x2RTtRQUNBRTtRQUNBRTtRQUNBRTtRQUNBRTtRQUNBRTtRQUNBb0Q7UUFDQVM7UUFDQUs7UUFDQUM7UUFDQTdCO1FBQ0FwQztJQUNGO0FBQ0Y7QUFFQSxpRUFBZWIsY0FBY0EsRUFBQyIsInNvdXJjZXMiOlsiQzpcXFVzZXJzXFxBbGl5dVN1bnVzaVxcT25lRHJpdmUgLSBVR1NNLU1vbmFyY2ggQnVzaW5lc3MgU2Nob29sIEdtYkhcXERvY3VtZW50c1xcY2FwdGlvbm5pbmphXFxjYXB0aW9uLW5pbmphLW5leHRqc1xcc3JjXFxob29rc1xcdXNlTWVkaWFTdHJlYW0udHMiXSwic291cmNlc0NvbnRlbnQiOlsiLyoqXG4gKiB1c2VNZWRpYVN0cmVhbSBIb29rXG4gKiBcbiAqIFJlYWN0IGhvb2sgZm9yIG1hbmFnaW5nIGNhbWVyYSBhbmQgbWljcm9waG9uZSBhY2Nlc3MgdmlhIGdldFVzZXJNZWRpYS5cbiAqIFByb3ZpZGVzIGNhbWVyYSBkZXZpY2Ugc2VsZWN0aW9uIGFuZCBzdHJlYW0gbWFuYWdlbWVudC5cbiAqL1xuXG5pbXBvcnQgeyB1c2VTdGF0ZSwgdXNlUmVmLCB1c2VDYWxsYmFjaywgdXNlRWZmZWN0IH0gZnJvbSAncmVhY3QnO1xuXG5pbnRlcmZhY2UgTWVkaWFTdHJlYW1PcHRpb25zIHtcbiAgdmlkZW86IGJvb2xlYW4gfCBNZWRpYVRyYWNrQ29uc3RyYWludHM7XG4gIGF1ZGlvOiBib29sZWFuIHwgTWVkaWFUcmFja0NvbnN0cmFpbnRzO1xufVxuXG5pbnRlcmZhY2UgTWVkaWFEZXZpY2Uge1xuICBkZXZpY2VJZDogc3RyaW5nO1xuICBsYWJlbDogc3RyaW5nO1xuICBraW5kOiBNZWRpYURldmljZUtpbmQ7XG59XG5cbmludGVyZmFjZSBNZWRpYVN0cmVhbUhvb2sge1xuICBzdHJlYW06IE1lZGlhU3RyZWFtIHwgbnVsbDtcbiAgaXNTdHJlYW1pbmc6IGJvb2xlYW47XG4gIGVycm9yOiBzdHJpbmcgfCBudWxsO1xuICBkZXZpY2VzOiBNZWRpYURldmljZVtdO1xuICBzZWxlY3RlZFZpZGVvRGV2aWNlOiBzdHJpbmc7XG4gIHNlbGVjdGVkQXVkaW9EZXZpY2U6IHN0cmluZztcbiAgc3RhcnRTdHJlYW06IChvcHRpb25zPzogUGFydGlhbDxNZWRpYVN0cmVhbU9wdGlvbnM+KSA9PiBQcm9taXNlPHZvaWQ+O1xuICBzdG9wU3RyZWFtOiAoKSA9PiB2b2lkO1xuICBzd2l0Y2hWaWRlb0RldmljZTogKGRldmljZUlkOiBzdHJpbmcpID0+IFByb21pc2U8dm9pZD47XG4gIHN3aXRjaEF1ZGlvRGV2aWNlOiAoZGV2aWNlSWQ6IHN0cmluZykgPT4gUHJvbWlzZTx2b2lkPjtcbiAgcmVmcmVzaERldmljZXM6ICgpID0+IFByb21pc2U8dm9pZD47XG4gIGlzU3VwcG9ydGVkOiBib29sZWFuO1xufVxuXG5jb25zdCB1c2VNZWRpYVN0cmVhbSA9ICgpOiBNZWRpYVN0cmVhbUhvb2sgPT4ge1xuICBjb25zdCBbc3RyZWFtLCBzZXRTdHJlYW1dID0gdXNlU3RhdGU8TWVkaWFTdHJlYW0gfCBudWxsPihudWxsKTtcbiAgY29uc3QgW2lzU3RyZWFtaW5nLCBzZXRJc1N0cmVhbWluZ10gPSB1c2VTdGF0ZTxib29sZWFuPihmYWxzZSk7XG4gIGNvbnN0IFtlcnJvciwgc2V0RXJyb3JdID0gdXNlU3RhdGU8c3RyaW5nIHwgbnVsbD4obnVsbCk7XG4gIGNvbnN0IFtkZXZpY2VzLCBzZXREZXZpY2VzXSA9IHVzZVN0YXRlPE1lZGlhRGV2aWNlW10+KFtdKTtcbiAgY29uc3QgW3NlbGVjdGVkVmlkZW9EZXZpY2UsIHNldFNlbGVjdGVkVmlkZW9EZXZpY2VdID0gdXNlU3RhdGU8c3RyaW5nPignJyk7XG4gIGNvbnN0IFtzZWxlY3RlZEF1ZGlvRGV2aWNlLCBzZXRTZWxlY3RlZEF1ZGlvRGV2aWNlXSA9IHVzZVN0YXRlPHN0cmluZz4oJycpO1xuICBjb25zdCBbaXNTdXBwb3J0ZWQsIHNldElzU3VwcG9ydGVkXSA9IHVzZVN0YXRlPGJvb2xlYW4+KGZhbHNlKTtcblxuICBjb25zdCBzdHJlYW1SZWYgPSB1c2VSZWY8TWVkaWFTdHJlYW0gfCBudWxsPihudWxsKTtcblxuICAvLyBUaW1lb3V0IHdyYXBwZXIgZm9yIGdldFVzZXJNZWRpYSBjYWxsc1xuICBjb25zdCBnZXRVc2VyTWVkaWFXaXRoVGltZW91dCA9IChjb25zdHJhaW50czogTWVkaWFTdHJlYW1Db25zdHJhaW50cywgdGltZW91dE1zOiBudW1iZXIgPSAxMDAwMCk6IFByb21pc2U8TWVkaWFTdHJlYW0+ID0+IHtcbiAgICByZXR1cm4gbmV3IFByb21pc2UoKHJlc29sdmUsIHJlamVjdCkgPT4ge1xuICAgICAgY29uc3QgdGltZW91dElkID0gc2V0VGltZW91dCgoKSA9PiB7XG4gICAgICAgIHJlamVjdChuZXcgRXJyb3IoYGdldFVzZXJNZWRpYSB0aW1lb3V0IGFmdGVyICR7dGltZW91dE1zfW1zYCkpO1xuICAgICAgfSwgdGltZW91dE1zKTtcblxuICAgICAgbmF2aWdhdG9yLm1lZGlhRGV2aWNlcy5nZXRVc2VyTWVkaWEoY29uc3RyYWludHMpXG4gICAgICAgIC50aGVuKHN0cmVhbSA9PiB7XG4gICAgICAgICAgY2xlYXJUaW1lb3V0KHRpbWVvdXRJZCk7XG4gICAgICAgICAgcmVzb2x2ZShzdHJlYW0pO1xuICAgICAgICB9KVxuICAgICAgICAuY2F0Y2goZXJyb3IgPT4ge1xuICAgICAgICAgIGNsZWFyVGltZW91dCh0aW1lb3V0SWQpO1xuICAgICAgICAgIHJlamVjdChlcnJvcik7XG4gICAgICAgIH0pO1xuICAgIH0pO1xuICB9O1xuXG4gIC8vIFByb2dyZXNzaXZlIGNvbnN0cmFpbnQgZmFsbGJhY2sgdG8gcHJldmVudCB0aW1lb3V0IGVycm9yc1xuICBjb25zdCB0cnlHZXRVc2VyTWVkaWEgPSBhc3luYyAob3B0aW9uczogTWVkaWFTdHJlYW1PcHRpb25zKTogUHJvbWlzZTxNZWRpYVN0cmVhbT4gPT4ge1xuICAgIGNvbnN0IGNvbnN0cmFpbnRzID0gW1xuICAgICAgLy8gRmlyc3QgYXR0ZW1wdDogRnVsbCBjb25zdHJhaW50cyB3aXRoIGV4YWN0IGRldmljZSBJRHNcbiAgICAgIG9wdGlvbnMsXG4gICAgICAvLyBTZWNvbmQgYXR0ZW1wdDogSWRlYWwgY29uc3RyYWludHMgd2l0aG91dCBleGFjdCBkZXZpY2UgSURzXG4gICAgICB7XG4gICAgICAgIHZpZGVvOiBvcHRpb25zLnZpZGVvID8ge1xuICAgICAgICAgIHdpZHRoOiB7IGlkZWFsOiAxMjgwIH0sXG4gICAgICAgICAgaGVpZ2h0OiB7IGlkZWFsOiA3MjAgfSxcbiAgICAgICAgICBmcmFtZVJhdGU6IHsgaWRlYWw6IDMwIH1cbiAgICAgICAgfSA6IGZhbHNlLFxuICAgICAgICBhdWRpbzogb3B0aW9ucy5hdWRpbyA/IHtcbiAgICAgICAgICBlY2hvQ2FuY2VsbGF0aW9uOiB0cnVlLFxuICAgICAgICAgIG5vaXNlU3VwcHJlc3Npb246IHRydWUsXG4gICAgICAgICAgYXV0b0dhaW5Db250cm9sOiB0cnVlXG4gICAgICAgIH0gOiBmYWxzZVxuICAgICAgfSxcbiAgICAgIC8vIFRoaXJkIGF0dGVtcHQ6IEJhc2ljIGNvbnN0cmFpbnRzXG4gICAgICB7XG4gICAgICAgIHZpZGVvOiBvcHRpb25zLnZpZGVvID8ge1xuICAgICAgICAgIHdpZHRoOiB7IGlkZWFsOiA2NDAgfSxcbiAgICAgICAgICBoZWlnaHQ6IHsgaWRlYWw6IDQ4MCB9XG4gICAgICAgIH0gOiBmYWxzZSxcbiAgICAgICAgYXVkaW86IG9wdGlvbnMuYXVkaW8gPyB0cnVlIDogZmFsc2VcbiAgICAgIH0sXG4gICAgICAvLyBGaW5hbCBhdHRlbXB0OiBNaW5pbWFsIGNvbnN0cmFpbnRzXG4gICAgICB7XG4gICAgICAgIHZpZGVvOiBvcHRpb25zLnZpZGVvID8gdHJ1ZSA6IGZhbHNlLFxuICAgICAgICBhdWRpbzogb3B0aW9ucy5hdWRpbyA/IHRydWUgOiBmYWxzZVxuICAgICAgfVxuICAgIF07XG5cbiAgICBsZXQgbGFzdEVycm9yOiBFcnJvciB8IG51bGwgPSBudWxsO1xuXG4gICAgZm9yIChsZXQgaSA9IDA7IGkgPCBjb25zdHJhaW50cy5sZW5ndGg7IGkrKykge1xuICAgICAgdHJ5IHtcbiAgICAgICAgY29uc29sZS5sb2coYEF0dGVtcHRpbmcgZ2V0VXNlck1lZGlhIHdpdGggY29uc3RyYWludCBzZXQgJHtpICsgMX0vJHtjb25zdHJhaW50cy5sZW5ndGh9YCk7XG4gICAgICAgIGNvbnN0IHN0cmVhbSA9IGF3YWl0IG5hdmlnYXRvci5tZWRpYURldmljZXMuZ2V0VXNlck1lZGlhKGNvbnN0cmFpbnRzW2ldKTtcbiAgICAgICAgaWYgKGkgPiAwKSB7XG4gICAgICAgICAgY29uc29sZS5sb2coYFN1Y2Nlc3NmdWxseSBvYnRhaW5lZCBtZWRpYSBzdHJlYW0gdXNpbmcgZmFsbGJhY2sgY29uc3RyYWludCBzZXQgJHtpICsgMX1gKTtcbiAgICAgICAgfVxuICAgICAgICByZXR1cm4gc3RyZWFtO1xuICAgICAgfSBjYXRjaCAoZXJyb3IpIHtcbiAgICAgICAgbGFzdEVycm9yID0gZXJyb3IgYXMgRXJyb3I7XG4gICAgICAgIGNvbnNvbGUud2FybihgQ29uc3RyYWludCBzZXQgJHtpICsgMX0gZmFpbGVkOmAsIGVycm9yKTtcblxuICAgICAgICAvLyBJZiBpdCdzIGEgcGVybWlzc2lvbiBlcnJvciwgZG9uJ3QgdHJ5IGZhbGxiYWNrc1xuICAgICAgICBpZiAobGFzdEVycm9yLm5hbWUgPT09ICdOb3RBbGxvd2VkRXJyb3InKSB7XG4gICAgICAgICAgdGhyb3cgbGFzdEVycm9yO1xuICAgICAgICB9XG4gICAgICB9XG4gICAgfVxuXG4gICAgLy8gSWYgYWxsIGF0dGVtcHRzIGZhaWxlZCwgdGhyb3cgdGhlIGxhc3QgZXJyb3JcbiAgICB0aHJvdyBsYXN0RXJyb3IgfHwgbmV3IEVycm9yKCdGYWlsZWQgdG8gYWNjZXNzIG1lZGlhIGRldmljZXMnKTtcbiAgfTtcblxuICAvLyBSZWZyZXNoIGF2YWlsYWJsZSBtZWRpYSBkZXZpY2VzXG4gIGNvbnN0IHJlZnJlc2hEZXZpY2VzID0gdXNlQ2FsbGJhY2soYXN5bmMgKCkgPT4ge1xuICAgIGlmICghaXNTdXBwb3J0ZWQpIHJldHVybjtcblxuICAgIHRyeSB7XG4gICAgICBjb25zdCBkZXZpY2VMaXN0ID0gYXdhaXQgbmF2aWdhdG9yLm1lZGlhRGV2aWNlcy5lbnVtZXJhdGVEZXZpY2VzKCk7XG4gICAgICBjb25zdCBtZWRpYURldmljZXM6IE1lZGlhRGV2aWNlW10gPSBkZXZpY2VMaXN0Lm1hcChkZXZpY2UgPT4gKHtcbiAgICAgICAgZGV2aWNlSWQ6IGRldmljZS5kZXZpY2VJZCxcbiAgICAgICAgbGFiZWw6IGRldmljZS5sYWJlbCB8fCBgJHtkZXZpY2Uua2luZH0gJHtkZXZpY2UuZGV2aWNlSWQuc2xpY2UoMCwgOCl9YCxcbiAgICAgICAga2luZDogZGV2aWNlLmtpbmRcbiAgICAgIH0pKTtcblxuICAgICAgc2V0RGV2aWNlcyhtZWRpYURldmljZXMpO1xuXG4gICAgICAvLyBTZXQgZGVmYXVsdCBkZXZpY2VzIGlmIG5vbmUgc2VsZWN0ZWQgKG9ubHkgb24gZmlyc3QgbG9hZClcbiAgICAgIGlmICghc2VsZWN0ZWRWaWRlb0RldmljZSAmJiBtZWRpYURldmljZXMubGVuZ3RoID4gMCkge1xuICAgICAgICBjb25zdCB2aWRlb0RldmljZSA9IG1lZGlhRGV2aWNlcy5maW5kKGQgPT4gZC5raW5kID09PSAndmlkZW9pbnB1dCcpO1xuICAgICAgICBpZiAodmlkZW9EZXZpY2UpIHtcbiAgICAgICAgICBzZXRTZWxlY3RlZFZpZGVvRGV2aWNlKHZpZGVvRGV2aWNlLmRldmljZUlkKTtcbiAgICAgICAgfVxuICAgICAgfVxuXG4gICAgICBpZiAoIXNlbGVjdGVkQXVkaW9EZXZpY2UgJiYgbWVkaWFEZXZpY2VzLmxlbmd0aCA+IDApIHtcbiAgICAgICAgY29uc3QgYXVkaW9EZXZpY2UgPSBtZWRpYURldmljZXMuZmluZChkID0+IGQua2luZCA9PT0gJ2F1ZGlvaW5wdXQnKTtcbiAgICAgICAgaWYgKGF1ZGlvRGV2aWNlKSB7XG4gICAgICAgICAgc2V0U2VsZWN0ZWRBdWRpb0RldmljZShhdWRpb0RldmljZS5kZXZpY2VJZCk7XG4gICAgICAgIH1cbiAgICAgIH1cbiAgICB9IGNhdGNoIChlcnIpIHtcbiAgICAgIGNvbnNvbGUuZXJyb3IoJ0ZhaWxlZCB0byBlbnVtZXJhdGUgbWVkaWEgZGV2aWNlczonLCBlcnIpO1xuICAgICAgc2V0RXJyb3IoJ0ZhaWxlZCB0byBlbnVtZXJhdGUgbWVkaWEgZGV2aWNlcycpO1xuICAgIH1cbiAgfSwgW2lzU3VwcG9ydGVkXSk7IC8vIFJlbW92ZSBzZWxlY3RlZFZpZGVvRGV2aWNlIGFuZCBzZWxlY3RlZEF1ZGlvRGV2aWNlIGZyb20gZGVwZW5kZW5jaWVzXG5cbiAgLy8gQ2hlY2sgZm9yIGdldFVzZXJNZWRpYSBzdXBwb3J0XG4gIHVzZUVmZmVjdCgoKSA9PiB7XG4gICAgaWYgKHR5cGVvZiB3aW5kb3cgIT09ICd1bmRlZmluZWQnICYmIG5hdmlnYXRvci5tZWRpYURldmljZXMgJiYgdHlwZW9mIG5hdmlnYXRvci5tZWRpYURldmljZXMuZ2V0VXNlck1lZGlhID09PSAnZnVuY3Rpb24nKSB7XG4gICAgICBzZXRJc1N1cHBvcnRlZCh0cnVlKTtcbiAgICB9IGVsc2Uge1xuICAgICAgc2V0SXNTdXBwb3J0ZWQoZmFsc2UpO1xuICAgICAgc2V0RXJyb3IoJ01lZGlhIGRldmljZXMgYXJlIG5vdCBzdXBwb3J0ZWQgaW4gdGhpcyBicm93c2VyJyk7XG4gICAgfVxuICB9LCBbXSk7XG5cbiAgLy8gUmVmcmVzaCBkZXZpY2VzIHdoZW4gc3VwcG9ydGVkIHN0YXR1cyBjaGFuZ2VzXG4gIHVzZUVmZmVjdCgoKSA9PiB7XG4gICAgaWYgKGlzU3VwcG9ydGVkKSB7XG4gICAgICByZWZyZXNoRGV2aWNlcygpO1xuICAgIH1cbiAgfSwgW2lzU3VwcG9ydGVkLCByZWZyZXNoRGV2aWNlc10pO1xuXG5cblxuICAvLyBTdGFydCBtZWRpYSBzdHJlYW1cbiAgY29uc3Qgc3RhcnRTdHJlYW0gPSB1c2VDYWxsYmFjayhhc3luYyAob3B0aW9uczogUGFydGlhbDxNZWRpYVN0cmVhbU9wdGlvbnM+ID0ge30pID0+IHtcbiAgICBpZiAoIWlzU3VwcG9ydGVkKSB7XG4gICAgICBzZXRFcnJvcignTWVkaWEgZGV2aWNlcyBhcmUgbm90IHN1cHBvcnRlZCcpO1xuICAgICAgcmV0dXJuO1xuICAgIH1cblxuICAgIGlmIChpc1N0cmVhbWluZykge1xuICAgICAgcmV0dXJuO1xuICAgIH1cblxuICAgIGNvbnN0IGRlZmF1bHRPcHRpb25zOiBNZWRpYVN0cmVhbU9wdGlvbnMgPSB7XG4gICAgICB2aWRlbzoge1xuICAgICAgICBkZXZpY2VJZDogc2VsZWN0ZWRWaWRlb0RldmljZSA/IHsgZXhhY3Q6IHNlbGVjdGVkVmlkZW9EZXZpY2UgfSA6IHVuZGVmaW5lZCxcbiAgICAgICAgd2lkdGg6IHsgaWRlYWw6IDEyODAgfSxcbiAgICAgICAgaGVpZ2h0OiB7IGlkZWFsOiA3MjAgfSxcbiAgICAgICAgZnJhbWVSYXRlOiB7IGlkZWFsOiAzMCB9XG4gICAgICB9LFxuICAgICAgYXVkaW86IHtcbiAgICAgICAgZGV2aWNlSWQ6IHNlbGVjdGVkQXVkaW9EZXZpY2UgPyB7IGV4YWN0OiBzZWxlY3RlZEF1ZGlvRGV2aWNlIH0gOiB1bmRlZmluZWQsXG4gICAgICAgIGVjaG9DYW5jZWxsYXRpb246IHRydWUsXG4gICAgICAgIG5vaXNlU3VwcHJlc3Npb246IHRydWUsXG4gICAgICAgIGF1dG9HYWluQ29udHJvbDogdHJ1ZVxuICAgICAgfVxuICAgIH07XG5cbiAgICBjb25zdCBzdHJlYW1PcHRpb25zID0geyAuLi5kZWZhdWx0T3B0aW9ucywgLi4ub3B0aW9ucyB9O1xuXG4gICAgdHJ5IHtcbiAgICAgIHNldEVycm9yKG51bGwpO1xuICAgICAgY29uc3QgbWVkaWFTdHJlYW0gPSBhd2FpdCB0cnlHZXRVc2VyTWVkaWEoc3RyZWFtT3B0aW9ucyk7XG5cbiAgICAgIHN0cmVhbVJlZi5jdXJyZW50ID0gbWVkaWFTdHJlYW07XG4gICAgICBzZXRTdHJlYW0obWVkaWFTdHJlYW0pO1xuICAgICAgc2V0SXNTdHJlYW1pbmcodHJ1ZSk7XG5cbiAgICAgIC8vIFJlZnJlc2ggZGV2aWNlcyBhZnRlciBnZXR0aW5nIHBlcm1pc3Npb24gKGxhYmVscyB3aWxsIGJlIGF2YWlsYWJsZSlcbiAgICAgIGF3YWl0IHJlZnJlc2hEZXZpY2VzKCk7XG4gICAgfSBjYXRjaCAoZXJyKSB7XG4gICAgICBjb25zdCBlcnJvciA9IGVyciBhcyBFcnJvcjtcbiAgICAgIGNvbnNvbGUuZXJyb3IoJ0ZhaWxlZCB0byBhY2Nlc3MgbWVkaWEgZGV2aWNlczonLCBlcnIpO1xuXG4gICAgICAvLyBQcm92aWRlIG1vcmUgc3BlY2lmaWMgZXJyb3IgbWVzc2FnZXNcbiAgICAgIGxldCBlcnJvck1lc3NhZ2UgPSAnRmFpbGVkIHRvIGFjY2VzcyBtZWRpYSBkZXZpY2VzJztcbiAgICAgIGlmIChlcnJvci5uYW1lID09PSAnTm90QWxsb3dlZEVycm9yJykge1xuICAgICAgICBlcnJvck1lc3NhZ2UgPSAnQ2FtZXJhL21pY3JvcGhvbmUgYWNjZXNzIGRlbmllZC4gUGxlYXNlIGFsbG93IHBlcm1pc3Npb25zIGFuZCB0cnkgYWdhaW4uJztcbiAgICAgIH0gZWxzZSBpZiAoZXJyb3IubmFtZSA9PT0gJ05vdEZvdW5kRXJyb3InKSB7XG4gICAgICAgIGVycm9yTWVzc2FnZSA9ICdObyBjYW1lcmEgb3IgbWljcm9waG9uZSBmb3VuZC4gUGxlYXNlIGNvbm5lY3QgYSBkZXZpY2UgYW5kIHRyeSBhZ2Fpbi4nO1xuICAgICAgfSBlbHNlIGlmIChlcnJvci5uYW1lID09PSAnTm90UmVhZGFibGVFcnJvcicpIHtcbiAgICAgICAgZXJyb3JNZXNzYWdlID0gJ0NhbWVyYS9taWNyb3Bob25lIGlzIGFscmVhZHkgaW4gdXNlIGJ5IGFub3RoZXIgYXBwbGljYXRpb24uJztcbiAgICAgIH0gZWxzZSBpZiAoZXJyb3IubmFtZSA9PT0gJ092ZXJjb25zdHJhaW5lZEVycm9yJykge1xuICAgICAgICBlcnJvck1lc3NhZ2UgPSAnQ2FtZXJhL21pY3JvcGhvbmUgY29uc3RyYWludHMgY2Fubm90IGJlIHNhdGlzZmllZC4gVHJ5IGRpZmZlcmVudCBzZXR0aW5ncy4nO1xuICAgICAgfSBlbHNlIGlmIChlcnJvci5tZXNzYWdlKSB7XG4gICAgICAgIGVycm9yTWVzc2FnZSA9IGBNZWRpYSBhY2Nlc3MgZXJyb3I6ICR7ZXJyb3IubWVzc2FnZX1gO1xuICAgICAgfVxuXG4gICAgICBzZXRFcnJvcihlcnJvck1lc3NhZ2UpO1xuICAgICAgc2V0SXNTdHJlYW1pbmcoZmFsc2UpO1xuICAgIH1cbiAgfSwgW2lzU3VwcG9ydGVkLCBpc1N0cmVhbWluZywgc2VsZWN0ZWRWaWRlb0RldmljZSwgc2VsZWN0ZWRBdWRpb0RldmljZSwgcmVmcmVzaERldmljZXNdKTtcblxuICAvLyBTdG9wIG1lZGlhIHN0cmVhbVxuICBjb25zdCBzdG9wU3RyZWFtID0gdXNlQ2FsbGJhY2soKCkgPT4ge1xuICAgIGlmIChzdHJlYW1SZWYuY3VycmVudCkge1xuICAgICAgc3RyZWFtUmVmLmN1cnJlbnQuZ2V0VHJhY2tzKCkuZm9yRWFjaCh0cmFjayA9PiB7XG4gICAgICAgIHRyYWNrLnN0b3AoKTtcbiAgICAgIH0pO1xuICAgICAgc3RyZWFtUmVmLmN1cnJlbnQgPSBudWxsO1xuICAgICAgc2V0U3RyZWFtKG51bGwpO1xuICAgICAgc2V0SXNTdHJlYW1pbmcoZmFsc2UpO1xuICAgIH1cbiAgfSwgW10pO1xuXG4gIC8vIFN3aXRjaCB2aWRlbyBkZXZpY2VcbiAgY29uc3Qgc3dpdGNoVmlkZW9EZXZpY2UgPSB1c2VDYWxsYmFjayhhc3luYyAoZGV2aWNlSWQ6IHN0cmluZykgPT4ge1xuICAgIHNldFNlbGVjdGVkVmlkZW9EZXZpY2UoZGV2aWNlSWQpO1xuICAgIFxuICAgIGlmIChpc1N0cmVhbWluZykge1xuICAgICAgLy8gU3RvcCBjdXJyZW50IHN0cmVhbSBhbmQgc3RhcnQgd2l0aCBuZXcgZGV2aWNlXG4gICAgICBzdG9wU3RyZWFtKCk7XG4gICAgICBhd2FpdCBzdGFydFN0cmVhbSh7XG4gICAgICAgIHZpZGVvOiB7XG4gICAgICAgICAgZGV2aWNlSWQ6IHsgZXhhY3Q6IGRldmljZUlkIH0sXG4gICAgICAgICAgd2lkdGg6IHsgaWRlYWw6IDEyODAgfSxcbiAgICAgICAgICBoZWlnaHQ6IHsgaWRlYWw6IDcyMCB9LFxuICAgICAgICAgIGZyYW1lUmF0ZTogeyBpZGVhbDogMzAgfVxuICAgICAgICB9LFxuICAgICAgICBhdWRpbzoge1xuICAgICAgICAgIGRldmljZUlkOiBzZWxlY3RlZEF1ZGlvRGV2aWNlID8geyBleGFjdDogc2VsZWN0ZWRBdWRpb0RldmljZSB9IDogdW5kZWZpbmVkLFxuICAgICAgICAgIGVjaG9DYW5jZWxsYXRpb246IHRydWUsXG4gICAgICAgICAgbm9pc2VTdXBwcmVzc2lvbjogdHJ1ZSxcbiAgICAgICAgICBhdXRvR2FpbkNvbnRyb2w6IHRydWVcbiAgICAgICAgfVxuICAgICAgfSk7XG4gICAgfVxuICB9LCBbaXNTdHJlYW1pbmcsIHNlbGVjdGVkQXVkaW9EZXZpY2UsIHN0b3BTdHJlYW0sIHN0YXJ0U3RyZWFtXSk7XG5cbiAgLy8gU3dpdGNoIGF1ZGlvIGRldmljZVxuICBjb25zdCBzd2l0Y2hBdWRpb0RldmljZSA9IHVzZUNhbGxiYWNrKGFzeW5jIChkZXZpY2VJZDogc3RyaW5nKSA9PiB7XG4gICAgc2V0U2VsZWN0ZWRBdWRpb0RldmljZShkZXZpY2VJZCk7XG4gICAgXG4gICAgaWYgKGlzU3RyZWFtaW5nKSB7XG4gICAgICAvLyBTdG9wIGN1cnJlbnQgc3RyZWFtIGFuZCBzdGFydCB3aXRoIG5ldyBkZXZpY2VcbiAgICAgIHN0b3BTdHJlYW0oKTtcbiAgICAgIGF3YWl0IHN0YXJ0U3RyZWFtKHtcbiAgICAgICAgdmlkZW86IHtcbiAgICAgICAgICBkZXZpY2VJZDogc2VsZWN0ZWRWaWRlb0RldmljZSA/IHsgZXhhY3Q6IHNlbGVjdGVkVmlkZW9EZXZpY2UgfSA6IHVuZGVmaW5lZCxcbiAgICAgICAgICB3aWR0aDogeyBpZGVhbDogMTI4MCB9LFxuICAgICAgICAgIGhlaWdodDogeyBpZGVhbDogNzIwIH0sXG4gICAgICAgICAgZnJhbWVSYXRlOiB7IGlkZWFsOiAzMCB9XG4gICAgICAgIH0sXG4gICAgICAgIGF1ZGlvOiB7XG4gICAgICAgICAgZGV2aWNlSWQ6IHsgZXhhY3Q6IGRldmljZUlkIH0sXG4gICAgICAgICAgZWNob0NhbmNlbGxhdGlvbjogdHJ1ZSxcbiAgICAgICAgICBub2lzZVN1cHByZXNzaW9uOiB0cnVlLFxuICAgICAgICAgIGF1dG9HYWluQ29udHJvbDogdHJ1ZVxuICAgICAgICB9XG4gICAgICB9KTtcbiAgICB9XG4gIH0sIFtpc1N0cmVhbWluZywgc2VsZWN0ZWRWaWRlb0RldmljZSwgc3RvcFN0cmVhbSwgc3RhcnRTdHJlYW1dKTtcblxuICAvLyBDbGVhbnVwIG9uIHVubW91bnRcbiAgdXNlRWZmZWN0KCgpID0+IHtcbiAgICByZXR1cm4gKCkgPT4ge1xuICAgICAgc3RvcFN0cmVhbSgpO1xuICAgIH07XG4gIH0sIFtzdG9wU3RyZWFtXSk7XG5cbiAgcmV0dXJuIHtcbiAgICBzdHJlYW0sXG4gICAgaXNTdHJlYW1pbmcsXG4gICAgZXJyb3IsXG4gICAgZGV2aWNlcyxcbiAgICBzZWxlY3RlZFZpZGVvRGV2aWNlLFxuICAgIHNlbGVjdGVkQXVkaW9EZXZpY2UsXG4gICAgc3RhcnRTdHJlYW0sXG4gICAgc3RvcFN0cmVhbSxcbiAgICBzd2l0Y2hWaWRlb0RldmljZSxcbiAgICBzd2l0Y2hBdWRpb0RldmljZSxcbiAgICByZWZyZXNoRGV2aWNlcyxcbiAgICBpc1N1cHBvcnRlZFxuICB9O1xufTtcblxuZXhwb3J0IGRlZmF1bHQgdXNlTWVkaWFTdHJlYW07XG4iXSwibmFtZXMiOlsidXNlU3RhdGUiLCJ1c2VSZWYiLCJ1c2VDYWxsYmFjayIsInVzZUVmZmVjdCIsInVzZU1lZGlhU3RyZWFtIiwic3RyZWFtIiwic2V0U3RyZWFtIiwiaXNTdHJlYW1pbmciLCJzZXRJc1N0cmVhbWluZyIsImVycm9yIiwic2V0RXJyb3IiLCJkZXZpY2VzIiwic2V0RGV2aWNlcyIsInNlbGVjdGVkVmlkZW9EZXZpY2UiLCJzZXRTZWxlY3RlZFZpZGVvRGV2aWNlIiwic2VsZWN0ZWRBdWRpb0RldmljZSIsInNldFNlbGVjdGVkQXVkaW9EZXZpY2UiLCJpc1N1cHBvcnRlZCIsInNldElzU3VwcG9ydGVkIiwic3RyZWFtUmVmIiwiZ2V0VXNlck1lZGlhV2l0aFRpbWVvdXQiLCJjb25zdHJhaW50cyIsInRpbWVvdXRNcyIsIlByb21pc2UiLCJyZXNvbHZlIiwicmVqZWN0IiwidGltZW91dElkIiwic2V0VGltZW91dCIsIkVycm9yIiwibmF2aWdhdG9yIiwibWVkaWFEZXZpY2VzIiwiZ2V0VXNlck1lZGlhIiwidGhlbiIsImNsZWFyVGltZW91dCIsImNhdGNoIiwidHJ5R2V0VXNlck1lZGlhIiwib3B0aW9ucyIsInZpZGVvIiwid2lkdGgiLCJpZGVhbCIsImhlaWdodCIsImZyYW1lUmF0ZSIsImF1ZGlvIiwiZWNob0NhbmNlbGxhdGlvbiIsIm5vaXNlU3VwcHJlc3Npb24iLCJhdXRvR2FpbkNvbnRyb2wiLCJsYXN0RXJyb3IiLCJpIiwibGVuZ3RoIiwiY29uc29sZSIsImxvZyIsIndhcm4iLCJuYW1lIiwicmVmcmVzaERldmljZXMiLCJkZXZpY2VMaXN0IiwiZW51bWVyYXRlRGV2aWNlcyIsIm1hcCIsImRldmljZSIsImRldmljZUlkIiwibGFiZWwiLCJraW5kIiwic2xpY2UiLCJ2aWRlb0RldmljZSIsImZpbmQiLCJkIiwiYXVkaW9EZXZpY2UiLCJlcnIiLCJzdGFydFN0cmVhbSIsImRlZmF1bHRPcHRpb25zIiwiZXhhY3QiLCJ1bmRlZmluZWQiLCJzdHJlYW1PcHRpb25zIiwibWVkaWFTdHJlYW0iLCJjdXJyZW50IiwiZXJyb3JNZXNzYWdlIiwibWVzc2FnZSIsInN0b3BTdHJlYW0iLCJnZXRUcmFja3MiLCJmb3JFYWNoIiwidHJhY2siLCJzdG9wIiwic3dpdGNoVmlkZW9EZXZpY2UiLCJzd2l0Y2hBdWRpb0RldmljZSJdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/hooks/useMediaStream.ts\n"));

/***/ })

});