/**
 * TranslationSettings Component
 * 
 * Component for configuring translation settings including API key,
 * source/target languages, and translation options.
 */

'use client';

import React, { useState, useEffect } from 'react';
import { useCaption, useCaptionActions } from '@/contexts/CaptionContext';
import useTranslation from '@/hooks/useTranslation';
import { SUPPORTED_TRANSLATION_LANGUAGES } from '@/utils/translation';

interface TranslationSettingsProps {
  className?: string;
}

const TranslationSettings: React.FC<TranslationSettingsProps> = ({
  className = ''
}) => {
  const { state } = useCaption();
  const actions = useCaptionActions();
  const translation = useTranslation();

  const [apiKey, setApiKey] = useState('');
  const [showApiKey, setShowApiKey] = useState(false);
  const [testText, setTestText] = useState('Hello, how are you today?');
  const [testResult, setTestResult] = useState<string | null>(null);
  const [isTestingTranslation, setIsTestingTranslation] = useState(false);

  // Load API key from localStorage on mount
  useEffect(() => {
    if (typeof window !== 'undefined') {
      const savedApiKey = localStorage.getItem('caption-ninja-google-api-key');
      if (savedApiKey) {
        setApiKey(savedApiKey);
      }
    }
  }, []);

  // Save API key to localStorage
  const handleSaveApiKey = () => {
    if (typeof window !== 'undefined') {
      if (apiKey.trim()) {
        localStorage.setItem('caption-ninja-google-api-key', apiKey.trim());
        alert('API key saved successfully!');
      } else {
        localStorage.removeItem('caption-ninja-google-api-key');
        alert('API key removed.');
      }
    }
  };

  // Test translation
  const handleTestTranslation = async () => {
    if (!testText.trim()) {
      alert('Please enter some text to test translation.');
      return;
    }

    if (!apiKey.trim()) {
      alert('Please enter your Google Cloud Translation API key first.');
      return;
    }

    setIsTestingTranslation(true);
    setTestResult(null);

    try {
      const result = await translation.translateText(testText, {
        sourceLanguage: state.settings.sourceLanguage,
        targetLanguage: state.settings.targetLanguage,
        apiKey: apiKey.trim()
      });

      if (result) {
        setTestResult(result.translatedText);
      } else {
        setTestResult('Translation failed - no result returned');
      }
    } catch (error) {
      console.error('Translation test failed:', error);
      setTestResult(`Translation failed: ${error instanceof Error ? error.message : 'Unknown error'}`);
    } finally {
      setIsTestingTranslation(false);
    }
  };

  // Clear translation cache
  const handleClearCache = () => {
    translation.clearCache();
    alert(`Translation cache cleared! (${translation.getCacheSize()} entries removed)`);
  };

  const stats = translation.getTranslationStats();

  return (
    <div className={`space-y-6 ${className}`}>
      {/* Translation Toggle */}
      <div className="flex items-center justify-between">
        <div>
          <h3 className="text-lg font-semibold text-gray-800">Translation</h3>
          <p className="text-sm text-gray-600">
            Translate speech recognition results in real-time
          </p>
        </div>
        <label className="relative inline-flex items-center cursor-pointer">
          <input
            type="checkbox"
            checked={state.settings.translationEnabled}
            onChange={(e) => actions.updateSettings({ translationEnabled: e.target.checked })}
            className="sr-only peer"
          />
          <div className="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-blue-600"></div>
        </label>
      </div>

      {/* API Key Configuration */}
      <div className="space-y-3">
        <label className="block text-sm font-medium text-gray-700">
          Google Cloud Translation API Key
        </label>
        <div className="flex gap-2">
          <div className="flex-1 relative">
            <input
              type={showApiKey ? 'text' : 'password'}
              value={apiKey}
              onChange={(e) => setApiKey(e.target.value)}
              placeholder="Enter your Google Cloud Translation API key"
              className="w-full px-3 py-2 border border-gray-300 rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            />
            <button
              type="button"
              onClick={() => setShowApiKey(!showApiKey)}
              className="absolute right-2 top-1/2 transform -translate-y-1/2 text-gray-500 hover:text-gray-700"
            >
              {showApiKey ? '🙈' : '👁️'}
            </button>
          </div>
          <button
            onClick={handleSaveApiKey}
            className="px-4 py-2 bg-blue-500 text-white rounded-md hover:bg-blue-600 transition-colors text-sm"
          >
            Save
          </button>
        </div>
        <p className="text-xs text-gray-500">
          Get your API key from the{' '}
          <a
            href="https://console.cloud.google.com/apis/credentials"
            target="_blank"
            rel="noopener noreferrer"
            className="text-blue-500 hover:underline"
          >
            Google Cloud Console
          </a>
        </p>
      </div>

      {/* Language Selection */}
      <div className="grid grid-cols-2 gap-4">
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            Source Language
          </label>
          <select
            value={state.settings.sourceLanguage}
            onChange={(e) => actions.updateSettings({ sourceLanguage: e.target.value })}
            className="w-full px-3 py-2 border border-gray-300 rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
          >
            <option value="auto">🌐 Auto-detect</option>
            {SUPPORTED_TRANSLATION_LANGUAGES.map((lang) => (
              <option key={lang.code} value={lang.code}>
                {lang.flag} {lang.name}
              </option>
            ))}
          </select>
        </div>

        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            Target Language
          </label>
          <select
            value={state.settings.targetLanguage}
            onChange={(e) => actions.updateSettings({ targetLanguage: e.target.value })}
            className="w-full px-3 py-2 border border-gray-300 rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
          >
            {SUPPORTED_TRANSLATION_LANGUAGES.map((lang) => (
              <option key={lang.code} value={lang.code}>
                {lang.flag} {lang.name}
              </option>
            ))}
          </select>
        </div>
      </div>

      {/* Translation Test */}
      <div className="space-y-3">
        <label className="block text-sm font-medium text-gray-700">
          Test Translation
        </label>
        <div className="space-y-2">
          <input
            type="text"
            value={testText}
            onChange={(e) => setTestText(e.target.value)}
            placeholder="Enter text to test translation"
            className="w-full px-3 py-2 border border-gray-300 rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
          />
          <button
            onClick={handleTestTranslation}
            disabled={isTestingTranslation || !apiKey.trim()}
            className="w-full px-4 py-2 bg-green-500 text-white rounded-md hover:bg-green-600 disabled:bg-gray-400 disabled:cursor-not-allowed transition-colors text-sm"
          >
            {isTestingTranslation ? 'Testing...' : 'Test Translation'}
          </button>
          {testResult && (
            <div className="p-3 bg-gray-50 border border-gray-200 rounded-md">
              <div className="text-xs text-gray-500 mb-1">Translation Result:</div>
              <div className="text-sm text-gray-800">{testResult}</div>
            </div>
          )}
        </div>
      </div>

      {/* Translation Statistics */}
      <div className="space-y-3">
        <h4 className="text-sm font-medium text-gray-700">Translation Statistics</h4>
        <div className="grid grid-cols-2 gap-4 text-sm">
          <div className="bg-gray-50 p-3 rounded">
            <div className="font-medium text-gray-800">Cache Size</div>
            <div className="text-gray-600">{stats.cacheSize} translations</div>
          </div>
          <div className="bg-gray-50 p-3 rounded">
            <div className="font-medium text-gray-800">Total Characters</div>
            <div className="text-gray-600">{stats.totalCharacters.toLocaleString()}</div>
          </div>
          <div className="bg-gray-50 p-3 rounded">
            <div className="font-medium text-gray-800">Avg. Processing</div>
            <div className="text-gray-600">{Math.round(stats.averageProcessingTime)}ms</div>
          </div>
          <div className="bg-gray-50 p-3 rounded">
            <div className="font-medium text-gray-800">Status</div>
            <div className={`${translation.isTranslating ? 'text-blue-600' : 'text-green-600'}`}>
              {translation.isTranslating ? 'Translating...' : 'Ready'}
            </div>
          </div>
        </div>
        
        <button
          onClick={handleClearCache}
          className="w-full px-4 py-2 bg-gray-500 text-white rounded-md hover:bg-gray-600 transition-colors text-sm"
        >
          Clear Translation Cache
        </button>
      </div>

      {/* Error Display */}
      {translation.error && (
        <div className="p-3 bg-red-50 border border-red-200 rounded-md">
          <div className="text-sm font-medium text-red-800">Translation Error</div>
          <div className="text-sm text-red-700 mt-1">{translation.error}</div>
        </div>
      )}
    </div>
  );
};

export default TranslationSettings;
