[{"C:\\Users\\<USER>\\OneDrive - UGSM-Monarch Business School GmbH\\Documents\\captionninja\\caption-ninja-nextjs\\src\\app\\layout.tsx": "1", "C:\\Users\\<USER>\\OneDrive - UGSM-Monarch Business School GmbH\\Documents\\captionninja\\caption-ninja-nextjs\\src\\app\\page.tsx": "2", "C:\\Users\\<USER>\\OneDrive - UGSM-Monarch Business School GmbH\\Documents\\captionninja\\caption-ninja-nextjs\\src\\components\\ExportControls\\ExportButtons.tsx": "3", "C:\\Users\\<USER>\\OneDrive - UGSM-Monarch Business School GmbH\\Documents\\captionninja\\caption-ninja-nextjs\\src\\components\\ExportControls\\FormatSelector.tsx": "4", "C:\\Users\\<USER>\\OneDrive - UGSM-Monarch Business School GmbH\\Documents\\captionninja\\caption-ninja-nextjs\\src\\components\\SpeechRecognition\\LanguageSelector.tsx": "5", "C:\\Users\\<USER>\\OneDrive - UGSM-Monarch Business School GmbH\\Documents\\captionninja\\caption-ninja-nextjs\\src\\components\\SpeechRecognition\\SpeechController.tsx": "6", "C:\\Users\\<USER>\\OneDrive - UGSM-Monarch Business School GmbH\\Documents\\captionninja\\caption-ninja-nextjs\\src\\components\\TranscriptPanel\\TranscriptDisplay.tsx": "7", "C:\\Users\\<USER>\\OneDrive - UGSM-Monarch Business School GmbH\\Documents\\captionninja\\caption-ninja-nextjs\\src\\components\\TranscriptPanel\\TranscriptHistory.tsx": "8", "C:\\Users\\<USER>\\OneDrive - UGSM-Monarch Business School GmbH\\Documents\\captionninja\\caption-ninja-nextjs\\src\\components\\Translation\\LanguagePairSelector.tsx": "9", "C:\\Users\\<USER>\\OneDrive - UGSM-Monarch Business School GmbH\\Documents\\captionninja\\caption-ninja-nextjs\\src\\components\\Translation\\TranslationControls.tsx": "10", "C:\\Users\\<USER>\\OneDrive - UGSM-Monarch Business School GmbH\\Documents\\captionninja\\caption-ninja-nextjs\\src\\components\\TTS\\TTSControls.tsx": "11", "C:\\Users\\<USER>\\OneDrive - UGSM-Monarch Business School GmbH\\Documents\\captionninja\\caption-ninja-nextjs\\src\\components\\TTS\\VoiceSelector.tsx": "12", "C:\\Users\\<USER>\\OneDrive - UGSM-Monarch Business School GmbH\\Documents\\captionninja\\caption-ninja-nextjs\\src\\components\\VideoPlayer\\CaptionOverlay.tsx": "13", "C:\\Users\\<USER>\\OneDrive - UGSM-Monarch Business School GmbH\\Documents\\captionninja\\caption-ninja-nextjs\\src\\components\\VideoPlayer\\VideoPlayerWithCaptions.tsx": "14", "C:\\Users\\<USER>\\OneDrive - UGSM-Monarch Business School GmbH\\Documents\\captionninja\\caption-ninja-nextjs\\src\\contexts\\CaptionContext.tsx": "15", "C:\\Users\\<USER>\\OneDrive - UGSM-Monarch Business School GmbH\\Documents\\captionninja\\caption-ninja-nextjs\\src\\hooks\\useExport.ts": "16", "C:\\Users\\<USER>\\OneDrive - UGSM-Monarch Business School GmbH\\Documents\\captionninja\\caption-ninja-nextjs\\src\\hooks\\useMediaStream.ts": "17", "C:\\Users\\<USER>\\OneDrive - UGSM-Monarch Business School GmbH\\Documents\\captionninja\\caption-ninja-nextjs\\src\\hooks\\useSpeechRecognition.ts": "18", "C:\\Users\\<USER>\\OneDrive - UGSM-Monarch Business School GmbH\\Documents\\captionninja\\caption-ninja-nextjs\\src\\hooks\\useTranslation.ts": "19", "C:\\Users\\<USER>\\OneDrive - UGSM-Monarch Business School GmbH\\Documents\\captionninja\\caption-ninja-nextjs\\src\\hooks\\useTTS.ts": "20", "C:\\Users\\<USER>\\OneDrive - UGSM-Monarch Business School GmbH\\Documents\\captionninja\\caption-ninja-nextjs\\src\\types\\caption.ts": "21", "C:\\Users\\<USER>\\OneDrive - UGSM-Monarch Business School GmbH\\Documents\\captionninja\\caption-ninja-nextjs\\src\\types\\speech.ts": "22", "C:\\Users\\<USER>\\OneDrive - UGSM-Monarch Business School GmbH\\Documents\\captionninja\\caption-ninja-nextjs\\src\\types\\translation.ts": "23", "C:\\Users\\<USER>\\OneDrive - UGSM-Monarch Business School GmbH\\Documents\\captionninja\\caption-ninja-nextjs\\src\\utils\\constants.ts": "24", "C:\\Users\\<USER>\\OneDrive - UGSM-Monarch Business School GmbH\\Documents\\captionninja\\caption-ninja-nextjs\\src\\utils\\export.ts": "25", "C:\\Users\\<USER>\\OneDrive - UGSM-Monarch Business School GmbH\\Documents\\captionninja\\caption-ninja-nextjs\\src\\utils\\speechRecognition.ts": "26", "C:\\Users\\<USER>\\OneDrive - UGSM-Monarch Business School GmbH\\Documents\\captionninja\\caption-ninja-nextjs\\src\\utils\\textProcessing.ts": "27", "C:\\Users\\<USER>\\OneDrive - UGSM-Monarch Business School GmbH\\Documents\\captionninja\\caption-ninja-nextjs\\src\\utils\\translation.ts": "28", "C:\\Users\\<USER>\\OneDrive - UGSM-Monarch Business School GmbH\\Documents\\captionninja\\caption-ninja-nextjs\\src\\components\\VideoPlayer\\DeviceSelector.tsx": "29", "C:\\Users\\<USER>\\OneDrive - UGSM-Monarch Business School GmbH\\Documents\\captionninja\\caption-ninja-nextjs\\src\\app\\api\\translate\\route.ts": "30", "C:\\Users\\<USER>\\OneDrive - UGSM-Monarch Business School GmbH\\Documents\\captionninja\\caption-ninja-nextjs\\src\\components\\Translation\\TranslationSettings.tsx": "31", "C:\\Users\\<USER>\\OneDrive - UGSM-Monarch Business School GmbH\\Documents\\captionninja\\caption-ninja-nextjs\\src\\hooks\\useAutoTranslation.ts": "32"}, {"size": 927, "mtime": 1757163784642, "results": "33", "hashOfConfig": "34"}, {"size": 8551, "mtime": 1757166337827, "results": "35", "hashOfConfig": "34"}, {"size": 3088, "mtime": 1757162336666, "results": "36", "hashOfConfig": "34"}, {"size": 6049, "mtime": 1757162360577, "results": "37", "hashOfConfig": "34"}, {"size": 2448, "mtime": 1757162268094, "results": "38", "hashOfConfig": "34"}, {"size": 9546, "mtime": 1757166411731, "results": "39", "hashOfConfig": "34"}, {"size": 4069, "mtime": 1757162291471, "results": "40", "hashOfConfig": "34"}, {"size": 5315, "mtime": 1757162317457, "results": "41", "hashOfConfig": "34"}, {"size": 6387, "mtime": 1757162417376, "results": "42", "hashOfConfig": "34"}, {"size": 5183, "mtime": 1757162385514, "results": "43", "hashOfConfig": "34"}, {"size": 6527, "mtime": 1757162443033, "results": "44", "hashOfConfig": "34"}, {"size": 7906, "mtime": 1757162485203, "results": "45", "hashOfConfig": "34"}, {"size": 3861, "mtime": 1757165133025, "results": "46", "hashOfConfig": "34"}, {"size": 9074, "mtime": 1757165322500, "results": "47", "hashOfConfig": "34"}, {"size": 12917, "mtime": 1757167104315, "results": "48", "hashOfConfig": "34"}, {"size": 7943, "mtime": 1757162593920, "results": "49", "hashOfConfig": "34"}, {"size": 7545, "mtime": 1757167142262, "results": "50", "hashOfConfig": "34"}, {"size": 9176, "mtime": 1757167276885, "results": "51", "hashOfConfig": "34"}, {"size": 8845, "mtime": 1757166165830, "results": "52", "hashOfConfig": "34"}, {"size": 5219, "mtime": 1757163103288, "results": "53", "hashOfConfig": "34"}, {"size": 6736, "mtime": 1757162905170, "results": "54", "hashOfConfig": "34"}, {"size": 6186, "mtime": 1757163115231, "results": "55", "hashOfConfig": "34"}, {"size": 7190, "mtime": 1757162979055, "results": "56", "hashOfConfig": "34"}, {"size": 5343, "mtime": 1757162871462, "results": "57", "hashOfConfig": "34"}, {"size": 9880, "mtime": 1757162796632, "results": "58", "hashOfConfig": "34"}, {"size": 7265, "mtime": 1757163128551, "results": "59", "hashOfConfig": "34"}, {"size": 8654, "mtime": 1757162837508, "results": "60", "hashOfConfig": "34"}, {"size": 14718, "mtime": 1757166256388, "results": "61", "hashOfConfig": "34"}, {"size": 5252, "mtime": 1757167160031, "results": "62", "hashOfConfig": "34"}, {"size": 4524, "mtime": 1757166012705, "results": "63", "hashOfConfig": "34"}, {"size": 10390, "mtime": 1757166209752, "results": "64", "hashOfConfig": "34"}, {"size": 5726, "mtime": 1757166371451, "results": "65", "hashOfConfig": "34"}, {"filePath": "66", "messages": "67", "suppressedMessages": "68", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "kg33tf", {"filePath": "69", "messages": "70", "suppressedMessages": "71", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "72", "messages": "73", "suppressedMessages": "74", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "75", "messages": "76", "suppressedMessages": "77", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "78", "messages": "79", "suppressedMessages": "80", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "81", "messages": "82", "suppressedMessages": "83", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "84", "messages": "85", "suppressedMessages": "86", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "87", "messages": "88", "suppressedMessages": "89", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "90", "messages": "91", "suppressedMessages": "92", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "93", "messages": "94", "suppressedMessages": "95", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "96", "messages": "97", "suppressedMessages": "98", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "99", "messages": "100", "suppressedMessages": "101", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "102", "messages": "103", "suppressedMessages": "104", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "105", "messages": "106", "suppressedMessages": "107", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "108", "messages": "109", "suppressedMessages": "110", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "111", "messages": "112", "suppressedMessages": "113", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "114", "messages": "115", "suppressedMessages": "116", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "117", "messages": "118", "suppressedMessages": "119", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 8, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "120", "messages": "121", "suppressedMessages": "122", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "123", "messages": "124", "suppressedMessages": "125", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "126", "messages": "127", "suppressedMessages": "128", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "129", "messages": "130", "suppressedMessages": "131", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "132", "messages": "133", "suppressedMessages": "134", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "135", "messages": "136", "suppressedMessages": "137", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "138", "messages": "139", "suppressedMessages": "140", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "141", "messages": "142", "suppressedMessages": "143", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "144", "messages": "145", "suppressedMessages": "146", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "147", "messages": "148", "suppressedMessages": "149", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "150", "messages": "151", "suppressedMessages": "152", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "153", "messages": "154", "suppressedMessages": "155", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "156", "messages": "157", "suppressedMessages": "158", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "159", "messages": "160", "suppressedMessages": "161", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "C:\\Users\\<USER>\\OneDrive - UGSM-Monarch Business School GmbH\\Documents\\captionninja\\caption-ninja-nextjs\\src\\app\\layout.tsx", [], [], "C:\\Users\\<USER>\\OneDrive - UGSM-Monarch Business School GmbH\\Documents\\captionninja\\caption-ninja-nextjs\\src\\app\\page.tsx", [], [], "C:\\Users\\<USER>\\OneDrive - UGSM-Monarch Business School GmbH\\Documents\\captionninja\\caption-ninja-nextjs\\src\\components\\ExportControls\\ExportButtons.tsx", [], [], "C:\\Users\\<USER>\\OneDrive - UGSM-Monarch Business School GmbH\\Documents\\captionninja\\caption-ninja-nextjs\\src\\components\\ExportControls\\FormatSelector.tsx", ["162"], [], "C:\\Users\\<USER>\\OneDrive - UGSM-Monarch Business School GmbH\\Documents\\captionninja\\caption-ninja-nextjs\\src\\components\\SpeechRecognition\\LanguageSelector.tsx", [], [], "C:\\Users\\<USER>\\OneDrive - UGSM-Monarch Business School GmbH\\Documents\\captionninja\\caption-ninja-nextjs\\src\\components\\SpeechRecognition\\SpeechController.tsx", ["163"], [], "C:\\Users\\<USER>\\OneDrive - UGSM-Monarch Business School GmbH\\Documents\\captionninja\\caption-ninja-nextjs\\src\\components\\TranscriptPanel\\TranscriptDisplay.tsx", [], [], "C:\\Users\\<USER>\\OneDrive - UGSM-Monarch Business School GmbH\\Documents\\captionninja\\caption-ninja-nextjs\\src\\components\\TranscriptPanel\\TranscriptHistory.tsx", ["164"], [], "C:\\Users\\<USER>\\OneDrive - UGSM-Monarch Business School GmbH\\Documents\\captionninja\\caption-ninja-nextjs\\src\\components\\Translation\\LanguagePairSelector.tsx", [], [], "C:\\Users\\<USER>\\OneDrive - UGSM-Monarch Business School GmbH\\Documents\\captionninja\\caption-ninja-nextjs\\src\\components\\Translation\\TranslationControls.tsx", [], [], "C:\\Users\\<USER>\\OneDrive - UGSM-Monarch Business School GmbH\\Documents\\captionninja\\caption-ninja-nextjs\\src\\components\\TTS\\TTSControls.tsx", [], [], "C:\\Users\\<USER>\\OneDrive - UGSM-Monarch Business School GmbH\\Documents\\captionninja\\caption-ninja-nextjs\\src\\components\\TTS\\VoiceSelector.tsx", [], [], "C:\\Users\\<USER>\\OneDrive - UGSM-Monarch Business School GmbH\\Documents\\captionninja\\caption-ninja-nextjs\\src\\components\\VideoPlayer\\CaptionOverlay.tsx", [], [], "C:\\Users\\<USER>\\OneDrive - UGSM-Monarch Business School GmbH\\Documents\\captionninja\\caption-ninja-nextjs\\src\\components\\VideoPlayer\\VideoPlayerWithCaptions.tsx", [], [], "C:\\Users\\<USER>\\OneDrive - UGSM-Monarch Business School GmbH\\Documents\\captionninja\\caption-ninja-nextjs\\src\\contexts\\CaptionContext.tsx", [], [], "C:\\Users\\<USER>\\OneDrive - UGSM-Monarch Business School GmbH\\Documents\\captionninja\\caption-ninja-nextjs\\src\\hooks\\useExport.ts", [], [], "C:\\Users\\<USER>\\OneDrive - UGSM-Monarch Business School GmbH\\Documents\\captionninja\\caption-ninja-nextjs\\src\\hooks\\useMediaStream.ts", ["165"], [], "C:\\Users\\<USER>\\OneDrive - UGSM-Monarch Business School GmbH\\Documents\\captionninja\\caption-ninja-nextjs\\src\\hooks\\useSpeechRecognition.ts", ["166", "167", "168", "169", "170", "171", "172", "173"], [], "C:\\Users\\<USER>\\OneDrive - UGSM-Monarch Business School GmbH\\Documents\\captionninja\\caption-ninja-nextjs\\src\\hooks\\useTranslation.ts", ["174", "175"], [], "C:\\Users\\<USER>\\OneDrive - UGSM-Monarch Business School GmbH\\Documents\\captionninja\\caption-ninja-nextjs\\src\\hooks\\useTTS.ts", ["176", "177"], [], "C:\\Users\\<USER>\\OneDrive - UGSM-Monarch Business School GmbH\\Documents\\captionninja\\caption-ninja-nextjs\\src\\types\\caption.ts", ["178", "179"], [], "C:\\Users\\<USER>\\OneDrive - UGSM-Monarch Business School GmbH\\Documents\\captionninja\\caption-ninja-nextjs\\src\\types\\speech.ts", ["180"], [], "C:\\Users\\<USER>\\OneDrive - UGSM-Monarch Business School GmbH\\Documents\\captionninja\\caption-ninja-nextjs\\src\\types\\translation.ts", ["181"], [], "C:\\Users\\<USER>\\OneDrive - UGSM-Monarch Business School GmbH\\Documents\\captionninja\\caption-ninja-nextjs\\src\\utils\\constants.ts", [], [], "C:\\Users\\<USER>\\OneDrive - UGSM-Monarch Business School GmbH\\Documents\\captionninja\\caption-ninja-nextjs\\src\\utils\\export.ts", [], [], "C:\\Users\\<USER>\\OneDrive - UGSM-Monarch Business School GmbH\\Documents\\captionninja\\caption-ninja-nextjs\\src\\utils\\speechRecognition.ts", ["182", "183"], [], "C:\\Users\\<USER>\\OneDrive - UGSM-Monarch Business School GmbH\\Documents\\captionninja\\caption-ninja-nextjs\\src\\utils\\textProcessing.ts", [], [], "C:\\Users\\<USER>\\OneDrive - UGSM-Monarch Business School GmbH\\Documents\\captionninja\\caption-ninja-nextjs\\src\\utils\\translation.ts", ["184"], [], "C:\\Users\\<USER>\\OneDrive - UGSM-Monarch Business School GmbH\\Documents\\captionninja\\caption-ninja-nextjs\\src\\components\\VideoPlayer\\DeviceSelector.tsx", ["185"], [], "C:\\Users\\<USER>\\OneDrive - UGSM-Monarch Business School GmbH\\Documents\\captionninja\\caption-ninja-nextjs\\src\\app\\api\\translate\\route.ts", ["186"], [], "C:\\Users\\<USER>\\OneDrive - UGSM-Monarch Business School GmbH\\Documents\\captionninja\\caption-ninja-nextjs\\src\\components\\Translation\\TranslationSettings.tsx", [], [], "C:\\Users\\<USER>\\OneDrive - UGSM-Monarch Business School GmbH\\Documents\\captionninja\\caption-ninja-nextjs\\src\\hooks\\useAutoTranslation.ts", [], [], {"ruleId": "187", "severity": 1, "message": "188", "line": 45, "column": 64, "nodeType": "189", "messageId": "190", "endLine": 45, "endColumn": 67, "suggestions": "191"}, {"ruleId": "192", "severity": 1, "message": "193", "line": 27, "column": 9, "nodeType": null, "messageId": "194", "endLine": 27, "endColumn": 24}, {"ruleId": "192", "severity": 1, "message": "195", "line": 52, "column": 9, "nodeType": null, "messageId": "194", "endLine": 52, "endColumn": 23}, {"ruleId": "196", "severity": 1, "message": "197", "line": 79, "column": 6, "nodeType": "198", "endLine": 79, "endColumn": 19, "suggestions": "199"}, {"ruleId": "187", "severity": 1, "message": "188", "line": 35, "column": 24, "nodeType": "189", "messageId": "190", "endLine": 35, "endColumn": 27, "suggestions": "200"}, {"ruleId": "187", "severity": 1, "message": "188", "line": 36, "column": 30, "nodeType": "189", "messageId": "190", "endLine": 36, "endColumn": 33, "suggestions": "201"}, {"ruleId": "187", "severity": 1, "message": "188", "line": 57, "column": 33, "nodeType": "189", "messageId": "190", "endLine": 57, "endColumn": 36, "suggestions": "202"}, {"ruleId": "196", "severity": 1, "message": "203", "line": 69, "column": 6, "nodeType": "198", "endLine": 69, "endColumn": 93, "suggestions": "204"}, {"ruleId": "187", "severity": 1, "message": "188", "line": 126, "column": 35, "nodeType": "189", "messageId": "190", "endLine": 126, "endColumn": 38, "suggestions": "205"}, {"ruleId": "187", "severity": 1, "message": "188", "line": 158, "column": 36, "nodeType": "189", "messageId": "190", "endLine": 158, "endColumn": 39, "suggestions": "206"}, {"ruleId": "192", "severity": 1, "message": "207", "line": 233, "column": 22, "nodeType": null, "messageId": "194", "endLine": 233, "endColumn": 30}, {"ruleId": "192", "severity": 1, "message": "208", "line": 237, "column": 18, "nodeType": null, "messageId": "194", "endLine": 237, "endColumn": 25}, {"ruleId": "187", "severity": 1, "message": "188", "line": 73, "column": 65, "nodeType": "189", "messageId": "190", "endLine": 73, "endColumn": 68, "suggestions": "209"}, {"ruleId": "196", "severity": 1, "message": "210", "line": 131, "column": 6, "nodeType": "198", "endLine": 131, "endColumn": 8, "suggestions": "211"}, {"ruleId": "196", "severity": 1, "message": "212", "line": 56, "column": 6, "nodeType": "198", "endLine": 56, "endColumn": 8, "suggestions": "213"}, {"ruleId": "192", "severity": 1, "message": "214", "line": 167, "column": 14, "nodeType": null, "messageId": "194", "endLine": 167, "endColumn": 17}, {"ruleId": "187", "severity": 1, "message": "188", "line": 170, "column": 10, "nodeType": "189", "messageId": "190", "endLine": 170, "endColumn": 13, "suggestions": "215"}, {"ruleId": "187", "severity": 1, "message": "188", "line": 190, "column": 13, "nodeType": "189", "messageId": "190", "endLine": 190, "endColumn": 16, "suggestions": "216"}, {"ruleId": "187", "severity": 1, "message": "188", "line": 13, "column": 14, "nodeType": "189", "messageId": "190", "endLine": 13, "endColumn": 17, "suggestions": "217"}, {"ruleId": "187", "severity": 1, "message": "188", "line": 59, "column": 13, "nodeType": "189", "messageId": "190", "endLine": 59, "endColumn": 16, "suggestions": "218"}, {"ruleId": "187", "severity": 1, "message": "188", "line": 45, "column": 41, "nodeType": "189", "messageId": "190", "endLine": 45, "endColumn": 44, "suggestions": "219"}, {"ruleId": "192", "severity": 1, "message": "220", "line": 171, "column": 12, "nodeType": null, "messageId": "194", "endLine": 171, "endColumn": 17}, {"ruleId": "187", "severity": 1, "message": "188", "line": 320, "column": 46, "nodeType": "189", "messageId": "190", "endLine": 320, "endColumn": 49, "suggestions": "221"}, {"ruleId": "196", "severity": 1, "message": "222", "line": 33, "column": 6, "nodeType": "198", "endLine": 33, "endColumn": 31, "suggestions": "223"}, {"ruleId": "192", "severity": 1, "message": "224", "line": 107, "column": 16, "nodeType": null, "messageId": "194", "endLine": 107, "endColumn": 17}, "@typescript-eslint/no-explicit-any", "Unexpected any. Specify a different type.", "TSAnyKeyword", "unexpectedAny", ["225", "226"], "@typescript-eslint/no-unused-vars", "'autoTranslation' is assigned a value but never used.", "unusedVar", "'formatDuration' is assigned a value but never used.", "react-hooks/exhaustive-deps", "React Hook useCallback has missing dependencies: 'selectedAudioDevice' and 'selectedVideoDevice'. Either include them or remove the dependency array.", "ArrayExpression", ["227"], ["228", "229"], ["230", "231"], ["232", "233"], "React Hook useEffect has missing dependencies: 'defaultOptions' and 'options'. Either include them or remove the dependency array.", ["234"], ["235", "236"], ["237", "238"], "'retryErr' is defined but never used.", "'stopErr' is defined but never used.", ["239", "240"], "React Hook useCallback has a missing dependency: 'performTranslation'. Either include it or remove the dependency array.", ["241"], "React Hook useEffect has a missing dependency: 'loadVoices'. Either include it or remove the dependency array.", ["242"], "'err' is defined but never used.", ["243", "244"], ["245", "246"], ["247", "248"], ["249", "250"], ["251", "252"], "'error' is defined but never used.", ["253", "254"], "React Hook useEffect has a missing dependency: 'mediaStream'. Either include it or remove the dependency array.", ["255"], "'e' is defined but never used.", {"messageId": "256", "fix": "257", "desc": "258"}, {"messageId": "259", "fix": "260", "desc": "261"}, {"desc": "262", "fix": "263"}, {"messageId": "256", "fix": "264", "desc": "258"}, {"messageId": "259", "fix": "265", "desc": "261"}, {"messageId": "256", "fix": "266", "desc": "258"}, {"messageId": "259", "fix": "267", "desc": "261"}, {"messageId": "256", "fix": "268", "desc": "258"}, {"messageId": "259", "fix": "269", "desc": "261"}, {"desc": "270", "fix": "271"}, {"messageId": "256", "fix": "272", "desc": "258"}, {"messageId": "259", "fix": "273", "desc": "261"}, {"messageId": "256", "fix": "274", "desc": "258"}, {"messageId": "259", "fix": "275", "desc": "261"}, {"messageId": "256", "fix": "276", "desc": "258"}, {"messageId": "259", "fix": "277", "desc": "261"}, {"desc": "278", "fix": "279"}, {"desc": "280", "fix": "281"}, {"messageId": "256", "fix": "282", "desc": "258"}, {"messageId": "259", "fix": "283", "desc": "261"}, {"messageId": "256", "fix": "284", "desc": "258"}, {"messageId": "259", "fix": "285", "desc": "261"}, {"messageId": "256", "fix": "286", "desc": "258"}, {"messageId": "259", "fix": "287", "desc": "261"}, {"messageId": "256", "fix": "288", "desc": "258"}, {"messageId": "259", "fix": "289", "desc": "261"}, {"messageId": "256", "fix": "290", "desc": "258"}, {"messageId": "259", "fix": "291", "desc": "261"}, {"messageId": "256", "fix": "292", "desc": "258"}, {"messageId": "259", "fix": "293", "desc": "261"}, {"desc": "294", "fix": "295"}, "suggestUnknown", {"range": "296", "text": "297"}, "Use `unknown` instead, this will force you to explicitly, and safely assert the type is correct.", "suggestNever", {"range": "298", "text": "299"}, "Use `never` instead, this is useful when instantiating generic type parameters that you don't need to know the type of.", "Update the dependencies array to be: [isSupported, selectedAudioDevice, selectedVideoDevice]", {"range": "300", "text": "301"}, {"range": "302", "text": "297"}, {"range": "303", "text": "299"}, {"range": "304", "text": "297"}, {"range": "305", "text": "299"}, {"range": "306", "text": "297"}, {"range": "307", "text": "299"}, "Update the dependencies array to be: [options.language, options.continuous, options.interimResults, options.maxAlternatives, defaultOptions, options]", {"range": "308", "text": "309"}, {"range": "310", "text": "297"}, {"range": "311", "text": "299"}, {"range": "312", "text": "297"}, {"range": "313", "text": "299"}, {"range": "314", "text": "297"}, {"range": "315", "text": "299"}, "Update the dependencies array to be: [performTranslation]", {"range": "316", "text": "317"}, "Update the dependencies array to be: [loadVoices]", {"range": "318", "text": "319"}, {"range": "320", "text": "297"}, {"range": "321", "text": "299"}, {"range": "322", "text": "297"}, {"range": "323", "text": "299"}, {"range": "324", "text": "297"}, {"range": "325", "text": "299"}, {"range": "326", "text": "297"}, {"range": "327", "text": "299"}, {"range": "328", "text": "297"}, {"range": "329", "text": "299"}, {"range": "330", "text": "297"}, {"range": "331", "text": "299"}, "Update the dependencies array to be: [mediaStream, mediaStream.isSupported]", {"range": "332", "text": "333"}, [1317, 1320], "unknown", [1317, 1320], "never", [2649, 2662], "[isSupported, selectedAudioDevice, selectedVideoDevice]", [854, 857], [854, 857], [888, 891], [888, 891], [1548, 1551], [1548, 1551], [1994, 2081], "[options.language, options.continuous, options.interimResults, options.maxAlternatives, defaultOptions, options]", [4042, 4045], [4042, 4045], [5181, 5184], [5181, 5184], [2199, 2202], [2199, 2202], [3953, 3955], "[performTranslation]", [1576, 1578], "[loadVoices]", [3922, 3925], [3922, 3925], [4317, 4320], [4317, 4320], [294, 297], [294, 297], [1239, 1242], [1239, 1242], [2188, 2191], [2188, 2191], [12654, 12657], [12654, 12657], [785, 810], "[mediaStream, mediaStream.isSupported]"]