/**
 * CaptionOverlay Component
 *
 * Displays captions over the video player with proper positioning and styling.
 * Based on overlay.html functionality from the original CAPTION.Ninja.
 */

'use client';

import React, { useState, useEffect } from 'react';

interface CaptionOverlayProps {
  finalTranscript: string;
  interimTranscript: string;
  translatedText?: string;
  isVisible: boolean;
  position?: 'top' | 'center' | 'bottom';
  size?: 'small' | 'medium' | 'large';
  style?: 'default' | 'minimal' | 'bold';
  autoHide?: boolean;
  autoHideDelay?: number;
}

const CaptionOverlay: React.FC<CaptionOverlayProps> = ({
  finalTranscript,
  interimTranscript,
  translatedText,
  isVisible,
  position = 'bottom',
  size = 'medium',
  style = 'default',
  autoHide = false,
  autoHideDelay = 5000
}) => {
  const [shouldShow, setShouldShow] = useState(isVisible);
  const [hideTimeout, setHideTimeout] = useState<NodeJS.Timeout | null>(null);

  // Handle auto-hide functionality
  useEffect(() => {
    if (autoHide && (finalTranscript || interimTranscript)) {
      // Clear existing timeout
      if (hideTimeout) {
        clearTimeout(hideTimeout);
      }

      // Set new timeout
      const timeout = setTimeout(() => {
        setShouldShow(false);
      }, autoHideDelay);

      setHideTimeout(timeout);
      setShouldShow(true);
    } else if (!autoHide) {
      setShouldShow(isVisible);
    }

    return () => {
      if (hideTimeout) {
        clearTimeout(hideTimeout);
      }
    };
  }, [finalTranscript, interimTranscript, autoHide, autoHideDelay, isVisible, hideTimeout]);

  // Don't render if not visible or no content
  if (!shouldShow || (!finalTranscript && !interimTranscript && !translatedText)) {
    return null;
  }

  // Position classes
  const getPositionClasses = () => {
    switch (position) {
      case 'top':
        return 'top-4 left-4 right-4';
      case 'center':
        return 'top-1/2 left-4 right-4 transform -translate-y-1/2';
      default:
        return 'bottom-4 left-4 right-4';
    }
  };

  // Size classes
  const getSizeClasses = () => {
    switch (size) {
      case 'small':
        return 'text-sm p-2';
      case 'large':
        return 'text-xl p-6';
      default:
        return 'text-base p-4';
    }
  };

  // Style classes
  const getStyleClasses = () => {
    switch (style) {
      case 'minimal':
        return 'bg-black bg-opacity-50 text-white';
      case 'bold':
        return 'bg-black bg-opacity-90 text-white border-2 border-white';
      default:
        return 'bg-black bg-opacity-75 text-white';
    }
  };

  return (
    <div
      className={`absolute ${getPositionClasses()} ${getSizeClasses()} ${getStyleClasses()} rounded-lg shadow-lg transition-all duration-300 ease-in-out max-w-full`}
      style={{
        textShadow: '1px 1px 2px rgba(0,0,0,0.8)',
        backdropFilter: 'blur(4px)',
        WebkitBackdropFilter: 'blur(4px)'
      }}
    >
      {/* Final transcript */}
      {finalTranscript && (
        <div className="mb-2 font-medium leading-relaxed">
          {finalTranscript}
        </div>
      )}

      {/* Interim transcript */}
      {interimTranscript && (
        <div className="text-gray-300 italic leading-relaxed">
          {interimTranscript}
        </div>
      )}

      {/* Translated text */}
      {translatedText && (
        <div className="mt-3 text-blue-200 border-t border-gray-500 border-opacity-50 pt-3 leading-relaxed">
          <div className="text-xs uppercase tracking-wide text-blue-300 mb-1">Translation</div>
          {translatedText}
        </div>
      )}

      {/* Visual indicator for live captions */}
      {interimTranscript && (
        <div className="absolute -top-2 -right-2 w-3 h-3 bg-red-500 rounded-full animate-pulse"></div>
      )}
    </div>
  );
};

export default CaptionOverlay;
