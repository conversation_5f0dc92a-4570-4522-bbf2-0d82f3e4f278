"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./src/app/page.tsx":
/*!**************************!*\
  !*** ./src/app/page.tsx ***!
  \**************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Home)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var _contexts_CaptionContext__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/contexts/CaptionContext */ \"(app-pages-browser)/./src/contexts/CaptionContext.tsx\");\n/* harmony import */ var _components_SpeechRecognition_SpeechController__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/SpeechRecognition/SpeechController */ \"(app-pages-browser)/./src/components/SpeechRecognition/SpeechController.tsx\");\n/* harmony import */ var _components_VideoPlayer_VideoPlayerWithCaptions__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/VideoPlayer/VideoPlayerWithCaptions */ \"(app-pages-browser)/./src/components/VideoPlayer/VideoPlayerWithCaptions.tsx\");\n/* harmony import */ var _components_VideoPlayer_DeviceSelector__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/VideoPlayer/DeviceSelector */ \"(app-pages-browser)/./src/components/VideoPlayer/DeviceSelector.tsx\");\n/* harmony import */ var _components_Translation_TranslationSettings__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/Translation/TranslationSettings */ \"(app-pages-browser)/./src/components/Translation/TranslationSettings.tsx\");\n/* harmony import */ var _components_ErrorBoundary__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/ErrorBoundary */ \"(app-pages-browser)/./src/components/ErrorBoundary.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\nfunction Home() {\n    _s();\n    const { state } = (0,_contexts_CaptionContext__WEBPACK_IMPORTED_MODULE_1__.useCaption)();\n    const actions = (0,_contexts_CaptionContext__WEBPACK_IMPORTED_MODULE_1__.useCaptionActions)();\n    const selectors = (0,_contexts_CaptionContext__WEBPACK_IMPORTED_MODULE_1__.useCaptionSelectors)();\n    const handleTestTranscript = ()=>{\n        actions.setFinalTranscript(\"Hello, this is a test transcript from CAPTION.Ninja!\");\n        actions.setTranslatedText(\"¡Hola, esta es una transcripción de prueba de CAPTION.Ninja!\");\n    };\n    const handleClearTranscript = ()=>{\n        actions.resetTranscript();\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 p-8\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"max-w-4xl mx-auto\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"header\", {\n                    className: \"text-center mb-8\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                            className: \"text-4xl font-bold text-gray-900 mb-2\",\n                            children: \"CAPTION.Ninja\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive - UGSM-Monarch Business School GmbH\\\\Documents\\\\captionninja\\\\caption-ninja-nextjs\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 28,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-lg text-gray-600\",\n                            children: \"Real-time Speech-to-Text with Translation & Video Streaming\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive - UGSM-Monarch Business School GmbH\\\\Documents\\\\captionninja\\\\caption-ninja-nextjs\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 31,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive - UGSM-Monarch Business School GmbH\\\\Documents\\\\captionninja\\\\caption-ninja-nextjs\\\\src\\\\app\\\\page.tsx\",\n                    lineNumber: 27,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"mb-8\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ErrorBoundary__WEBPACK_IMPORTED_MODULE_6__.VideoPlayerErrorBoundary, {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_VideoPlayer_VideoPlayerWithCaptions__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                            className: \"w-full max-w-4xl mx-auto\",\n                            showControls: true,\n                            autoStart: false,\n                            aspectRatio: \"16:9\",\n                            quality: \"high\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive - UGSM-Monarch Business School GmbH\\\\Documents\\\\captionninja\\\\caption-ninja-nextjs\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 39,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive - UGSM-Monarch Business School GmbH\\\\Documents\\\\captionninja\\\\caption-ninja-nextjs\\\\src\\\\app\\\\page.tsx\",\n                        lineNumber: 38,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive - UGSM-Monarch Business School GmbH\\\\Documents\\\\captionninja\\\\caption-ninja-nextjs\\\\src\\\\app\\\\page.tsx\",\n                    lineNumber: 37,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"grid grid-cols-1 lg:grid-cols-3 gap-6 mb-8\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"lg:col-span-2\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ErrorBoundary__WEBPACK_IMPORTED_MODULE_6__.SpeechRecognitionErrorBoundary, {\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_SpeechRecognition_SpeechController__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {}, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive - UGSM-Monarch Business School GmbH\\\\Documents\\\\captionninja\\\\caption-ninja-nextjs\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 53,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive - UGSM-Monarch Business School GmbH\\\\Documents\\\\captionninja\\\\caption-ninja-nextjs\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 52,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive - UGSM-Monarch Business School GmbH\\\\Documents\\\\captionninja\\\\caption-ninja-nextjs\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 51,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"space-y-6\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"bg-white rounded-lg shadow-md p-6\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                            className: \"text-xl font-semibold text-gray-800 mb-4\",\n                                            children: \"System Status\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive - UGSM-Monarch Business School GmbH\\\\Documents\\\\captionninja\\\\caption-ninja-nextjs\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 61,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"space-y-2 text-sm\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"font-medium\",\n                                                            children: \"Session:\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive - UGSM-Monarch Business School GmbH\\\\Documents\\\\captionninja\\\\caption-ninja-nextjs\\\\src\\\\app\\\\page.tsx\",\n                                                            lineNumber: 66,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        \" \",\n                                                        state.sessionId.split('-').pop()\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive - UGSM-Monarch Business School GmbH\\\\Documents\\\\captionninja\\\\caption-ninja-nextjs\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 65,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"font-medium\",\n                                                            children: \"Recording:\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive - UGSM-Monarch Business School GmbH\\\\Documents\\\\captionninja\\\\caption-ninja-nextjs\\\\src\\\\app\\\\page.tsx\",\n                                                            lineNumber: 69,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        \" \",\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: state.isRecording ? \"text-green-600\" : \"text-red-600\",\n                                                            children: state.isRecording ? \"🔴 Live\" : \"⭕ Stopped\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive - UGSM-Monarch Business School GmbH\\\\Documents\\\\captionninja\\\\caption-ninja-nextjs\\\\src\\\\app\\\\page.tsx\",\n                                                            lineNumber: 70,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive - UGSM-Monarch Business School GmbH\\\\Documents\\\\captionninja\\\\caption-ninja-nextjs\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 68,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"font-medium\",\n                                                            children: \"Translation:\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive - UGSM-Monarch Business School GmbH\\\\Documents\\\\captionninja\\\\caption-ninja-nextjs\\\\src\\\\app\\\\page.tsx\",\n                                                            lineNumber: 75,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        \" \",\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: state.settings.translationEnabled ? \"text-green-600\" : \"text-gray-500\",\n                                                            children: state.settings.translationEnabled ? \"✅ Enabled\" : \"❌ Disabled\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive - UGSM-Monarch Business School GmbH\\\\Documents\\\\captionninja\\\\caption-ninja-nextjs\\\\src\\\\app\\\\page.tsx\",\n                                                            lineNumber: 76,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive - UGSM-Monarch Business School GmbH\\\\Documents\\\\captionninja\\\\caption-ninja-nextjs\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 74,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"font-medium\",\n                                                            children: \"Language:\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive - UGSM-Monarch Business School GmbH\\\\Documents\\\\captionninja\\\\caption-ninja-nextjs\\\\src\\\\app\\\\page.tsx\",\n                                                            lineNumber: 81,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        \" \",\n                                                        state.settings.speechLanguage\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive - UGSM-Monarch Business School GmbH\\\\Documents\\\\captionninja\\\\caption-ninja-nextjs\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 80,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"font-medium\",\n                                                            children: \"History:\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive - UGSM-Monarch Business School GmbH\\\\Documents\\\\captionninja\\\\caption-ninja-nextjs\\\\src\\\\app\\\\page.tsx\",\n                                                            lineNumber: 84,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        \" \",\n                                                        state.transcriptHistory.length,\n                                                        \" entries\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive - UGSM-Monarch Business School GmbH\\\\Documents\\\\captionninja\\\\caption-ninja-nextjs\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 83,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"font-medium\",\n                                                            children: \"Words:\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive - UGSM-Monarch Business School GmbH\\\\Documents\\\\captionninja\\\\caption-ninja-nextjs\\\\src\\\\app\\\\page.tsx\",\n                                                            lineNumber: 87,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        \" \",\n                                                        selectors.getTranscriptWordCount()\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive - UGSM-Monarch Business School GmbH\\\\Documents\\\\captionninja\\\\caption-ninja-nextjs\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 86,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive - UGSM-Monarch Business School GmbH\\\\Documents\\\\captionninja\\\\caption-ninja-nextjs\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 64,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"mt-4 pt-4 border-t border-gray-200\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                    className: \"text-sm font-medium text-gray-700 mb-2\",\n                                                    children: \"Quick Tests\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive - UGSM-Monarch Business School GmbH\\\\Documents\\\\captionninja\\\\caption-ninja-nextjs\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 93,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"space-y-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                            onClick: handleTestTranscript,\n                                                            className: \"w-full px-3 py-2 text-sm bg-blue-500 text-white rounded hover:bg-blue-600 transition-colors\",\n                                                            children: \"Add Test Transcript\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive - UGSM-Monarch Business School GmbH\\\\Documents\\\\captionninja\\\\caption-ninja-nextjs\\\\src\\\\app\\\\page.tsx\",\n                                                            lineNumber: 95,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                            onClick: handleClearTranscript,\n                                                            className: \"w-full px-3 py-2 text-sm bg-red-500 text-white rounded hover:bg-red-600 transition-colors\",\n                                                            children: \"Clear All\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive - UGSM-Monarch Business School GmbH\\\\Documents\\\\captionninja\\\\caption-ninja-nextjs\\\\src\\\\app\\\\page.tsx\",\n                                                            lineNumber: 101,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                            onClick: ()=>actions.updateSettings({\n                                                                    translationEnabled: !state.settings.translationEnabled\n                                                                }),\n                                                            className: \"w-full px-3 py-2 text-sm bg-purple-500 text-white rounded hover:bg-purple-600 transition-colors\",\n                                                            children: \"Toggle Translation\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive - UGSM-Monarch Business School GmbH\\\\Documents\\\\captionninja\\\\caption-ninja-nextjs\\\\src\\\\app\\\\page.tsx\",\n                                                            lineNumber: 107,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive - UGSM-Monarch Business School GmbH\\\\Documents\\\\captionninja\\\\caption-ninja-nextjs\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 94,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive - UGSM-Monarch Business School GmbH\\\\Documents\\\\captionninja\\\\caption-ninja-nextjs\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 92,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive - UGSM-Monarch Business School GmbH\\\\Documents\\\\captionninja\\\\caption-ninja-nextjs\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 60,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"bg-white rounded-lg shadow-md p-6\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                            className: \"text-xl font-semibold text-gray-800 mb-4\",\n                                            children: \"Device Settings\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive - UGSM-Monarch Business School GmbH\\\\Documents\\\\captionninja\\\\caption-ninja-nextjs\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 121,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_VideoPlayer_DeviceSelector__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                            showAudioDevices: true,\n                                            showVideoDevices: true\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive - UGSM-Monarch Business School GmbH\\\\Documents\\\\captionninja\\\\caption-ninja-nextjs\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 124,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive - UGSM-Monarch Business School GmbH\\\\Documents\\\\captionninja\\\\caption-ninja-nextjs\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 120,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"bg-white rounded-lg shadow-md p-6\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Translation_TranslationSettings__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {}, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive - UGSM-Monarch Business School GmbH\\\\Documents\\\\captionninja\\\\caption-ninja-nextjs\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 132,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive - UGSM-Monarch Business School GmbH\\\\Documents\\\\captionninja\\\\caption-ninja-nextjs\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 131,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive - UGSM-Monarch Business School GmbH\\\\Documents\\\\captionninja\\\\caption-ninja-nextjs\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 58,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive - UGSM-Monarch Business School GmbH\\\\Documents\\\\captionninja\\\\caption-ninja-nextjs\\\\src\\\\app\\\\page.tsx\",\n                    lineNumber: 49,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"bg-white rounded-lg shadow-md p-6\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                            className: \"text-xl font-semibold text-gray-800 mb-4\",\n                            children: \"Transcript History\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive - UGSM-Monarch Business School GmbH\\\\Documents\\\\captionninja\\\\caption-ninja-nextjs\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 139,\n                            columnNumber: 11\n                        }, this),\n                        state.transcriptHistory.length > 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"space-y-3 max-h-96 overflow-y-auto\",\n                            children: [\n                                state.transcriptHistory.slice(-10).reverse().map((entry, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"p-3 bg-gray-50 border border-gray-200 rounded-lg\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-start justify-between mb-2\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center gap-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-xs bg-blue-100 text-blue-800 px-2 py-1 rounded\",\n                                                            children: [\n                                                                \"#\",\n                                                                state.transcriptHistory.length - index\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive - UGSM-Monarch Business School GmbH\\\\Documents\\\\captionninja\\\\caption-ninja-nextjs\\\\src\\\\app\\\\page.tsx\",\n                                                            lineNumber: 149,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-xs text-gray-500\",\n                                                            children: new Date(entry.timestamp).toLocaleTimeString()\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive - UGSM-Monarch Business School GmbH\\\\Documents\\\\captionninja\\\\caption-ninja-nextjs\\\\src\\\\app\\\\page.tsx\",\n                                                            lineNumber: 152,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        entry.languageCode && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-xs bg-green-100 text-green-800 px-2 py-1 rounded\",\n                                                            children: entry.languageCode\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive - UGSM-Monarch Business School GmbH\\\\Documents\\\\captionninja\\\\caption-ninja-nextjs\\\\src\\\\app\\\\page.tsx\",\n                                                            lineNumber: 156,\n                                                            columnNumber: 25\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive - UGSM-Monarch Business School GmbH\\\\Documents\\\\captionninja\\\\caption-ninja-nextjs\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 148,\n                                                    columnNumber: 21\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive - UGSM-Monarch Business School GmbH\\\\Documents\\\\captionninja\\\\caption-ninja-nextjs\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 147,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-gray-800 text-sm leading-relaxed\",\n                                                children: entry.text\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive - UGSM-Monarch Business School GmbH\\\\Documents\\\\captionninja\\\\caption-ninja-nextjs\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 162,\n                                                columnNumber: 19\n                                            }, this),\n                                            entry.translatedText && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-blue-600 text-sm mt-2 italic border-l-2 border-blue-200 pl-2\",\n                                                children: entry.translatedText\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive - UGSM-Monarch Business School GmbH\\\\Documents\\\\captionninja\\\\caption-ninja-nextjs\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 164,\n                                                columnNumber: 21\n                                            }, this)\n                                        ]\n                                    }, entry.id, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive - UGSM-Monarch Business School GmbH\\\\Documents\\\\captionninja\\\\caption-ninja-nextjs\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 146,\n                                        columnNumber: 17\n                                    }, this)),\n                                state.transcriptHistory.length > 10 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-center text-sm text-gray-500 py-2\",\n                                    children: [\n                                        \"Showing last 10 entries of \",\n                                        state.transcriptHistory.length,\n                                        \" total\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive - UGSM-Monarch Business School GmbH\\\\Documents\\\\captionninja\\\\caption-ninja-nextjs\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 171,\n                                    columnNumber: 17\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive - UGSM-Monarch Business School GmbH\\\\Documents\\\\captionninja\\\\caption-ninja-nextjs\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 144,\n                            columnNumber: 13\n                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-center py-8 text-gray-500\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-4xl mb-2\",\n                                    children: \"\\uD83C\\uDFA4\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive - UGSM-Monarch Business School GmbH\\\\Documents\\\\captionninja\\\\caption-ninja-nextjs\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 178,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    children: \"No transcript history yet\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive - UGSM-Monarch Business School GmbH\\\\Documents\\\\captionninja\\\\caption-ninja-nextjs\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 179,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-sm mt-1\",\n                                    children: \"Start recording to see your speech transcribed here\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive - UGSM-Monarch Business School GmbH\\\\Documents\\\\captionninja\\\\caption-ninja-nextjs\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 180,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive - UGSM-Monarch Business School GmbH\\\\Documents\\\\captionninja\\\\caption-ninja-nextjs\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 177,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive - UGSM-Monarch Business School GmbH\\\\Documents\\\\captionninja\\\\caption-ninja-nextjs\\\\src\\\\app\\\\page.tsx\",\n                    lineNumber: 138,\n                    columnNumber: 9\n                }, this),\n                (state.speechError || state.translationError || state.ttsError) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"bg-red-50 border border-red-200 rounded-lg p-4\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                            className: \"font-medium text-red-800 mb-2\",\n                            children: \"Errors:\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive - UGSM-Monarch Business School GmbH\\\\Documents\\\\captionninja\\\\caption-ninja-nextjs\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 188,\n                            columnNumber: 13\n                        }, this),\n                        state.speechError && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-red-700 text-sm\",\n                            children: [\n                                \"Speech: \",\n                                state.speechError\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive - UGSM-Monarch Business School GmbH\\\\Documents\\\\captionninja\\\\caption-ninja-nextjs\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 190,\n                            columnNumber: 15\n                        }, this),\n                        state.translationError && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-red-700 text-sm\",\n                            children: [\n                                \"Translation: \",\n                                state.translationError\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive - UGSM-Monarch Business School GmbH\\\\Documents\\\\captionninja\\\\caption-ninja-nextjs\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 193,\n                            columnNumber: 15\n                        }, this),\n                        state.ttsError && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-red-700 text-sm\",\n                            children: [\n                                \"TTS: \",\n                                state.ttsError\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive - UGSM-Monarch Business School GmbH\\\\Documents\\\\captionninja\\\\caption-ninja-nextjs\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 196,\n                            columnNumber: 15\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive - UGSM-Monarch Business School GmbH\\\\Documents\\\\captionninja\\\\caption-ninja-nextjs\\\\src\\\\app\\\\page.tsx\",\n                    lineNumber: 187,\n                    columnNumber: 11\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"footer\", {\n                    className: \"text-center text-gray-600 text-sm\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            children: \"CAPTION.Ninja Next.js Migration - Context Testing Page\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive - UGSM-Monarch Business School GmbH\\\\Documents\\\\captionninja\\\\caption-ninja-nextjs\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 202,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"mt-1\",\n                            children: \"This page demonstrates the centralized state management system\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive - UGSM-Monarch Business School GmbH\\\\Documents\\\\captionninja\\\\caption-ninja-nextjs\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 203,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive - UGSM-Monarch Business School GmbH\\\\Documents\\\\captionninja\\\\caption-ninja-nextjs\\\\src\\\\app\\\\page.tsx\",\n                    lineNumber: 201,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive - UGSM-Monarch Business School GmbH\\\\Documents\\\\captionninja\\\\caption-ninja-nextjs\\\\src\\\\app\\\\page.tsx\",\n            lineNumber: 26,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive - UGSM-Monarch Business School GmbH\\\\Documents\\\\captionninja\\\\caption-ninja-nextjs\\\\src\\\\app\\\\page.tsx\",\n        lineNumber: 25,\n        columnNumber: 5\n    }, this);\n}\n_s(Home, \"UKz95Ypqnn+5m1AMQ0CsYbmEMtQ=\", false, function() {\n    return [\n        _contexts_CaptionContext__WEBPACK_IMPORTED_MODULE_1__.useCaption,\n        _contexts_CaptionContext__WEBPACK_IMPORTED_MODULE_1__.useCaptionActions,\n        _contexts_CaptionContext__WEBPACK_IMPORTED_MODULE_1__.useCaptionSelectors\n    ];\n});\n_c = Home;\nvar _c;\n$RefreshReg$(_c, \"Home\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/page.tsx\n"));

/***/ })

});