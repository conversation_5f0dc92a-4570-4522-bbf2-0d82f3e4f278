/**
 * Google Cloud Translation API Route
 * 
 * Next.js API route for handling translation requests.
 * Based on the translation implementation from the original CAPTION.Ninja.
 */

import { NextRequest, NextResponse } from 'next/server';

interface TranslationRequest {
  text: string;
  sourceLanguage?: string;
  targetLanguage: string;
  apiKey?: string;
}

interface GoogleTranslationResponse {
  data: {
    translations: Array<{
      translatedText: string;
      detectedSourceLanguage?: string;
    }>;
  };
}

export async function POST(request: NextRequest) {
  try {
    const body: TranslationRequest = await request.json();
    const { text, sourceLanguage, targetLanguage, apiKey } = body;

    // Validate required fields
    if (!text || !text.trim()) {
      return NextResponse.json(
        { error: 'Text is required for translation' },
        { status: 400 }
      );
    }

    if (!targetLanguage) {
      return NextResponse.json(
        { error: 'Target language is required' },
        { status: 400 }
      );
    }

    // Check if API key is provided (either in request or environment)
    const translationApiKey = apiKey || process.env.GOOGLE_CLOUD_TRANSLATION_API_KEY;
    
    if (!translationApiKey) {
      return NextResponse.json(
        { error: 'Google Cloud Translation API key is required' },
        { status: 401 }
      );
    }

    // Skip translation if source and target languages are the same
    if (sourceLanguage && sourceLanguage === targetLanguage) {
      return NextResponse.json({
        translatedText: text,
        detectedLanguage: sourceLanguage,
        confidence: 1.0,
        metadata: {
          model: 'passthrough',
          processingTime: 0,
          characterCount: text.length,
          wordCount: text.split(/\s+/).length,
          cacheHit: false
        }
      });
    }

    // Prepare Google Cloud Translation API request
    const params = new URLSearchParams({
      key: translationApiKey,
      q: text,
      target: targetLanguage,
      format: 'text'
    });

    // Add source language if specified (otherwise Google will auto-detect)
    if (sourceLanguage && sourceLanguage !== 'auto') {
      params.append('source', sourceLanguage);
    }

    const googleApiUrl = `https://www.googleapis.com/language/translate/v2?${params.toString()}`;

    // Make request to Google Cloud Translation API
    const startTime = Date.now();
    const response = await fetch(googleApiUrl, {
      method: 'GET',
      headers: {
        'Accept': 'application/json',
        'Content-Type': 'application/json',
      },
    });

    const processingTime = Date.now() - startTime;

    if (!response.ok) {
      let errorMessage = `Google Translation API error: ${response.status}`;
      
      try {
        const errorData = await response.json();
        if (errorData.error?.message) {
          errorMessage = errorData.error.message;
        }
      } catch (e) {
        // Use default error message if can't parse error response
      }

      return NextResponse.json(
        { error: errorMessage },
        { status: response.status }
      );
    }

    const data: GoogleTranslationResponse = await response.json();

    if (!data.data?.translations?.[0]) {
      return NextResponse.json(
        { error: 'No translation returned from Google API' },
        { status: 500 }
      );
    }

    const translation = data.data.translations[0];
    
    // Prepare response in our format
    const result = {
      translatedText: translation.translatedText,
      detectedLanguage: translation.detectedSourceLanguage || sourceLanguage,
      confidence: 0.95, // Google doesn't provide confidence scores, so we use a high default
      metadata: {
        model: 'google-translate-v2',
        processingTime,
        characterCount: text.length,
        wordCount: text.split(/\s+/).length,
        cacheHit: false
      }
    };

    return NextResponse.json(result);

  } catch (error) {
    console.error('Translation API error:', error);
    
    let errorMessage = 'Internal server error during translation';
    if (error instanceof Error) {
      errorMessage = error.message;
    }

    return NextResponse.json(
      { error: errorMessage },
      { status: 500 }
    );
  }
}

// Handle GET requests (for health checks)
export async function GET() {
  return NextResponse.json({
    status: 'ok',
    service: 'Google Cloud Translation API',
    version: '1.0.0',
    timestamp: new Date().toISOString()
  });
}
