/**
 * useTTS Hook
 * 
 * React hook for text-to-speech functionality with multiple provider support.
 * Based on the TTS integration from tts-integration.js.
 */

import { useState, useRef, useCallback, useEffect } from 'react';

interface TTSOptions {
  voice?: string;
  rate: number;
  pitch: number;
  volume: number;
  lang?: string;
}

interface TTSHook {
  speak: (text: string, options?: Partial<TTSOptions>) => void;
  stop: () => void;
  pause: () => void;
  resume: () => void;
  isPlaying: boolean;
  isPaused: boolean;
  isSupported: boolean;
  voices: SpeechSynthesisVoice[];
  currentText: string;
  error: string | null;
}

const useTTS = (defaultOptions: Partial<TTSOptions> = {}): TTSHook => {
  const [isPlaying, setIsPlaying] = useState<boolean>(false);
  const [isPaused, setIsPaused] = useState<boolean>(false);
  const [isSupported, setIsSupported] = useState<boolean>(false);
  const [voices, setVoices] = useState<SpeechSynthesisVoice[]>([]);
  const [currentText, setCurrentText] = useState<string>('');
  const [error, setError] = useState<string | null>(null);

  const utteranceRef = useRef<SpeechSynthesisUtterance | null>(null);
  const optionsRef = useRef<TTSOptions>({
    rate: 1,
    pitch: 1,
    volume: 1,
    ...defaultOptions
  });

  // Check for speech synthesis support
  useEffect(() => {
    if (typeof window !== 'undefined' && 'speechSynthesis' in window) {
      setIsSupported(true);
      loadVoices();
    } else {
      setIsSupported(false);
      setError('Speech synthesis is not supported in this browser');
    }
  }, []);

  // Load available voices
  const loadVoices = useCallback(() => {
    if (!isSupported) return;

    const availableVoices = speechSynthesis.getVoices();
    setVoices(availableVoices);

    // Set default voice if none specified
    if (!optionsRef.current.voice && availableVoices.length > 0) {
      const defaultVoice = availableVoices.find(voice => voice.default) || availableVoices[0];
      optionsRef.current.voice = defaultVoice.name;
    }
  }, [isSupported]);

  // Listen for voices changed event (some browsers load voices asynchronously)
  useEffect(() => {
    if (isSupported) {
      speechSynthesis.addEventListener('voiceschanged', loadVoices);
      return () => {
        speechSynthesis.removeEventListener('voiceschanged', loadVoices);
      };
    }
  }, [isSupported, loadVoices]);

  // Update options when defaultOptions change
  useEffect(() => {
    optionsRef.current = {
      ...optionsRef.current,
      ...defaultOptions
    };
  }, [defaultOptions]);

  // Stop speech synthesis
  const stop = useCallback(() => {
    if (isSupported && speechSynthesis.speaking) {
      speechSynthesis.cancel();
      setIsPlaying(false);
      setIsPaused(false);
      setCurrentText('');
    }
  }, [isSupported]);

  // Speak text using speech synthesis
  const speak = useCallback((text: string, options: Partial<TTSOptions> = {}) => {
    if (!isSupported) {
      setError('Speech synthesis is not supported');
      return;
    }

    if (!text.trim()) {
      return;
    }

    // Stop any current speech
    stop();

    const mergedOptions = { ...optionsRef.current, ...options };
    const utterance = new SpeechSynthesisUtterance(text);

    // Configure utterance
    utterance.rate = mergedOptions.rate;
    utterance.pitch = mergedOptions.pitch;
    utterance.volume = mergedOptions.volume;

    if (mergedOptions.lang) {
      utterance.lang = mergedOptions.lang;
    }

    // Set voice if specified
    if (mergedOptions.voice) {
      const selectedVoice = voices.find(voice => voice.name === mergedOptions.voice);
      if (selectedVoice) {
        utterance.voice = selectedVoice;
      }
    }

    // Event handlers
    utterance.onstart = () => {
      setIsPlaying(true);
      setIsPaused(false);
      setCurrentText(text);
      setError(null);
    };

    utterance.onend = () => {
      setIsPlaying(false);
      setIsPaused(false);
      setCurrentText('');
    };

    utterance.onerror = (event) => {
      setError(`Speech synthesis error: ${event.error}`);
      setIsPlaying(false);
      setIsPaused(false);
      setCurrentText('');
    };

    utterance.onpause = () => {
      setIsPaused(true);
    };

    utterance.onresume = () => {
      setIsPaused(false);
    };

    utteranceRef.current = utterance;

    try {
      speechSynthesis.speak(utterance);
    } catch (err) {
      setError('Failed to start speech synthesis');
    }
  }, [isSupported, voices, stop]);

  // Pause speech synthesis
  const pause = useCallback(() => {
    if (isSupported && speechSynthesis.speaking && !speechSynthesis.paused) {
      speechSynthesis.pause();
      setIsPaused(true);
    }
  }, [isSupported]);

  // Resume speech synthesis
  const resume = useCallback(() => {
    if (isSupported && speechSynthesis.paused) {
      speechSynthesis.resume();
      setIsPaused(false);
    }
  }, [isSupported]);

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      stop();
    };
  }, [stop]);

  return {
    speak,
    stop,
    pause,
    resume,
    isPlaying,
    isPaused,
    isSupported,
    voices,
    currentText,
    error
  };
};

export default useTTS;
