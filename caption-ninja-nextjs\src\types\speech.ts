/**
 * Speech Recognition Types
 * 
 * TypeScript type definitions for speech recognition functionality.
 */

// Speech recognition options
export interface SpeechRecognitionOptions {
  language: string;
  continuous: boolean;
  interimResults: boolean;
  maxAlternatives: number;
  grammars?: any;
  serviceURI?: string;
}

// Speech recognition result
export interface SpeechRecognitionResult {
  finalTranscript: string;
  interimTranscript: string;
  isListening: boolean;
  error: string | null;
  isSupported: boolean;
  confidence?: number;
  alternatives?: SpeechRecognitionAlternative[];
}

// Speech recognition alternative
export interface SpeechRecognitionAlternative {
  transcript: string;
  confidence: number;
}

// Speech recognition event data
export interface SpeechRecognitionEventData {
  transcript: string;
  confidence: number;
  isFinal: boolean;
  alternatives: SpeechRecognitionAlternative[];
  timestamp: number;
}

// Speech recognition error types
export type SpeechRecognitionErrorType = 
  | 'no-speech'
  | 'aborted'
  | 'audio-capture'
  | 'network'
  | 'not-allowed'
  | 'service-not-allowed'
  | 'bad-grammar'
  | 'language-not-supported';

// Speech recognition error
export interface SpeechRecognitionError {
  error: SpeechRecognitionErrorType;
  message: string;
  timestamp: number;
}

// Speech recognition status
export type SpeechRecognitionStatus = 
  | 'idle'
  | 'listening'
  | 'processing'
  | 'error'
  | 'stopped';

// Speech recognition state
export interface SpeechRecognitionState {
  status: SpeechRecognitionStatus;
  isSupported: boolean;
  currentLanguage: string;
  availableLanguages: SpeechLanguage[];
  error: SpeechRecognitionError | null;
  lastResult: SpeechRecognitionEventData | null;
  sessionStartTime: number | null;
  totalListeningTime: number;
  restartCount: number;
}

// Speech language definition
export interface SpeechLanguage {
  code: string;
  name: string;
  nativeName: string;
  flag: string;
  region?: string;
  isSupported: boolean;
  confidence?: number;
}

// Speech recognition configuration
export interface SpeechRecognitionConfig {
  autoRestart: boolean;
  maxRestartAttempts: number;
  restartDelay: number;
  silenceTimeout: number;
  noSpeechTimeout: number;
  confidenceThreshold: number;
  enableProfanityFilter: boolean;
  enablePunctuation: boolean;
  enableCapitalization: boolean;
}

// Speech recognition metrics
export interface SpeechRecognitionMetrics {
  totalSessions: number;
  totalListeningTime: number;
  averageSessionDuration: number;
  totalWordsRecognized: number;
  averageConfidence: number;
  errorRate: number;
  restartCount: number;
  languageDistribution: { [language: string]: number };
  accuracyByLanguage: { [language: string]: number };
}

// Speech recognition capabilities
export interface SpeechRecognitionCapabilities {
  isSupported: boolean;
  supportsContinuous: boolean;
  supportsInterimResults: boolean;
  supportsGrammars: boolean;
  supportsMaxAlternatives: boolean;
  supportedLanguages: string[];
  maxAlternatives: number;
  browserInfo: {
    name: string;
    version: string;
    engine: string;
  };
}

// Speech recognition event handlers
export interface SpeechRecognitionEventHandlers {
  onStart?: () => void;
  onEnd?: () => void;
  onResult?: (event: SpeechRecognitionEventData) => void;
  onError?: (error: SpeechRecognitionError) => void;
  onSpeechStart?: () => void;
  onSpeechEnd?: () => void;
  onSoundStart?: () => void;
  onSoundEnd?: () => void;
  onAudioStart?: () => void;
  onAudioEnd?: () => void;
  onNoMatch?: () => void;
}

// Speech recognition hook return type
export interface SpeechRecognitionHook extends SpeechRecognitionResult {
  startListening: () => void;
  stopListening: () => void;
  resetTranscript: () => void;
  setLanguage: (language: string) => void;
  getCapabilities: () => SpeechRecognitionCapabilities;
  getMetrics: () => SpeechRecognitionMetrics;
  updateConfig: (config: Partial<SpeechRecognitionConfig>) => void;
}

// Speech recognition provider props
export interface SpeechRecognitionProviderProps {
  children: React.ReactNode;
  defaultLanguage?: string;
  config?: Partial<SpeechRecognitionConfig>;
  onError?: (error: SpeechRecognitionError) => void;
}

// Speech recognition context value
export interface SpeechRecognitionContextValue {
  state: SpeechRecognitionState;
  actions: {
    start: () => void;
    stop: () => void;
    restart: () => void;
    setLanguage: (language: string) => void;
    updateConfig: (config: Partial<SpeechRecognitionConfig>) => void;
  };
  capabilities: SpeechRecognitionCapabilities;
  metrics: SpeechRecognitionMetrics;
}

// Speech grammar rule
export interface SpeechGrammarRule {
  src: string;
  weight?: number;
}

// Speech recognition command
export interface SpeechRecognitionCommand {
  command: string | RegExp | string[];
  callback: (transcript: string, matches?: string[]) => void;
  matchInterim?: boolean;
  isFuzzyMatch?: boolean;
  fuzzyMatchingThreshold?: number;
  bestMatchOnly?: boolean;
}

// Speech recognition phrase
export interface SpeechRecognitionPhrase {
  phrase: string;
  weight?: number;
  alternatives?: string[];
}

// Speech recognition vocabulary
export interface SpeechRecognitionVocabulary {
  phrases: SpeechRecognitionPhrase[];
  commands: SpeechRecognitionCommand[];
  customWords: string[];
}

// Speech recognition audio settings
export interface SpeechRecognitionAudioSettings {
  echoCancellation: boolean;
  noiseSuppression: boolean;
  autoGainControl: boolean;
  sampleRate?: number;
  channelCount?: number;
  volume?: number;
  deviceId?: string;
}

// Speech recognition performance settings
export interface SpeechRecognitionPerformanceSettings {
  enableOptimizations: boolean;
  batchSize: number;
  processingDelay: number;
  memoryLimit: number;
  enableCaching: boolean;
  cacheSize: number;
}

// Speech recognition accessibility settings
export interface SpeechRecognitionAccessibilitySettings {
  enableVisualFeedback: boolean;
  enableAudioFeedback: boolean;
  enableHapticFeedback: boolean;
  highContrastMode: boolean;
  largeTextMode: boolean;
  screenReaderSupport: boolean;
}
