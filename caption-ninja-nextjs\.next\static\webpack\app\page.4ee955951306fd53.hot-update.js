"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./src/hooks/useSpeechRecognition.ts":
/*!*******************************************!*\
  !*** ./src/hooks/useSpeechRecognition.ts ***!
  \*******************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n/**\n * useSpeechRecognition Hook\n * \n * React hook for managing speech recognition functionality.\n * Based on the speech recognition implementation from index.html.\n */ \nconst useSpeechRecognition = function() {\n    let options = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : {};\n    // Memoize default options to prevent recreation on every render\n    const defaultOptions = (0,react__WEBPACK_IMPORTED_MODULE_0__.useMemo)({\n        \"useSpeechRecognition.useMemo[defaultOptions]\": ()=>({\n                language: 'en-US',\n                continuous: true,\n                interimResults: true,\n                maxAlternatives: 1\n            })\n    }[\"useSpeechRecognition.useMemo[defaultOptions]\"], []);\n    // Memoize merged options to prevent infinite re-renders\n    const mergedOptions = (0,react__WEBPACK_IMPORTED_MODULE_0__.useMemo)({\n        \"useSpeechRecognition.useMemo[mergedOptions]\": ()=>({\n                ...defaultOptions,\n                ...options\n            })\n    }[\"useSpeechRecognition.useMemo[mergedOptions]\"], [\n        defaultOptions,\n        options.language,\n        options.continuous,\n        options.interimResults,\n        options.maxAlternatives\n    ]);\n    const [finalTranscript, setFinalTranscript] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)('');\n    const [interimTranscript, setInterimTranscript] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)('');\n    const [isListening, setIsListening] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(false);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(null);\n    const [isSupported, setIsSupported] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(false);\n    const [recognitionState, setRecognitionState] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)('idle');\n    const recognitionRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(null);\n    const optionsRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(mergedOptions);\n    const restartTimeoutRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(null);\n    const restartAttemptsRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(0);\n    const isStoppedManuallyRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(false);\n    const MAX_RESTART_ATTEMPTS = 3;\n    const RESTART_DELAY = 1000;\n    // Update options ref when merged options change\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)({\n        \"useSpeechRecognition.useEffect\": ()=>{\n            optionsRef.current = mergedOptions;\n        }\n    }[\"useSpeechRecognition.useEffect\"], [\n        mergedOptions\n    ]);\n    // Initialize speech recognition\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)({\n        \"useSpeechRecognition.useEffect\": ()=>{\n            if (false) {}\n            const SpeechRecognition = window.SpeechRecognition || window.webkitSpeechRecognition;\n            if (!SpeechRecognition) {\n                setIsSupported(false);\n                setError('Speech recognition is not supported in this browser');\n                return;\n            }\n            setIsSupported(true);\n            const recognition = new SpeechRecognition();\n            recognitionRef.current = recognition;\n            // Configure recognition with current merged options\n            recognition.continuous = mergedOptions.continuous;\n            recognition.interimResults = mergedOptions.interimResults;\n            recognition.lang = mergedOptions.language;\n            recognition.maxAlternatives = mergedOptions.maxAlternatives;\n            // Event handlers with state machine\n            recognition.onstart = ({\n                \"useSpeechRecognition.useEffect\": ()=>{\n                    console.log('Speech recognition started');\n                    setIsListening(true);\n                    setRecognitionState('listening');\n                    setError(null);\n                    // Reset restart attempts on successful start\n                    restartAttemptsRef.current = 0;\n                }\n            })[\"useSpeechRecognition.useEffect\"];\n            recognition.onend = ({\n                \"useSpeechRecognition.useEffect\": ()=>{\n                    console.log('Speech recognition ended');\n                    setIsListening(false);\n                    // Only attempt restart if we're not in stopping state and not stopped manually\n                    if (recognitionState !== 'stopping' && !isStoppedManuallyRef.current && mergedOptions.continuous) {\n                        if (restartAttemptsRef.current < MAX_RESTART_ATTEMPTS) {\n                            console.log(\"Attempting to restart recognition (attempt \".concat(restartAttemptsRef.current + 1, \")\"));\n                            setRecognitionState('restarting');\n                            restartAttemptsRef.current++;\n                            // Use exponential backoff for restart attempts\n                            const backoffDelay = RESTART_DELAY * Math.pow(2, restartAttemptsRef.current - 1);\n                            restartTimeoutRef.current = setTimeout({\n                                \"useSpeechRecognition.useEffect\": ()=>{\n                                    try {\n                                        if (recognitionRef.current && !isStoppedManuallyRef.current && recognitionState !== 'stopping') {\n                                            setRecognitionState('starting');\n                                            recognitionRef.current.start();\n                                        }\n                                    } catch (err) {\n                                        console.error('Failed to restart recognition:', err);\n                                        setError('Failed to restart speech recognition');\n                                        setRecognitionState('error');\n                                    }\n                                }\n                            }[\"useSpeechRecognition.useEffect\"], backoffDelay);\n                        } else {\n                            setError('Maximum restart attempts reached. Please restart manually.');\n                            setRecognitionState('error');\n                        }\n                    } else {\n                        setRecognitionState('idle');\n                    }\n                }\n            })[\"useSpeechRecognition.useEffect\"];\n            recognition.onerror = ({\n                \"useSpeechRecognition.useEffect\": (event)=>{\n                    console.error('Speech recognition error:', event);\n                    // Handle different error types based on original implementation\n                    switch(event.error){\n                        case 'no-speech':\n                            setError('No speech detected. Still listening...');\n                            break;\n                        case 'audio-capture':\n                            setError('No microphone detected. Please check your microphone.');\n                            setIsListening(false);\n                            break;\n                        case 'not-allowed':\n                            setError('Microphone access denied. Please allow microphone access.');\n                            setIsListening(false);\n                            break;\n                        case 'network':\n                            setError('Network error occurred. Will attempt to reconnect...');\n                            break;\n                        case 'aborted':\n                            console.log('Recognition aborted');\n                            if (!isStoppedManuallyRef.current) {\n                                setError('Recognition was aborted unexpectedly');\n                            }\n                            break;\n                        default:\n                            setError(\"Speech recognition error: \".concat(event.error));\n                            setIsListening(false);\n                    }\n                }\n            })[\"useSpeechRecognition.useEffect\"];\n            recognition.onresult = ({\n                \"useSpeechRecognition.useEffect\": (event)=>{\n                    if (typeof event.results === 'undefined') {\n                        console.log('Undefined results in event:', event);\n                        return;\n                    }\n                    let interim = '';\n                    let final = '';\n                    // Process only new results since last event\n                    for(let i = event.resultIndex; i < event.results.length; i++){\n                        const transcript = event.results[i][0].transcript.trim();\n                        if (event.results[i].isFinal) {\n                            final += (final ? ' ' : '') + transcript;\n                        } else {\n                            interim += (interim ? ' ' : '') + transcript;\n                        }\n                    }\n                    setInterimTranscript(interim);\n                    if (final) {\n                        setFinalTranscript({\n                            \"useSpeechRecognition.useEffect\": (prev)=>prev + (prev ? ' ' : '') + final\n                        }[\"useSpeechRecognition.useEffect\"]);\n                        // Reset restart attempts on successful recognition\n                        restartAttemptsRef.current = 0;\n                    }\n                }\n            })[\"useSpeechRecognition.useEffect\"];\n            return ({\n                \"useSpeechRecognition.useEffect\": ()=>{\n                    if (recognition) {\n                        recognition.stop();\n                    }\n                }\n            })[\"useSpeechRecognition.useEffect\"];\n        }\n    }[\"useSpeechRecognition.useEffect\"], [\n        mergedOptions\n    ]);\n    // Update language when it changes\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)({\n        \"useSpeechRecognition.useEffect\": ()=>{\n            if (recognitionRef.current && mergedOptions.language) {\n                recognitionRef.current.lang = mergedOptions.language;\n            }\n        }\n    }[\"useSpeechRecognition.useEffect\"], [\n        mergedOptions.language\n    ]);\n    const startListening = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useSpeechRecognition.useCallback[startListening]\": ()=>{\n            if (!recognitionRef.current || !isSupported) {\n                setError('Speech recognition is not available');\n                return;\n            }\n            if (isListening) {\n                return;\n            }\n            // Clear any pending restart timeout\n            if (restartTimeoutRef.current) {\n                clearTimeout(restartTimeoutRef.current);\n                restartTimeoutRef.current = null;\n            }\n            isStoppedManuallyRef.current = false;\n            restartAttemptsRef.current = 0;\n            setError(null);\n            try {\n                recognitionRef.current.start();\n                console.log('Speech recognition started');\n            } catch (err) {\n                console.error('Failed to start speech recognition:', err);\n                if (err instanceof Error && err.name === 'InvalidStateError') {\n                    // Recognition is already running, try to stop and restart\n                    try {\n                        recognitionRef.current.stop();\n                        setTimeout({\n                            \"useSpeechRecognition.useCallback[startListening]\": ()=>{\n                                try {\n                                    recognitionRef.current.start();\n                                } catch (retryErr) {\n                                    setError('Failed to start speech recognition after retry');\n                                }\n                            }\n                        }[\"useSpeechRecognition.useCallback[startListening]\"], 100);\n                    } catch (stopErr) {\n                        setError('Failed to restart speech recognition');\n                    }\n                } else {\n                    setError('Failed to start speech recognition');\n                }\n            }\n        }\n    }[\"useSpeechRecognition.useCallback[startListening]\"], [\n        isListening,\n        isSupported\n    ]);\n    const stopListening = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useSpeechRecognition.useCallback[stopListening]\": ()=>{\n            if (!recognitionRef.current) return;\n            isStoppedManuallyRef.current = true;\n            // Clear any pending restart timeout\n            if (restartTimeoutRef.current) {\n                clearTimeout(restartTimeoutRef.current);\n                restartTimeoutRef.current = null;\n            }\n            try {\n                recognitionRef.current.stop();\n                console.log('Speech recognition stopped manually');\n            } catch (err) {\n                console.error('Failed to stop speech recognition:', err);\n                setError('Failed to stop speech recognition');\n            }\n        }\n    }[\"useSpeechRecognition.useCallback[stopListening]\"], []);\n    const resetTranscript = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useSpeechRecognition.useCallback[resetTranscript]\": ()=>{\n            setFinalTranscript('');\n            setInterimTranscript('');\n            setError(null);\n            restartAttemptsRef.current = 0;\n        }\n    }[\"useSpeechRecognition.useCallback[resetTranscript]\"], []);\n    const setLanguage = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useSpeechRecognition.useCallback[setLanguage]\": (language)=>{\n            if (recognitionRef.current) {\n                recognitionRef.current.lang = language;\n                optionsRef.current.language = language;\n            }\n        }\n    }[\"useSpeechRecognition.useCallback[setLanguage]\"], []);\n    // Cleanup on unmount\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)({\n        \"useSpeechRecognition.useEffect\": ()=>{\n            return ({\n                \"useSpeechRecognition.useEffect\": ()=>{\n                    if (restartTimeoutRef.current) {\n                        clearTimeout(restartTimeoutRef.current);\n                    }\n                    if (recognitionRef.current) {\n                        isStoppedManuallyRef.current = true;\n                        try {\n                            recognitionRef.current.stop();\n                        } catch (err) {\n                            console.error('Error stopping recognition on cleanup:', err);\n                        }\n                    }\n                }\n            })[\"useSpeechRecognition.useEffect\"];\n        }\n    }[\"useSpeechRecognition.useEffect\"], []);\n    return {\n        finalTranscript,\n        interimTranscript,\n        isListening,\n        error,\n        isSupported,\n        startListening,\n        stopListening,\n        resetTranscript,\n        setLanguage\n    };\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (useSpeechRecognition);\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/hooks/useSpeechRecognition.ts\n"));

/***/ })

});