/**
 * useSpeechRecognition Hook
 * 
 * React hook for managing speech recognition functionality.
 * Based on the speech recognition implementation from index.html.
 */

import { useState, useRef, useCallback, useEffect } from 'react';

interface SpeechRecognitionOptions {
  language: string;
  continuous: boolean;
  interimResults: boolean;
  maxAlternatives: number;
}

interface SpeechRecognitionResult {
  finalTranscript: string;
  interimTranscript: string;
  isListening: boolean;
  error: string | null;
  isSupported: boolean;
}

interface SpeechRecognitionHook extends SpeechRecognitionResult {
  startListening: () => void;
  stopListening: () => void;
  resetTranscript: () => void;
  setLanguage: (language: string) => void;
}

// Extend Window interface for speech recognition
declare global {
  interface Window {
    SpeechRecognition: any;
    webkitSpeechRecognition: any;
  }
}

const useSpeechRecognition = (
  options: Partial<SpeechRecognitionOptions> = {}
): SpeechRecognitionHook => {
  const defaultOptions: SpeechRecognitionOptions = {
    language: 'en-US',
    continuous: true,
    interimResults: true,
    maxAlternatives: 1,
    ...options
  };

  const [finalTranscript, setFinalTranscript] = useState<string>('');
  const [interimTranscript, setInterimTranscript] = useState<string>('');
  const [isListening, setIsListening] = useState<boolean>(false);
  const [error, setError] = useState<string | null>(null);
  const [isSupported, setIsSupported] = useState<boolean>(false);

  const recognitionRef = useRef<any>(null);
  const optionsRef = useRef<SpeechRecognitionOptions>(defaultOptions);
  const restartTimeoutRef = useRef<NodeJS.Timeout | null>(null);
  const restartAttemptsRef = useRef<number>(0);
  const isStoppedManuallyRef = useRef<boolean>(false);

  const MAX_RESTART_ATTEMPTS = 3;
  const RESTART_DELAY = 1000;

  // Update options ref when options change
  useEffect(() => {
    optionsRef.current = { ...defaultOptions, ...options };
  }, [options.language, options.continuous, options.interimResults, options.maxAlternatives]); // Only depend on specific option values

  // Initialize speech recognition
  useEffect(() => {
    if (typeof window === 'undefined') return;

    const SpeechRecognition = window.SpeechRecognition || window.webkitSpeechRecognition;
    
    if (!SpeechRecognition) {
      setIsSupported(false);
      setError('Speech recognition is not supported in this browser');
      return;
    }

    setIsSupported(true);
    
    const recognition = new SpeechRecognition();
    recognitionRef.current = recognition;

    // Configure recognition
    recognition.continuous = optionsRef.current.continuous;
    recognition.interimResults = optionsRef.current.interimResults;
    recognition.lang = optionsRef.current.language;
    recognition.maxAlternatives = optionsRef.current.maxAlternatives;

    // Event handlers
    recognition.onstart = () => {
      setIsListening(true);
      setError(null);
    };

    recognition.onend = () => {
      console.log('Speech recognition ended');
      setIsListening(false);

      // Auto-restart if not stopped manually and continuous mode is enabled
      if (!isStoppedManuallyRef.current && optionsRef.current.continuous) {
        if (restartAttemptsRef.current < MAX_RESTART_ATTEMPTS) {
          console.log(`Attempting to restart recognition (attempt ${restartAttemptsRef.current + 1})`);
          restartAttemptsRef.current++;

          restartTimeoutRef.current = setTimeout(() => {
            try {
              if (recognitionRef.current && !isStoppedManuallyRef.current) {
                recognitionRef.current.start();
              }
            } catch (err) {
              console.error('Failed to restart recognition:', err);
              setError('Failed to restart speech recognition');
            }
          }, RESTART_DELAY);
        } else {
          setError('Maximum restart attempts reached. Please restart manually.');
        }
      }
    };

    recognition.onerror = (event: any) => {
      console.error('Speech recognition error:', event);

      // Handle different error types based on original implementation
      switch (event.error) {
        case 'no-speech':
          setError('No speech detected. Still listening...');
          break;
        case 'audio-capture':
          setError('No microphone detected. Please check your microphone.');
          setIsListening(false);
          break;
        case 'not-allowed':
          setError('Microphone access denied. Please allow microphone access.');
          setIsListening(false);
          break;
        case 'network':
          setError('Network error occurred. Will attempt to reconnect...');
          // Don't stop listening, let onend handle restart
          break;
        case 'aborted':
          console.log('Recognition aborted');
          if (!isStoppedManuallyRef.current) {
            setError('Recognition was aborted unexpectedly');
          }
          break;
        default:
          setError(`Speech recognition error: ${event.error}`);
          setIsListening(false);
      }
    };

    recognition.onresult = (event: any) => {
      if (typeof event.results === 'undefined') {
        console.log('Undefined results in event:', event);
        return;
      }

      let interim = '';
      let final = '';

      // Process only new results since last event
      for (let i = event.resultIndex; i < event.results.length; i++) {
        const transcript = event.results[i][0].transcript.trim();

        if (event.results[i].isFinal) {
          final += (final ? ' ' : '') + transcript;
        } else {
          interim += (interim ? ' ' : '') + transcript;
        }
      }

      setInterimTranscript(interim);

      if (final) {
        setFinalTranscript(prev => prev + (prev ? ' ' : '') + final);
        // Reset restart attempts on successful recognition
        restartAttemptsRef.current = 0;
      }
    };

    return () => {
      if (recognition) {
        recognition.stop();
      }
    };
  }, []);

  // Update language when it changes
  useEffect(() => {
    if (recognitionRef.current && options.language) {
      recognitionRef.current.lang = options.language;
    }
  }, [options.language]);

  const startListening = useCallback(() => {
    if (!recognitionRef.current || !isSupported) {
      setError('Speech recognition is not available');
      return;
    }

    if (isListening) {
      return;
    }

    // Clear any pending restart timeout
    if (restartTimeoutRef.current) {
      clearTimeout(restartTimeoutRef.current);
      restartTimeoutRef.current = null;
    }

    isStoppedManuallyRef.current = false;
    restartAttemptsRef.current = 0;
    setError(null);

    try {
      recognitionRef.current.start();
      console.log('Speech recognition started');
    } catch (err) {
      console.error('Failed to start speech recognition:', err);
      if (err instanceof Error && err.name === 'InvalidStateError') {
        // Recognition is already running, try to stop and restart
        try {
          recognitionRef.current.stop();
          setTimeout(() => {
            try {
              recognitionRef.current.start();
            } catch (retryErr) {
              setError('Failed to start speech recognition after retry');
            }
          }, 100);
        } catch (stopErr) {
          setError('Failed to restart speech recognition');
        }
      } else {
        setError('Failed to start speech recognition');
      }
    }
  }, [isListening, isSupported]);

  const stopListening = useCallback(() => {
    if (!recognitionRef.current) return;

    isStoppedManuallyRef.current = true;

    // Clear any pending restart timeout
    if (restartTimeoutRef.current) {
      clearTimeout(restartTimeoutRef.current);
      restartTimeoutRef.current = null;
    }

    try {
      recognitionRef.current.stop();
      console.log('Speech recognition stopped manually');
    } catch (err) {
      console.error('Failed to stop speech recognition:', err);
      setError('Failed to stop speech recognition');
    }
  }, []);

  const resetTranscript = useCallback(() => {
    setFinalTranscript('');
    setInterimTranscript('');
    setError(null);
    restartAttemptsRef.current = 0;
  }, []);

  const setLanguage = useCallback((language: string) => {
    if (recognitionRef.current) {
      recognitionRef.current.lang = language;
      optionsRef.current.language = language;
    }
  }, []);

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      if (restartTimeoutRef.current) {
        clearTimeout(restartTimeoutRef.current);
      }
      if (recognitionRef.current) {
        isStoppedManuallyRef.current = true;
        try {
          recognitionRef.current.stop();
        } catch (err) {
          console.error('Error stopping recognition on cleanup:', err);
        }
      }
    };
  }, []);

  return {
    finalTranscript,
    interimTranscript,
    isListening,
    error,
    isSupported,
    startListening,
    stopListening,
    resetTranscript,
    setLanguage
  };
};

export default useSpeechRecognition;
