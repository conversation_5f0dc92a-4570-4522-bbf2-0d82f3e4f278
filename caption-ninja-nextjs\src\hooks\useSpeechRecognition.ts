/**
 * useSpeechRecognition Hook
 * 
 * React hook for managing speech recognition functionality.
 * Based on the speech recognition implementation from index.html.
 */

import { useState, useRef, useCallback, useEffect, useMemo } from 'react';
import logger from '@/utils/logger';

interface SpeechRecognitionOptions {
  language: string;
  continuous: boolean;
  interimResults: boolean;
  maxAlternatives: number;
}

// Speech recognition states for proper state machine
type SpeechRecognitionState = 'idle' | 'starting' | 'listening' | 'stopping' | 'error' | 'restarting';

interface SpeechRecognitionResult {
  finalTranscript: string;
  interimTranscript: string;
  isListening: boolean;
  error: string | null;
  isSupported: boolean;
  recognitionState: SpeechRecognitionState;
}

interface SpeechRecognitionHook extends SpeechRecognitionResult {
  startListening: () => void;
  stopListening: () => void;
  resetTranscript: () => void;
  setLanguage: (language: string) => void;
}

// Extend Window interface for speech recognition
declare global {
  interface Window {
    SpeechRecognition: any;
    webkitSpeechRecognition: any;
  }
}

const useSpeechRecognition = (
  options: Partial<SpeechRecognitionOptions> = {}
): SpeechRecognitionHook => {
  // Memoize default options to prevent recreation on every render
  const defaultOptions = useMemo<SpeechRecognitionOptions>(() => ({
    language: 'en-US',
    continuous: true,
    interimResults: true,
    maxAlternatives: 1,
  }), []);

  // Memoize merged options to prevent infinite re-renders
  const mergedOptions = useMemo<SpeechRecognitionOptions>(() => ({
    ...defaultOptions,
    ...options
  }), [defaultOptions, options.language, options.continuous, options.interimResults, options.maxAlternatives]);

  const [finalTranscript, setFinalTranscript] = useState<string>('');
  const [interimTranscript, setInterimTranscript] = useState<string>('');
  const [isListening, setIsListening] = useState<boolean>(false);
  const [error, setError] = useState<string | null>(null);
  const [isSupported, setIsSupported] = useState<boolean>(false);
  const [recognitionState, setRecognitionState] = useState<SpeechRecognitionState>('idle');

  const recognitionRef = useRef<any>(null);
  const optionsRef = useRef<SpeechRecognitionOptions>(mergedOptions);
  const restartTimeoutRef = useRef<NodeJS.Timeout | null>(null);
  const restartAttemptsRef = useRef<number>(0);
  const isStoppedManuallyRef = useRef<boolean>(false);

  const MAX_RESTART_ATTEMPTS = 3;
  const RESTART_DELAY = 1000;

  // Update options ref when merged options change
  useEffect(() => {
    optionsRef.current = mergedOptions;
  }, [mergedOptions]);

  // Initialize speech recognition
  useEffect(() => {
    if (typeof window === 'undefined') return;

    const SpeechRecognition = window.SpeechRecognition || window.webkitSpeechRecognition;

    if (!SpeechRecognition) {
      setIsSupported(false);
      setError('Speech recognition is not supported in this browser');
      return;
    }

    setIsSupported(true);

    const recognition = new SpeechRecognition();
    recognitionRef.current = recognition;

    // Configure recognition with current merged options
    recognition.continuous = mergedOptions.continuous;
    recognition.interimResults = mergedOptions.interimResults;
    recognition.lang = mergedOptions.language;
    recognition.maxAlternatives = mergedOptions.maxAlternatives;

    // Event handlers with state machine
    recognition.onstart = () => {
      logger.speechRecognition.started(mergedOptions.language);
      logger.speechRecognition.stateChange(recognitionState, 'listening');
      setIsListening(true);
      setRecognitionState('listening');
      setError(null);
      // Reset restart attempts on successful start
      restartAttemptsRef.current = 0;
    };

    recognition.onend = () => {
      logger.speechRecognition.stopped();
      setIsListening(false);

      // Only attempt restart if we're not in stopping state and not stopped manually
      if (recognitionState !== 'stopping' && !isStoppedManuallyRef.current && mergedOptions.continuous) {
        if (restartAttemptsRef.current < MAX_RESTART_ATTEMPTS) {
          logger.speechRecognition.restart(restartAttemptsRef.current + 1);
          setRecognitionState('restarting');
          restartAttemptsRef.current++;

          // Use exponential backoff for restart attempts
          const backoffDelay = RESTART_DELAY * Math.pow(2, restartAttemptsRef.current - 1);

          restartTimeoutRef.current = setTimeout(() => {
            try {
              if (recognitionRef.current && !isStoppedManuallyRef.current && recognitionState !== 'stopping') {
                setRecognitionState('starting');
                recognitionRef.current.start();
              }
            } catch (err) {
              console.error('Failed to restart recognition:', err);
              setError('Failed to restart speech recognition');
              setRecognitionState('error');
            }
          }, backoffDelay);
        } else {
          setError('Maximum restart attempts reached. Please restart manually.');
          setRecognitionState('error');
        }
      } else {
        setRecognitionState('idle');
      }
    };

    recognition.onerror = (event: any) => {
      console.error('Speech recognition error:', event);

      // Handle different error types with state machine
      switch (event.error) {
        case 'no-speech':
          setError('No speech detected. Still listening...');
          // Don't change state for no-speech, it's not a critical error
          break;
        case 'audio-capture':
          setError('No microphone detected. Please check your microphone.');
          setIsListening(false);
          setRecognitionState('error');
          break;
        case 'not-allowed':
          setError('Microphone access denied. Please allow microphone access.');
          setIsListening(false);
          setRecognitionState('error');
          break;
        case 'network':
          setError('Network error occurred. Will attempt to reconnect...');
          setRecognitionState('error');
          // Don't stop listening, let onend handle restart
          break;
        case 'aborted':
          console.log('Recognition aborted');
          if (!isStoppedManuallyRef.current) {
            setError('Recognition was aborted unexpectedly');
            setRecognitionState('error');
          } else {
            setRecognitionState('idle');
          }
          break;
        default:
          setError(`Speech recognition error: ${event.error}`);
          setRecognitionState('error');
          setIsListening(false);
      }
    };

    recognition.onresult = (event: any) => {
      if (typeof event.results === 'undefined') {
        console.log('Undefined results in event:', event);
        return;
      }

      let interim = '';
      let final = '';

      // Process only new results since last event
      for (let i = event.resultIndex; i < event.results.length; i++) {
        const transcript = event.results[i][0].transcript.trim();

        if (event.results[i].isFinal) {
          final += (final ? ' ' : '') + transcript;
        } else {
          interim += (interim ? ' ' : '') + transcript;
        }
      }

      setInterimTranscript(interim);

      if (final) {
        setFinalTranscript(prev => prev + (prev ? ' ' : '') + final);
        // Reset restart attempts on successful recognition
        restartAttemptsRef.current = 0;
      }
    };

    return () => {
      if (recognition) {
        recognition.stop();
      }
    };
  }, [mergedOptions]);

  // Update language when it changes
  useEffect(() => {
    if (recognitionRef.current && mergedOptions.language) {
      recognitionRef.current.lang = mergedOptions.language;
    }
  }, [mergedOptions.language]);

  const startListening = useCallback(() => {
    if (!recognitionRef.current || !isSupported) {
      setError('Speech recognition is not available');
      setRecognitionState('error');
      return;
    }

    // Prevent starting if already in a transitional state
    if (recognitionState === 'starting' || recognitionState === 'restarting' || isListening) {
      return;
    }

    // Clear any pending restart timeout
    if (restartTimeoutRef.current) {
      clearTimeout(restartTimeoutRef.current);
      restartTimeoutRef.current = null;
    }

    isStoppedManuallyRef.current = false;
    restartAttemptsRef.current = 0;
    setError(null);
    setRecognitionState('starting');

    try {
      recognitionRef.current.start();
      console.log('Speech recognition start requested');
    } catch (err) {
      console.error('Failed to start speech recognition:', err);
      if (err instanceof Error && err.name === 'InvalidStateError') {
        // Recognition is already running, try to stop and restart
        try {
          setRecognitionState('stopping');
          recognitionRef.current.stop();
          setTimeout(() => {
            try {
              if (!isStoppedManuallyRef.current) {
                setRecognitionState('starting');
                recognitionRef.current.start();
              }
            } catch (retryErr) {
              setError('Failed to start speech recognition after retry');
              setRecognitionState('error');
            }
          }, 100);
        } catch (stopErr) {
          setError('Failed to restart speech recognition');
          setRecognitionState('error');
        }
      } else {
        setError('Failed to start speech recognition');
        setRecognitionState('error');
      }
    }
  }, [recognitionState, isListening, isSupported]);

  const stopListening = useCallback(() => {
    if (!recognitionRef.current) return;

    isStoppedManuallyRef.current = true;
    setRecognitionState('stopping');

    // Clear any pending restart timeout
    if (restartTimeoutRef.current) {
      clearTimeout(restartTimeoutRef.current);
      restartTimeoutRef.current = null;
    }

    try {
      recognitionRef.current.stop();
      console.log('Speech recognition stopped manually');
    } catch (err) {
      console.error('Failed to stop speech recognition:', err);
      setError('Failed to stop speech recognition');
      setRecognitionState('error');
    }
  }, []);

  const resetTranscript = useCallback(() => {
    setFinalTranscript('');
    setInterimTranscript('');
    setError(null);
    restartAttemptsRef.current = 0;
  }, []);

  const setLanguage = useCallback((language: string) => {
    if (recognitionRef.current) {
      recognitionRef.current.lang = language;
      optionsRef.current.language = language;
    }
  }, []);

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      if (restartTimeoutRef.current) {
        clearTimeout(restartTimeoutRef.current);
      }
      if (recognitionRef.current) {
        isStoppedManuallyRef.current = true;
        try {
          recognitionRef.current.stop();
        } catch (err) {
          console.error('Error stopping recognition on cleanup:', err);
        }
      }
    };
  }, []);

  return {
    finalTranscript,
    interimTranscript,
    isListening,
    error,
    isSupported,
    recognitionState,
    startListening,
    stopListening,
    resetTranscript,
    setLanguage
  };
};

export default useSpeechRecognition;
