/**
 * Logger Utility
 * 
 * Structured logging system for tracking state changes and identifying issues.
 * Provides different log levels and context-aware logging.
 */

export type LogLevel = 'debug' | 'info' | 'warn' | 'error';

export interface LogEntry {
  timestamp: string;
  level: LogLevel;
  category: string;
  message: string;
  data?: any;
  userId?: string;
  sessionId?: string;
}

class Logger {
  private logs: LogEntry[] = [];
  private maxLogs = 1000; // Keep last 1000 logs in memory
  private isDevelopment = process.env.NODE_ENV === 'development';

  private formatTimestamp(): string {
    return new Date().toISOString();
  }

  private shouldLog(level: LogLevel): boolean {
    if (this.isDevelopment) return true;
    
    // In production, only log warnings and errors
    return level === 'warn' || level === 'error';
  }

  private addLog(entry: LogEntry): void {
    this.logs.push(entry);
    
    // Keep only the last maxLogs entries
    if (this.logs.length > this.maxLogs) {
      this.logs = this.logs.slice(-this.maxLogs);
    }

    // Save to localStorage for persistence
    if (typeof window !== 'undefined') {
      try {
        const recentLogs = this.logs.slice(-100); // Keep last 100 in localStorage
        localStorage.setItem('caption-ninja-logs', JSON.stringify(recentLogs));
      } catch (error) {
        console.warn('Failed to save logs to localStorage:', error);
      }
    }
  }

  private log(level: LogLevel, category: string, message: string, data?: any): void {
    if (!this.shouldLog(level)) return;

    const entry: LogEntry = {
      timestamp: this.formatTimestamp(),
      level,
      category,
      message,
      data,
      sessionId: this.getSessionId()
    };

    this.addLog(entry);

    // Console output with appropriate method
    const consoleMethod = level === 'debug' ? 'log' : level;
    const prefix = `[${entry.timestamp}] [${level.toUpperCase()}] [${category}]`;
    
    if (data) {
      console[consoleMethod](prefix, message, data);
    } else {
      console[consoleMethod](prefix, message);
    }
  }

  private getSessionId(): string {
    if (typeof window !== 'undefined') {
      let sessionId = sessionStorage.getItem('caption-ninja-session-id');
      if (!sessionId) {
        sessionId = `session-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
        sessionStorage.setItem('caption-ninja-session-id', sessionId);
      }
      return sessionId;
    }
    return 'server-session';
  }

  // Public logging methods
  debug(category: string, message: string, data?: any): void {
    this.log('debug', category, message, data);
  }

  info(category: string, message: string, data?: any): void {
    this.log('info', category, message, data);
  }

  warn(category: string, message: string, data?: any): void {
    this.log('warn', category, message, data);
  }

  error(category: string, message: string, data?: any): void {
    this.log('error', category, message, data);
  }

  // Specific logging methods for different components
  speechRecognition = {
    started: (language: string) => this.info('SpeechRecognition', `Started recognition for language: ${language}`),
    stopped: () => this.info('SpeechRecognition', 'Stopped recognition'),
    result: (transcript: string, isFinal: boolean) => this.debug('SpeechRecognition', `Result: ${isFinal ? 'Final' : 'Interim'}`, { transcript }),
    error: (error: string) => this.error('SpeechRecognition', `Recognition error: ${error}`),
    restart: (attempt: number) => this.warn('SpeechRecognition', `Restart attempt ${attempt}`),
    stateChange: (from: string, to: string) => this.debug('SpeechRecognition', `State change: ${from} -> ${to}`)
  };

  translation = {
    started: (text: string, from: string, to: string) => this.info('Translation', `Started translation: ${from} -> ${to}`, { text: text.substring(0, 100) }),
    completed: (originalText: string, translatedText: string) => this.info('Translation', 'Translation completed', { originalText: originalText.substring(0, 50), translatedText: translatedText.substring(0, 50) }),
    error: (error: string, text?: string) => this.error('Translation', `Translation error: ${error}`, { text }),
    cached: (text: string) => this.debug('Translation', 'Used cached translation', { text: text.substring(0, 50) }),
    apiCall: (endpoint: string, duration: number) => this.debug('Translation', `API call to ${endpoint} took ${duration}ms`)
  };

  mediaStream = {
    started: (constraints: any) => this.info('MediaStream', 'Media stream started', { constraints }),
    stopped: () => this.info('MediaStream', 'Media stream stopped'),
    error: (error: string) => this.error('MediaStream', `Media stream error: ${error}`),
    deviceChange: (type: string, deviceId: string) => this.info('MediaStream', `Device changed: ${type}`, { deviceId }),
    permissionDenied: () => this.warn('MediaStream', 'Media permission denied'),
    fallback: (attempt: number) => this.warn('MediaStream', `Using fallback constraints (attempt ${attempt})`)
  };

  context = {
    stateUpdate: (action: string, payload?: any) => this.debug('Context', `State update: ${action}`, payload),
    rerender: (component: string, reason?: string) => this.debug('Context', `Component re-render: ${component}`, { reason }),
    error: (component: string, error: string) => this.error('Context', `Context error in ${component}: ${error}`)
  };

  // Utility methods
  getLogs(level?: LogLevel, category?: string): LogEntry[] {
    let filteredLogs = this.logs;

    if (level) {
      filteredLogs = filteredLogs.filter(log => log.level === level);
    }

    if (category) {
      filteredLogs = filteredLogs.filter(log => log.category === category);
    }

    return filteredLogs;
  }

  getRecentLogs(count: number = 50): LogEntry[] {
    return this.logs.slice(-count);
  }

  clearLogs(): void {
    this.logs = [];
    if (typeof window !== 'undefined') {
      localStorage.removeItem('caption-ninja-logs');
    }
  }

  exportLogs(): string {
    return JSON.stringify(this.logs, null, 2);
  }

  // Load logs from localStorage on initialization
  loadPersistedLogs(): void {
    if (typeof window !== 'undefined') {
      try {
        const savedLogs = localStorage.getItem('caption-ninja-logs');
        if (savedLogs) {
          const logs = JSON.parse(savedLogs);
          if (Array.isArray(logs)) {
            this.logs = logs;
          }
        }
      } catch (error) {
        console.warn('Failed to load persisted logs:', error);
      }
    }
  }
}

// Create singleton instance
const logger = new Logger();

// Load persisted logs on initialization
if (typeof window !== 'undefined') {
  logger.loadPersistedLogs();
}

export default logger;

// React hook for using logger in components
export const useLogger = () => {
  return logger;
};

// Performance logging utility
export const performanceLogger = {
  start: (operation: string): (() => void) => {
    const startTime = performance.now();
    logger.debug('Performance', `Started: ${operation}`);
    
    return () => {
      const duration = performance.now() - startTime;
      logger.info('Performance', `Completed: ${operation}`, { duration: `${duration.toFixed(2)}ms` });
    };
  },

  measure: async <T>(operation: string, fn: () => Promise<T>): Promise<T> => {
    const endTimer = performanceLogger.start(operation);
    try {
      const result = await fn();
      endTimer();
      return result;
    } catch (error) {
      endTimer();
      logger.error('Performance', `Failed: ${operation}`, { error });
      throw error;
    }
  }
};
