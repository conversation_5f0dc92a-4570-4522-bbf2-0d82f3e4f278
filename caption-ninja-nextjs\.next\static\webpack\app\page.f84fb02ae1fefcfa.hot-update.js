"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./src/app/page.tsx":
/*!**************************!*\
  !*** ./src/app/page.tsx ***!
  \**************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Home)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var _contexts_CaptionContext__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/contexts/CaptionContext */ \"(app-pages-browser)/./src/contexts/CaptionContext.tsx\");\n/* harmony import */ var _components_SpeechRecognition_SpeechController__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/SpeechRecognition/SpeechController */ \"(app-pages-browser)/./src/components/SpeechRecognition/SpeechController.tsx\");\n/* harmony import */ var _components_VideoPlayer_VideoPlayerWithCaptions__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/VideoPlayer/VideoPlayerWithCaptions */ \"(app-pages-browser)/./src/components/VideoPlayer/VideoPlayerWithCaptions.tsx\");\n/* harmony import */ var _components_VideoPlayer_DeviceSelector__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/VideoPlayer/DeviceSelector */ \"(app-pages-browser)/./src/components/VideoPlayer/DeviceSelector.tsx\");\n/* harmony import */ var _components_Translation_TranslationSettings__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/Translation/TranslationSettings */ \"(app-pages-browser)/./src/components/Translation/TranslationSettings.tsx\");\n/* harmony import */ var _components_ErrorBoundary__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/ErrorBoundary */ \"(app-pages-browser)/./src/components/ErrorBoundary.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\nfunction Home() {\n    _s();\n    const { state } = (0,_contexts_CaptionContext__WEBPACK_IMPORTED_MODULE_1__.useCaption)();\n    const actions = (0,_contexts_CaptionContext__WEBPACK_IMPORTED_MODULE_1__.useCaptionActions)();\n    const selectors = (0,_contexts_CaptionContext__WEBPACK_IMPORTED_MODULE_1__.useCaptionSelectors)();\n    const handleTestTranscript = ()=>{\n        actions.setFinalTranscript(\"Hello, this is a test transcript from CAPTION.Ninja!\");\n        actions.setTranslatedText(\"¡Hola, esta es una transcripción de prueba de CAPTION.Ninja!\");\n    };\n    const handleClearTranscript = ()=>{\n        actions.resetTranscript();\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 p-8\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"max-w-4xl mx-auto\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"header\", {\n                    className: \"text-center mb-8\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                            className: \"text-4xl font-bold text-gray-900 mb-2\",\n                            children: \"CAPTION.Ninja\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive - UGSM-Monarch Business School GmbH\\\\Documents\\\\captionninja\\\\caption-ninja-nextjs\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 28,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-lg text-gray-600\",\n                            children: \"Real-time Speech-to-Text with Translation & Video Streaming\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive - UGSM-Monarch Business School GmbH\\\\Documents\\\\captionninja\\\\caption-ninja-nextjs\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 31,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive - UGSM-Monarch Business School GmbH\\\\Documents\\\\captionninja\\\\caption-ninja-nextjs\\\\src\\\\app\\\\page.tsx\",\n                    lineNumber: 27,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"mb-8\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ErrorBoundary__WEBPACK_IMPORTED_MODULE_6__.VideoPlayerErrorBoundary, {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_VideoPlayer_VideoPlayerWithCaptions__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                            className: \"w-full max-w-4xl mx-auto\",\n                            showControls: true,\n                            autoStart: false,\n                            aspectRatio: \"16:9\",\n                            quality: \"high\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive - UGSM-Monarch Business School GmbH\\\\Documents\\\\captionninja\\\\caption-ninja-nextjs\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 39,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive - UGSM-Monarch Business School GmbH\\\\Documents\\\\captionninja\\\\caption-ninja-nextjs\\\\src\\\\app\\\\page.tsx\",\n                        lineNumber: 38,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive - UGSM-Monarch Business School GmbH\\\\Documents\\\\captionninja\\\\caption-ninja-nextjs\\\\src\\\\app\\\\page.tsx\",\n                    lineNumber: 37,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"grid grid-cols-1 lg:grid-cols-3 gap-6 mb-8\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"lg:col-span-2\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_SpeechRecognition_SpeechController__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {}, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive - UGSM-Monarch Business School GmbH\\\\Documents\\\\captionninja\\\\caption-ninja-nextjs\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 52,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive - UGSM-Monarch Business School GmbH\\\\Documents\\\\captionninja\\\\caption-ninja-nextjs\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 51,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"space-y-6\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"bg-white rounded-lg shadow-md p-6\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                            className: \"text-xl font-semibold text-gray-800 mb-4\",\n                                            children: \"System Status\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive - UGSM-Monarch Business School GmbH\\\\Documents\\\\captionninja\\\\caption-ninja-nextjs\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 59,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"space-y-2 text-sm\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"font-medium\",\n                                                            children: \"Session:\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive - UGSM-Monarch Business School GmbH\\\\Documents\\\\captionninja\\\\caption-ninja-nextjs\\\\src\\\\app\\\\page.tsx\",\n                                                            lineNumber: 64,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        \" \",\n                                                        state.sessionId.split('-').pop()\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive - UGSM-Monarch Business School GmbH\\\\Documents\\\\captionninja\\\\caption-ninja-nextjs\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 63,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"font-medium\",\n                                                            children: \"Recording:\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive - UGSM-Monarch Business School GmbH\\\\Documents\\\\captionninja\\\\caption-ninja-nextjs\\\\src\\\\app\\\\page.tsx\",\n                                                            lineNumber: 67,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        \" \",\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: state.isRecording ? \"text-green-600\" : \"text-red-600\",\n                                                            children: state.isRecording ? \"🔴 Live\" : \"⭕ Stopped\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive - UGSM-Monarch Business School GmbH\\\\Documents\\\\captionninja\\\\caption-ninja-nextjs\\\\src\\\\app\\\\page.tsx\",\n                                                            lineNumber: 68,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive - UGSM-Monarch Business School GmbH\\\\Documents\\\\captionninja\\\\caption-ninja-nextjs\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 66,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"font-medium\",\n                                                            children: \"Translation:\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive - UGSM-Monarch Business School GmbH\\\\Documents\\\\captionninja\\\\caption-ninja-nextjs\\\\src\\\\app\\\\page.tsx\",\n                                                            lineNumber: 73,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        \" \",\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: state.settings.translationEnabled ? \"text-green-600\" : \"text-gray-500\",\n                                                            children: state.settings.translationEnabled ? \"✅ Enabled\" : \"❌ Disabled\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive - UGSM-Monarch Business School GmbH\\\\Documents\\\\captionninja\\\\caption-ninja-nextjs\\\\src\\\\app\\\\page.tsx\",\n                                                            lineNumber: 74,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive - UGSM-Monarch Business School GmbH\\\\Documents\\\\captionninja\\\\caption-ninja-nextjs\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 72,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"font-medium\",\n                                                            children: \"Language:\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive - UGSM-Monarch Business School GmbH\\\\Documents\\\\captionninja\\\\caption-ninja-nextjs\\\\src\\\\app\\\\page.tsx\",\n                                                            lineNumber: 79,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        \" \",\n                                                        state.settings.speechLanguage\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive - UGSM-Monarch Business School GmbH\\\\Documents\\\\captionninja\\\\caption-ninja-nextjs\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 78,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"font-medium\",\n                                                            children: \"History:\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive - UGSM-Monarch Business School GmbH\\\\Documents\\\\captionninja\\\\caption-ninja-nextjs\\\\src\\\\app\\\\page.tsx\",\n                                                            lineNumber: 82,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        \" \",\n                                                        state.transcriptHistory.length,\n                                                        \" entries\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive - UGSM-Monarch Business School GmbH\\\\Documents\\\\captionninja\\\\caption-ninja-nextjs\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 81,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"font-medium\",\n                                                            children: \"Words:\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive - UGSM-Monarch Business School GmbH\\\\Documents\\\\captionninja\\\\caption-ninja-nextjs\\\\src\\\\app\\\\page.tsx\",\n                                                            lineNumber: 85,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        \" \",\n                                                        selectors.getTranscriptWordCount()\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive - UGSM-Monarch Business School GmbH\\\\Documents\\\\captionninja\\\\caption-ninja-nextjs\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 84,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive - UGSM-Monarch Business School GmbH\\\\Documents\\\\captionninja\\\\caption-ninja-nextjs\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 62,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"mt-4 pt-4 border-t border-gray-200\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                    className: \"text-sm font-medium text-gray-700 mb-2\",\n                                                    children: \"Quick Tests\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive - UGSM-Monarch Business School GmbH\\\\Documents\\\\captionninja\\\\caption-ninja-nextjs\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 91,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"space-y-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                            onClick: handleTestTranscript,\n                                                            className: \"w-full px-3 py-2 text-sm bg-blue-500 text-white rounded hover:bg-blue-600 transition-colors\",\n                                                            children: \"Add Test Transcript\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive - UGSM-Monarch Business School GmbH\\\\Documents\\\\captionninja\\\\caption-ninja-nextjs\\\\src\\\\app\\\\page.tsx\",\n                                                            lineNumber: 93,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                            onClick: handleClearTranscript,\n                                                            className: \"w-full px-3 py-2 text-sm bg-red-500 text-white rounded hover:bg-red-600 transition-colors\",\n                                                            children: \"Clear All\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive - UGSM-Monarch Business School GmbH\\\\Documents\\\\captionninja\\\\caption-ninja-nextjs\\\\src\\\\app\\\\page.tsx\",\n                                                            lineNumber: 99,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                            onClick: ()=>actions.updateSettings({\n                                                                    translationEnabled: !state.settings.translationEnabled\n                                                                }),\n                                                            className: \"w-full px-3 py-2 text-sm bg-purple-500 text-white rounded hover:bg-purple-600 transition-colors\",\n                                                            children: \"Toggle Translation\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive - UGSM-Monarch Business School GmbH\\\\Documents\\\\captionninja\\\\caption-ninja-nextjs\\\\src\\\\app\\\\page.tsx\",\n                                                            lineNumber: 105,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive - UGSM-Monarch Business School GmbH\\\\Documents\\\\captionninja\\\\caption-ninja-nextjs\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 92,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive - UGSM-Monarch Business School GmbH\\\\Documents\\\\captionninja\\\\caption-ninja-nextjs\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 90,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive - UGSM-Monarch Business School GmbH\\\\Documents\\\\captionninja\\\\caption-ninja-nextjs\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 58,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"bg-white rounded-lg shadow-md p-6\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                            className: \"text-xl font-semibold text-gray-800 mb-4\",\n                                            children: \"Device Settings\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive - UGSM-Monarch Business School GmbH\\\\Documents\\\\captionninja\\\\caption-ninja-nextjs\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 119,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_VideoPlayer_DeviceSelector__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                            showAudioDevices: true,\n                                            showVideoDevices: true\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive - UGSM-Monarch Business School GmbH\\\\Documents\\\\captionninja\\\\caption-ninja-nextjs\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 122,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive - UGSM-Monarch Business School GmbH\\\\Documents\\\\captionninja\\\\caption-ninja-nextjs\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 118,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"bg-white rounded-lg shadow-md p-6\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Translation_TranslationSettings__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {}, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive - UGSM-Monarch Business School GmbH\\\\Documents\\\\captionninja\\\\caption-ninja-nextjs\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 130,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive - UGSM-Monarch Business School GmbH\\\\Documents\\\\captionninja\\\\caption-ninja-nextjs\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 129,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive - UGSM-Monarch Business School GmbH\\\\Documents\\\\captionninja\\\\caption-ninja-nextjs\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 56,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive - UGSM-Monarch Business School GmbH\\\\Documents\\\\captionninja\\\\caption-ninja-nextjs\\\\src\\\\app\\\\page.tsx\",\n                    lineNumber: 49,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"bg-white rounded-lg shadow-md p-6\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                            className: \"text-xl font-semibold text-gray-800 mb-4\",\n                            children: \"Transcript History\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive - UGSM-Monarch Business School GmbH\\\\Documents\\\\captionninja\\\\caption-ninja-nextjs\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 137,\n                            columnNumber: 11\n                        }, this),\n                        state.transcriptHistory.length > 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"space-y-3 max-h-96 overflow-y-auto\",\n                            children: [\n                                state.transcriptHistory.slice(-10).reverse().map((entry, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"p-3 bg-gray-50 border border-gray-200 rounded-lg\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-start justify-between mb-2\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center gap-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-xs bg-blue-100 text-blue-800 px-2 py-1 rounded\",\n                                                            children: [\n                                                                \"#\",\n                                                                state.transcriptHistory.length - index\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive - UGSM-Monarch Business School GmbH\\\\Documents\\\\captionninja\\\\caption-ninja-nextjs\\\\src\\\\app\\\\page.tsx\",\n                                                            lineNumber: 147,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-xs text-gray-500\",\n                                                            children: new Date(entry.timestamp).toLocaleTimeString()\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive - UGSM-Monarch Business School GmbH\\\\Documents\\\\captionninja\\\\caption-ninja-nextjs\\\\src\\\\app\\\\page.tsx\",\n                                                            lineNumber: 150,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        entry.languageCode && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-xs bg-green-100 text-green-800 px-2 py-1 rounded\",\n                                                            children: entry.languageCode\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive - UGSM-Monarch Business School GmbH\\\\Documents\\\\captionninja\\\\caption-ninja-nextjs\\\\src\\\\app\\\\page.tsx\",\n                                                            lineNumber: 154,\n                                                            columnNumber: 25\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive - UGSM-Monarch Business School GmbH\\\\Documents\\\\captionninja\\\\caption-ninja-nextjs\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 146,\n                                                    columnNumber: 21\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive - UGSM-Monarch Business School GmbH\\\\Documents\\\\captionninja\\\\caption-ninja-nextjs\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 145,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-gray-800 text-sm leading-relaxed\",\n                                                children: entry.text\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive - UGSM-Monarch Business School GmbH\\\\Documents\\\\captionninja\\\\caption-ninja-nextjs\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 160,\n                                                columnNumber: 19\n                                            }, this),\n                                            entry.translatedText && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-blue-600 text-sm mt-2 italic border-l-2 border-blue-200 pl-2\",\n                                                children: entry.translatedText\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive - UGSM-Monarch Business School GmbH\\\\Documents\\\\captionninja\\\\caption-ninja-nextjs\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 162,\n                                                columnNumber: 21\n                                            }, this)\n                                        ]\n                                    }, entry.id, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive - UGSM-Monarch Business School GmbH\\\\Documents\\\\captionninja\\\\caption-ninja-nextjs\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 144,\n                                        columnNumber: 17\n                                    }, this)),\n                                state.transcriptHistory.length > 10 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-center text-sm text-gray-500 py-2\",\n                                    children: [\n                                        \"Showing last 10 entries of \",\n                                        state.transcriptHistory.length,\n                                        \" total\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive - UGSM-Monarch Business School GmbH\\\\Documents\\\\captionninja\\\\caption-ninja-nextjs\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 169,\n                                    columnNumber: 17\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive - UGSM-Monarch Business School GmbH\\\\Documents\\\\captionninja\\\\caption-ninja-nextjs\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 142,\n                            columnNumber: 13\n                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-center py-8 text-gray-500\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-4xl mb-2\",\n                                    children: \"\\uD83C\\uDFA4\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive - UGSM-Monarch Business School GmbH\\\\Documents\\\\captionninja\\\\caption-ninja-nextjs\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 176,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    children: \"No transcript history yet\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive - UGSM-Monarch Business School GmbH\\\\Documents\\\\captionninja\\\\caption-ninja-nextjs\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 177,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-sm mt-1\",\n                                    children: \"Start recording to see your speech transcribed here\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive - UGSM-Monarch Business School GmbH\\\\Documents\\\\captionninja\\\\caption-ninja-nextjs\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 178,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive - UGSM-Monarch Business School GmbH\\\\Documents\\\\captionninja\\\\caption-ninja-nextjs\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 175,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive - UGSM-Monarch Business School GmbH\\\\Documents\\\\captionninja\\\\caption-ninja-nextjs\\\\src\\\\app\\\\page.tsx\",\n                    lineNumber: 136,\n                    columnNumber: 9\n                }, this),\n                (state.speechError || state.translationError || state.ttsError) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"bg-red-50 border border-red-200 rounded-lg p-4\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                            className: \"font-medium text-red-800 mb-2\",\n                            children: \"Errors:\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive - UGSM-Monarch Business School GmbH\\\\Documents\\\\captionninja\\\\caption-ninja-nextjs\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 186,\n                            columnNumber: 13\n                        }, this),\n                        state.speechError && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-red-700 text-sm\",\n                            children: [\n                                \"Speech: \",\n                                state.speechError\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive - UGSM-Monarch Business School GmbH\\\\Documents\\\\captionninja\\\\caption-ninja-nextjs\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 188,\n                            columnNumber: 15\n                        }, this),\n                        state.translationError && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-red-700 text-sm\",\n                            children: [\n                                \"Translation: \",\n                                state.translationError\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive - UGSM-Monarch Business School GmbH\\\\Documents\\\\captionninja\\\\caption-ninja-nextjs\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 191,\n                            columnNumber: 15\n                        }, this),\n                        state.ttsError && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-red-700 text-sm\",\n                            children: [\n                                \"TTS: \",\n                                state.ttsError\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive - UGSM-Monarch Business School GmbH\\\\Documents\\\\captionninja\\\\caption-ninja-nextjs\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 194,\n                            columnNumber: 15\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive - UGSM-Monarch Business School GmbH\\\\Documents\\\\captionninja\\\\caption-ninja-nextjs\\\\src\\\\app\\\\page.tsx\",\n                    lineNumber: 185,\n                    columnNumber: 11\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"footer\", {\n                    className: \"text-center text-gray-600 text-sm\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            children: \"CAPTION.Ninja Next.js Migration - Context Testing Page\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive - UGSM-Monarch Business School GmbH\\\\Documents\\\\captionninja\\\\caption-ninja-nextjs\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 200,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"mt-1\",\n                            children: \"This page demonstrates the centralized state management system\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive - UGSM-Monarch Business School GmbH\\\\Documents\\\\captionninja\\\\caption-ninja-nextjs\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 201,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive - UGSM-Monarch Business School GmbH\\\\Documents\\\\captionninja\\\\caption-ninja-nextjs\\\\src\\\\app\\\\page.tsx\",\n                    lineNumber: 199,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive - UGSM-Monarch Business School GmbH\\\\Documents\\\\captionninja\\\\caption-ninja-nextjs\\\\src\\\\app\\\\page.tsx\",\n            lineNumber: 26,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive - UGSM-Monarch Business School GmbH\\\\Documents\\\\captionninja\\\\caption-ninja-nextjs\\\\src\\\\app\\\\page.tsx\",\n        lineNumber: 25,\n        columnNumber: 5\n    }, this);\n}\n_s(Home, \"UKz95Ypqnn+5m1AMQ0CsYbmEMtQ=\", false, function() {\n    return [\n        _contexts_CaptionContext__WEBPACK_IMPORTED_MODULE_1__.useCaption,\n        _contexts_CaptionContext__WEBPACK_IMPORTED_MODULE_1__.useCaptionActions,\n        _contexts_CaptionContext__WEBPACK_IMPORTED_MODULE_1__.useCaptionSelectors\n    ];\n});\n_c = Home;\nvar _c;\n$RefreshReg$(_c, \"Home\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/page.tsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/components/ErrorBoundary.tsx":
/*!******************************************!*\
  !*** ./src/components/ErrorBoundary.tsx ***!
  \******************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   SpeechRecognitionErrorBoundary: () => (/* binding */ SpeechRecognitionErrorBoundary),\n/* harmony export */   TranslationErrorBoundary: () => (/* binding */ TranslationErrorBoundary),\n/* harmony export */   VideoPlayerErrorBoundary: () => (/* binding */ VideoPlayerErrorBoundary),\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__),\n/* harmony export */   useErrorHandler: () => (/* binding */ useErrorHandler)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/**\n * ErrorBoundary Component\n * \n * React Error Boundary to catch and handle component crashes gracefully.\n * Prevents complete application crashes and provides user-friendly error messages.\n */ /* __next_internal_client_entry_do_not_use__ default,useErrorHandler,SpeechRecognitionErrorBoundary,VideoPlayerErrorBoundary,TranslationErrorBoundary auto */ \nvar _s = $RefreshSig$();\n\nclass ErrorBoundary extends react__WEBPACK_IMPORTED_MODULE_1__.Component {\n    static getDerivedStateFromError(error) {\n        // Update state so the next render will show the fallback UI\n        return {\n            hasError: true,\n            error,\n            errorInfo: null\n        };\n    }\n    componentDidCatch(error, errorInfo) {\n        // Log error details\n        console.error('ErrorBoundary caught an error:', error, errorInfo);\n        this.setState({\n            error,\n            errorInfo\n        });\n        // Call custom error handler if provided\n        if (this.props.onError) {\n            this.props.onError(error, errorInfo);\n        }\n        // Log to external service in production\n        if (false) {}\n    }\n    render() {\n        if (this.state.hasError) {\n            var _this_state_errorInfo;\n            // Custom fallback UI\n            if (this.props.fallback) {\n                return this.props.fallback;\n            }\n            // Default error UI\n            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"min-h-screen bg-red-50 flex items-center justify-center p-8\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"max-w-md w-full bg-white rounded-lg shadow-lg p-6\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center mb-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex-shrink-0\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                        className: \"h-8 w-8 text-red-500\",\n                                        fill: \"none\",\n                                        viewBox: \"0 0 24 24\",\n                                        stroke: \"currentColor\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                            strokeLinecap: \"round\",\n                                            strokeLinejoin: \"round\",\n                                            strokeWidth: 2,\n                                            d: \"M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive - UGSM-Monarch Business School GmbH\\\\Documents\\\\captionninja\\\\caption-ninja-nextjs\\\\src\\\\components\\\\ErrorBoundary.tsx\",\n                                            lineNumber: 90,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive - UGSM-Monarch Business School GmbH\\\\Documents\\\\captionninja\\\\caption-ninja-nextjs\\\\src\\\\components\\\\ErrorBoundary.tsx\",\n                                        lineNumber: 89,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive - UGSM-Monarch Business School GmbH\\\\Documents\\\\captionninja\\\\caption-ninja-nextjs\\\\src\\\\components\\\\ErrorBoundary.tsx\",\n                                    lineNumber: 88,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"ml-3\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"text-lg font-medium text-red-800\",\n                                        children: \"Something went wrong\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive - UGSM-Monarch Business School GmbH\\\\Documents\\\\captionninja\\\\caption-ninja-nextjs\\\\src\\\\components\\\\ErrorBoundary.tsx\",\n                                        lineNumber: 94,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive - UGSM-Monarch Business School GmbH\\\\Documents\\\\captionninja\\\\caption-ninja-nextjs\\\\src\\\\components\\\\ErrorBoundary.tsx\",\n                                    lineNumber: 93,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive - UGSM-Monarch Business School GmbH\\\\Documents\\\\captionninja\\\\caption-ninja-nextjs\\\\src\\\\components\\\\ErrorBoundary.tsx\",\n                            lineNumber: 87,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"mb-4\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-sm text-red-700\",\n                                children: \"An unexpected error occurred in the application. This has been logged and will be investigated.\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive - UGSM-Monarch Business School GmbH\\\\Documents\\\\captionninja\\\\caption-ninja-nextjs\\\\src\\\\components\\\\ErrorBoundary.tsx\",\n                                lineNumber: 101,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive - UGSM-Monarch Business School GmbH\\\\Documents\\\\captionninja\\\\caption-ninja-nextjs\\\\src\\\\components\\\\ErrorBoundary.tsx\",\n                            lineNumber: 100,\n                            columnNumber: 13\n                        }, this),\n                         true && this.state.error && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"mb-4 p-3 bg-red-100 rounded text-xs\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"details\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"summary\", {\n                                        className: \"cursor-pointer font-medium text-red-800 mb-2\",\n                                        children: \"Error Details (Development Only)\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive - UGSM-Monarch Business School GmbH\\\\Documents\\\\captionninja\\\\caption-ninja-nextjs\\\\src\\\\components\\\\ErrorBoundary.tsx\",\n                                        lineNumber: 109,\n                                        columnNumber: 19\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-red-700 whitespace-pre-wrap\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                children: \"Error:\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive - UGSM-Monarch Business School GmbH\\\\Documents\\\\captionninja\\\\caption-ninja-nextjs\\\\src\\\\components\\\\ErrorBoundary.tsx\",\n                                                lineNumber: 113,\n                                                columnNumber: 21\n                                            }, this),\n                                            \" \",\n                                            this.state.error.toString(),\n                                            this.state.error.stack && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"br\", {}, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive - UGSM-Monarch Business School GmbH\\\\Documents\\\\captionninja\\\\caption-ninja-nextjs\\\\src\\\\components\\\\ErrorBoundary.tsx\",\n                                                        lineNumber: 116,\n                                                        columnNumber: 25\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"br\", {}, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive - UGSM-Monarch Business School GmbH\\\\Documents\\\\captionninja\\\\caption-ninja-nextjs\\\\src\\\\components\\\\ErrorBoundary.tsx\",\n                                                        lineNumber: 116,\n                                                        columnNumber: 31\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                        children: \"Stack Trace:\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive - UGSM-Monarch Business School GmbH\\\\Documents\\\\captionninja\\\\caption-ninja-nextjs\\\\src\\\\components\\\\ErrorBoundary.tsx\",\n                                                        lineNumber: 117,\n                                                        columnNumber: 25\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"br\", {}, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive - UGSM-Monarch Business School GmbH\\\\Documents\\\\captionninja\\\\caption-ninja-nextjs\\\\src\\\\components\\\\ErrorBoundary.tsx\",\n                                                        lineNumber: 118,\n                                                        columnNumber: 25\n                                                    }, this),\n                                                    this.state.error.stack\n                                                ]\n                                            }, void 0, true),\n                                            ((_this_state_errorInfo = this.state.errorInfo) === null || _this_state_errorInfo === void 0 ? void 0 : _this_state_errorInfo.componentStack) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"br\", {}, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive - UGSM-Monarch Business School GmbH\\\\Documents\\\\captionninja\\\\caption-ninja-nextjs\\\\src\\\\components\\\\ErrorBoundary.tsx\",\n                                                        lineNumber: 124,\n                                                        columnNumber: 25\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"br\", {}, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive - UGSM-Monarch Business School GmbH\\\\Documents\\\\captionninja\\\\caption-ninja-nextjs\\\\src\\\\components\\\\ErrorBoundary.tsx\",\n                                                        lineNumber: 124,\n                                                        columnNumber: 31\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                        children: \"Component Stack:\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive - UGSM-Monarch Business School GmbH\\\\Documents\\\\captionninja\\\\caption-ninja-nextjs\\\\src\\\\components\\\\ErrorBoundary.tsx\",\n                                                        lineNumber: 125,\n                                                        columnNumber: 25\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"br\", {}, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive - UGSM-Monarch Business School GmbH\\\\Documents\\\\captionninja\\\\caption-ninja-nextjs\\\\src\\\\components\\\\ErrorBoundary.tsx\",\n                                                        lineNumber: 126,\n                                                        columnNumber: 25\n                                                    }, this),\n                                                    this.state.errorInfo.componentStack\n                                                ]\n                                            }, void 0, true)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive - UGSM-Monarch Business School GmbH\\\\Documents\\\\captionninja\\\\caption-ninja-nextjs\\\\src\\\\components\\\\ErrorBoundary.tsx\",\n                                        lineNumber: 112,\n                                        columnNumber: 19\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive - UGSM-Monarch Business School GmbH\\\\Documents\\\\captionninja\\\\caption-ninja-nextjs\\\\src\\\\components\\\\ErrorBoundary.tsx\",\n                                lineNumber: 108,\n                                columnNumber: 17\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive - UGSM-Monarch Business School GmbH\\\\Documents\\\\captionninja\\\\caption-ninja-nextjs\\\\src\\\\components\\\\ErrorBoundary.tsx\",\n                            lineNumber: 107,\n                            columnNumber: 15\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex space-x-3\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: this.handleRetry,\n                                    className: \"flex-1 bg-red-600 text-white px-4 py-2 rounded-md text-sm font-medium hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-red-500 focus:ring-offset-2 transition-colors\",\n                                    children: \"Try Again\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive - UGSM-Monarch Business School GmbH\\\\Documents\\\\captionninja\\\\caption-ninja-nextjs\\\\src\\\\components\\\\ErrorBoundary.tsx\",\n                                    lineNumber: 136,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: ()=>window.location.reload(),\n                                    className: \"flex-1 bg-gray-600 text-white px-4 py-2 rounded-md text-sm font-medium hover:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-gray-500 focus:ring-offset-2 transition-colors\",\n                                    children: \"Reload Page\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive - UGSM-Monarch Business School GmbH\\\\Documents\\\\captionninja\\\\caption-ninja-nextjs\\\\src\\\\components\\\\ErrorBoundary.tsx\",\n                                    lineNumber: 142,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive - UGSM-Monarch Business School GmbH\\\\Documents\\\\captionninja\\\\caption-ninja-nextjs\\\\src\\\\components\\\\ErrorBoundary.tsx\",\n                            lineNumber: 135,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"mt-4 text-center\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-xs text-gray-500\",\n                                children: \"If this problem persists, please contact support.\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive - UGSM-Monarch Business School GmbH\\\\Documents\\\\captionninja\\\\caption-ninja-nextjs\\\\src\\\\components\\\\ErrorBoundary.tsx\",\n                                lineNumber: 151,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive - UGSM-Monarch Business School GmbH\\\\Documents\\\\captionninja\\\\caption-ninja-nextjs\\\\src\\\\components\\\\ErrorBoundary.tsx\",\n                            lineNumber: 150,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive - UGSM-Monarch Business School GmbH\\\\Documents\\\\captionninja\\\\caption-ninja-nextjs\\\\src\\\\components\\\\ErrorBoundary.tsx\",\n                    lineNumber: 86,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive - UGSM-Monarch Business School GmbH\\\\Documents\\\\captionninja\\\\caption-ninja-nextjs\\\\src\\\\components\\\\ErrorBoundary.tsx\",\n                lineNumber: 85,\n                columnNumber: 9\n            }, this);\n        }\n        return this.props.children;\n    }\n    constructor(props){\n        super(props), this.handleRetry = ()=>{\n            this.setState({\n                hasError: false,\n                error: null,\n                errorInfo: null\n            });\n        };\n        this.state = {\n            hasError: false,\n            error: null,\n            errorInfo: null\n        };\n    }\n}\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (ErrorBoundary);\n// Hook version for functional components\nconst useErrorHandler = ()=>{\n    _s();\n    const handleError = react__WEBPACK_IMPORTED_MODULE_1___default().useCallback({\n        \"useErrorHandler.useCallback[handleError]\": (error, errorInfo)=>{\n            console.error('Error caught by useErrorHandler:', error, errorInfo);\n            // Log to external service in production\n            if (false) {}\n        }\n    }[\"useErrorHandler.useCallback[handleError]\"], []);\n    return {\n        handleError\n    };\n};\n_s(useErrorHandler, \"5rbwmV++WeaxnfeHb7i5GqkumM4=\");\n// Specific error boundaries for different sections\nconst SpeechRecognitionErrorBoundary = (param)=>{\n    let { children } = param;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ErrorBoundary, {\n        onError: (error, errorInfo)=>{\n            console.error('Speech Recognition Error:', error, errorInfo);\n        },\n        fallback: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"p-4 bg-red-50 border border-red-200 rounded-lg\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                    className: \"text-red-800 font-medium mb-2\",\n                    children: \"Speech Recognition Error\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive - UGSM-Monarch Business School GmbH\\\\Documents\\\\captionninja\\\\caption-ninja-nextjs\\\\src\\\\components\\\\ErrorBoundary.tsx\",\n                    lineNumber: 193,\n                    columnNumber: 9\n                }, void 0),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                    className: \"text-red-700 text-sm mb-3\",\n                    children: \"There was an issue with the speech recognition system. Please try refreshing the page.\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive - UGSM-Monarch Business School GmbH\\\\Documents\\\\captionninja\\\\caption-ninja-nextjs\\\\src\\\\components\\\\ErrorBoundary.tsx\",\n                    lineNumber: 194,\n                    columnNumber: 9\n                }, void 0),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                    onClick: ()=>window.location.reload(),\n                    className: \"px-3 py-2 bg-red-600 text-white rounded text-sm hover:bg-red-700 transition-colors\",\n                    children: \"Refresh Page\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive - UGSM-Monarch Business School GmbH\\\\Documents\\\\captionninja\\\\caption-ninja-nextjs\\\\src\\\\components\\\\ErrorBoundary.tsx\",\n                    lineNumber: 197,\n                    columnNumber: 9\n                }, void 0)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive - UGSM-Monarch Business School GmbH\\\\Documents\\\\captionninja\\\\caption-ninja-nextjs\\\\src\\\\components\\\\ErrorBoundary.tsx\",\n            lineNumber: 192,\n            columnNumber: 7\n        }, void 0),\n        children: children\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive - UGSM-Monarch Business School GmbH\\\\Documents\\\\captionninja\\\\caption-ninja-nextjs\\\\src\\\\components\\\\ErrorBoundary.tsx\",\n        lineNumber: 187,\n        columnNumber: 3\n    }, undefined);\n};\n_c = SpeechRecognitionErrorBoundary;\nconst VideoPlayerErrorBoundary = (param)=>{\n    let { children } = param;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ErrorBoundary, {\n        onError: (error, errorInfo)=>{\n            console.error('Video Player Error:', error, errorInfo);\n        },\n        fallback: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"p-4 bg-red-50 border border-red-200 rounded-lg\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                    className: \"text-red-800 font-medium mb-2\",\n                    children: \"Video Player Error\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive - UGSM-Monarch Business School GmbH\\\\Documents\\\\captionninja\\\\caption-ninja-nextjs\\\\src\\\\components\\\\ErrorBoundary.tsx\",\n                    lineNumber: 217,\n                    columnNumber: 9\n                }, void 0),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                    className: \"text-red-700 text-sm mb-3\",\n                    children: \"There was an issue with the video player. Please check your camera permissions and try again.\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive - UGSM-Monarch Business School GmbH\\\\Documents\\\\captionninja\\\\caption-ninja-nextjs\\\\src\\\\components\\\\ErrorBoundary.tsx\",\n                    lineNumber: 218,\n                    columnNumber: 9\n                }, void 0),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                    onClick: ()=>window.location.reload(),\n                    className: \"px-3 py-2 bg-red-600 text-white rounded text-sm hover:bg-red-700 transition-colors\",\n                    children: \"Refresh Page\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive - UGSM-Monarch Business School GmbH\\\\Documents\\\\captionninja\\\\caption-ninja-nextjs\\\\src\\\\components\\\\ErrorBoundary.tsx\",\n                    lineNumber: 221,\n                    columnNumber: 9\n                }, void 0)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive - UGSM-Monarch Business School GmbH\\\\Documents\\\\captionninja\\\\caption-ninja-nextjs\\\\src\\\\components\\\\ErrorBoundary.tsx\",\n            lineNumber: 216,\n            columnNumber: 7\n        }, void 0),\n        children: children\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive - UGSM-Monarch Business School GmbH\\\\Documents\\\\captionninja\\\\caption-ninja-nextjs\\\\src\\\\components\\\\ErrorBoundary.tsx\",\n        lineNumber: 211,\n        columnNumber: 3\n    }, undefined);\n};\n_c1 = VideoPlayerErrorBoundary;\nconst TranslationErrorBoundary = (param)=>{\n    let { children } = param;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ErrorBoundary, {\n        onError: (error, errorInfo)=>{\n            console.error('Translation Error:', error, errorInfo);\n        },\n        fallback: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"p-4 bg-red-50 border border-red-200 rounded-lg\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                    className: \"text-red-800 font-medium mb-2\",\n                    children: \"Translation Error\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive - UGSM-Monarch Business School GmbH\\\\Documents\\\\captionninja\\\\caption-ninja-nextjs\\\\src\\\\components\\\\ErrorBoundary.tsx\",\n                    lineNumber: 241,\n                    columnNumber: 9\n                }, void 0),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                    className: \"text-red-700 text-sm mb-3\",\n                    children: \"There was an issue with the translation system. Please check your API key and try again.\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive - UGSM-Monarch Business School GmbH\\\\Documents\\\\captionninja\\\\caption-ninja-nextjs\\\\src\\\\components\\\\ErrorBoundary.tsx\",\n                    lineNumber: 242,\n                    columnNumber: 9\n                }, void 0),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                    onClick: ()=>window.location.reload(),\n                    className: \"px-3 py-2 bg-red-600 text-white rounded text-sm hover:bg-red-700 transition-colors\",\n                    children: \"Refresh Page\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive - UGSM-Monarch Business School GmbH\\\\Documents\\\\captionninja\\\\caption-ninja-nextjs\\\\src\\\\components\\\\ErrorBoundary.tsx\",\n                    lineNumber: 245,\n                    columnNumber: 9\n                }, void 0)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive - UGSM-Monarch Business School GmbH\\\\Documents\\\\captionninja\\\\caption-ninja-nextjs\\\\src\\\\components\\\\ErrorBoundary.tsx\",\n            lineNumber: 240,\n            columnNumber: 7\n        }, void 0),\n        children: children\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive - UGSM-Monarch Business School GmbH\\\\Documents\\\\captionninja\\\\caption-ninja-nextjs\\\\src\\\\components\\\\ErrorBoundary.tsx\",\n        lineNumber: 235,\n        columnNumber: 3\n    }, undefined);\n};\n_c2 = TranslationErrorBoundary;\nvar _c, _c1, _c2;\n$RefreshReg$(_c, \"SpeechRecognitionErrorBoundary\");\n$RefreshReg$(_c1, \"VideoPlayerErrorBoundary\");\n$RefreshReg$(_c2, \"TranslationErrorBoundary\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/ErrorBoundary.tsx\n"));

/***/ })

});