"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./src/hooks/useSpeechRecognition.ts":
/*!*******************************************!*\
  !*** ./src/hooks/useSpeechRecognition.ts ***!
  \*******************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _utils_logger__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/utils/logger */ \"(app-pages-browser)/./src/utils/logger.ts\");\n/**\n * useSpeechRecognition Hook\n * \n * React hook for managing speech recognition functionality.\n * Based on the speech recognition implementation from index.html.\n */ \n\nconst useSpeechRecognition = function() {\n    let options = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : {};\n    // Memoize default options to prevent recreation on every render\n    const defaultOptions = (0,react__WEBPACK_IMPORTED_MODULE_0__.useMemo)({\n        \"useSpeechRecognition.useMemo[defaultOptions]\": ()=>({\n                language: 'en-US',\n                continuous: true,\n                interimResults: true,\n                maxAlternatives: 1\n            })\n    }[\"useSpeechRecognition.useMemo[defaultOptions]\"], []);\n    // Memoize merged options to prevent infinite re-renders\n    const mergedOptions = (0,react__WEBPACK_IMPORTED_MODULE_0__.useMemo)({\n        \"useSpeechRecognition.useMemo[mergedOptions]\": ()=>({\n                ...defaultOptions,\n                ...options\n            })\n    }[\"useSpeechRecognition.useMemo[mergedOptions]\"], [\n        defaultOptions,\n        options.language,\n        options.continuous,\n        options.interimResults,\n        options.maxAlternatives\n    ]);\n    const [finalTranscript, setFinalTranscript] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)('');\n    const [interimTranscript, setInterimTranscript] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)('');\n    const [isListening, setIsListening] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(false);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(null);\n    const [isSupported, setIsSupported] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(false);\n    const [recognitionState, setRecognitionState] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)('idle');\n    const recognitionRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(null);\n    const optionsRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(mergedOptions);\n    const restartTimeoutRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(null);\n    const restartAttemptsRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(0);\n    const isStoppedManuallyRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(false);\n    const MAX_RESTART_ATTEMPTS = 3;\n    const RESTART_DELAY = 1000;\n    // Update options ref when merged options change\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)({\n        \"useSpeechRecognition.useEffect\": ()=>{\n            optionsRef.current = mergedOptions;\n        }\n    }[\"useSpeechRecognition.useEffect\"], [\n        mergedOptions\n    ]);\n    // Initialize speech recognition\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)({\n        \"useSpeechRecognition.useEffect\": ()=>{\n            if (false) {}\n            const SpeechRecognition = window.SpeechRecognition || window.webkitSpeechRecognition;\n            if (!SpeechRecognition) {\n                setIsSupported(false);\n                setError('Speech recognition is not supported in this browser');\n                return;\n            }\n            setIsSupported(true);\n            const recognition = new SpeechRecognition();\n            recognitionRef.current = recognition;\n            // Configure recognition with current merged options\n            recognition.continuous = mergedOptions.continuous;\n            recognition.interimResults = mergedOptions.interimResults;\n            recognition.lang = mergedOptions.language;\n            recognition.maxAlternatives = mergedOptions.maxAlternatives;\n            // Event handlers with state machine\n            recognition.onstart = ({\n                \"useSpeechRecognition.useEffect\": ()=>{\n                    _utils_logger__WEBPACK_IMPORTED_MODULE_1__[\"default\"].speechRecognition.started(mergedOptions.language);\n                    _utils_logger__WEBPACK_IMPORTED_MODULE_1__[\"default\"].speechRecognition.stateChange(recognitionState, 'listening');\n                    setIsListening(true);\n                    setRecognitionState('listening');\n                    setError(null);\n                    // Reset restart attempts on successful start\n                    restartAttemptsRef.current = 0;\n                }\n            })[\"useSpeechRecognition.useEffect\"];\n            recognition.onend = ({\n                \"useSpeechRecognition.useEffect\": ()=>{\n                    _utils_logger__WEBPACK_IMPORTED_MODULE_1__[\"default\"].speechRecognition.stopped();\n                    setIsListening(false);\n                    // Only attempt restart if we're not in stopping state and not stopped manually\n                    if (recognitionState !== 'stopping' && !isStoppedManuallyRef.current && mergedOptions.continuous) {\n                        if (restartAttemptsRef.current < MAX_RESTART_ATTEMPTS) {\n                            _utils_logger__WEBPACK_IMPORTED_MODULE_1__[\"default\"].speechRecognition.restart(restartAttemptsRef.current + 1);\n                            setRecognitionState('restarting');\n                            restartAttemptsRef.current++;\n                            // Use exponential backoff for restart attempts\n                            const backoffDelay = RESTART_DELAY * Math.pow(2, restartAttemptsRef.current - 1);\n                            restartTimeoutRef.current = setTimeout({\n                                \"useSpeechRecognition.useEffect\": ()=>{\n                                    try {\n                                        if (recognitionRef.current && !isStoppedManuallyRef.current && recognitionState !== 'stopping') {\n                                            setRecognitionState('starting');\n                                            recognitionRef.current.start();\n                                        }\n                                    } catch (err) {\n                                        console.error('Failed to restart recognition:', err);\n                                        setError('Failed to restart speech recognition');\n                                        setRecognitionState('error');\n                                    }\n                                }\n                            }[\"useSpeechRecognition.useEffect\"], backoffDelay);\n                        } else {\n                            setError('Maximum restart attempts reached. Please restart manually.');\n                            setRecognitionState('error');\n                        }\n                    } else {\n                        setRecognitionState('idle');\n                    }\n                }\n            })[\"useSpeechRecognition.useEffect\"];\n            recognition.onerror = ({\n                \"useSpeechRecognition.useEffect\": (event)=>{\n                    console.error('Speech recognition error:', event);\n                    // Handle different error types with state machine\n                    switch(event.error){\n                        case 'no-speech':\n                            setError('No speech detected. Still listening...');\n                            break;\n                        case 'audio-capture':\n                            setError('No microphone detected. Please check your microphone.');\n                            setIsListening(false);\n                            setRecognitionState('error');\n                            break;\n                        case 'not-allowed':\n                            setError('Microphone access denied. Please allow microphone access.');\n                            setIsListening(false);\n                            setRecognitionState('error');\n                            break;\n                        case 'network':\n                            setError('Network error occurred. Will attempt to reconnect...');\n                            setRecognitionState('error');\n                            break;\n                        case 'aborted':\n                            console.log('Recognition aborted');\n                            if (!isStoppedManuallyRef.current) {\n                                setError('Recognition was aborted unexpectedly');\n                                setRecognitionState('error');\n                            } else {\n                                setRecognitionState('idle');\n                            }\n                            break;\n                        default:\n                            setError(\"Speech recognition error: \".concat(event.error));\n                            setRecognitionState('error');\n                            setIsListening(false);\n                    }\n                }\n            })[\"useSpeechRecognition.useEffect\"];\n            recognition.onresult = ({\n                \"useSpeechRecognition.useEffect\": (event)=>{\n                    if (typeof event.results === 'undefined') {\n                        console.log('Undefined results in event:', event);\n                        return;\n                    }\n                    let interim = '';\n                    let final = '';\n                    // Process only new results since last event\n                    for(let i = event.resultIndex; i < event.results.length; i++){\n                        const transcript = event.results[i][0].transcript.trim();\n                        if (event.results[i].isFinal) {\n                            final += (final ? ' ' : '') + transcript;\n                        } else {\n                            interim += (interim ? ' ' : '') + transcript;\n                        }\n                    }\n                    setInterimTranscript(interim);\n                    if (final) {\n                        setFinalTranscript({\n                            \"useSpeechRecognition.useEffect\": (prev)=>prev + (prev ? ' ' : '') + final\n                        }[\"useSpeechRecognition.useEffect\"]);\n                        // Reset restart attempts on successful recognition\n                        restartAttemptsRef.current = 0;\n                    }\n                }\n            })[\"useSpeechRecognition.useEffect\"];\n            return ({\n                \"useSpeechRecognition.useEffect\": ()=>{\n                    if (recognition) {\n                        recognition.stop();\n                    }\n                }\n            })[\"useSpeechRecognition.useEffect\"];\n        }\n    }[\"useSpeechRecognition.useEffect\"], [\n        mergedOptions\n    ]);\n    // Update language when it changes\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)({\n        \"useSpeechRecognition.useEffect\": ()=>{\n            if (recognitionRef.current && mergedOptions.language) {\n                recognitionRef.current.lang = mergedOptions.language;\n            }\n        }\n    }[\"useSpeechRecognition.useEffect\"], [\n        mergedOptions.language\n    ]);\n    const startListening = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useSpeechRecognition.useCallback[startListening]\": ()=>{\n            if (!recognitionRef.current || !isSupported) {\n                setError('Speech recognition is not available');\n                setRecognitionState('error');\n                return;\n            }\n            // Prevent starting if already in a transitional state\n            if (recognitionState === 'starting' || recognitionState === 'restarting' || isListening) {\n                return;\n            }\n            // Clear any pending restart timeout\n            if (restartTimeoutRef.current) {\n                clearTimeout(restartTimeoutRef.current);\n                restartTimeoutRef.current = null;\n            }\n            isStoppedManuallyRef.current = false;\n            restartAttemptsRef.current = 0;\n            setError(null);\n            setRecognitionState('starting');\n            try {\n                recognitionRef.current.start();\n                console.log('Speech recognition start requested');\n            } catch (err) {\n                console.error('Failed to start speech recognition:', err);\n                if (err instanceof Error && err.name === 'InvalidStateError') {\n                    // Recognition is already running, try to stop and restart\n                    try {\n                        setRecognitionState('stopping');\n                        recognitionRef.current.stop();\n                        setTimeout({\n                            \"useSpeechRecognition.useCallback[startListening]\": ()=>{\n                                try {\n                                    if (!isStoppedManuallyRef.current) {\n                                        setRecognitionState('starting');\n                                        recognitionRef.current.start();\n                                    }\n                                } catch (retryErr) {\n                                    setError('Failed to start speech recognition after retry');\n                                    setRecognitionState('error');\n                                }\n                            }\n                        }[\"useSpeechRecognition.useCallback[startListening]\"], 100);\n                    } catch (stopErr) {\n                        setError('Failed to restart speech recognition');\n                        setRecognitionState('error');\n                    }\n                } else {\n                    setError('Failed to start speech recognition');\n                    setRecognitionState('error');\n                }\n            }\n        }\n    }[\"useSpeechRecognition.useCallback[startListening]\"], [\n        recognitionState,\n        isListening,\n        isSupported\n    ]);\n    const stopListening = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useSpeechRecognition.useCallback[stopListening]\": ()=>{\n            if (!recognitionRef.current) return;\n            isStoppedManuallyRef.current = true;\n            setRecognitionState('stopping');\n            // Clear any pending restart timeout\n            if (restartTimeoutRef.current) {\n                clearTimeout(restartTimeoutRef.current);\n                restartTimeoutRef.current = null;\n            }\n            try {\n                recognitionRef.current.stop();\n                console.log('Speech recognition stopped manually');\n            } catch (err) {\n                console.error('Failed to stop speech recognition:', err);\n                setError('Failed to stop speech recognition');\n                setRecognitionState('error');\n            }\n        }\n    }[\"useSpeechRecognition.useCallback[stopListening]\"], []);\n    const resetTranscript = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useSpeechRecognition.useCallback[resetTranscript]\": ()=>{\n            setFinalTranscript('');\n            setInterimTranscript('');\n            setError(null);\n            restartAttemptsRef.current = 0;\n        }\n    }[\"useSpeechRecognition.useCallback[resetTranscript]\"], []);\n    const setLanguage = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useSpeechRecognition.useCallback[setLanguage]\": (language)=>{\n            if (recognitionRef.current) {\n                recognitionRef.current.lang = language;\n                optionsRef.current.language = language;\n            }\n        }\n    }[\"useSpeechRecognition.useCallback[setLanguage]\"], []);\n    // Cleanup on unmount\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)({\n        \"useSpeechRecognition.useEffect\": ()=>{\n            return ({\n                \"useSpeechRecognition.useEffect\": ()=>{\n                    if (restartTimeoutRef.current) {\n                        clearTimeout(restartTimeoutRef.current);\n                    }\n                    if (recognitionRef.current) {\n                        isStoppedManuallyRef.current = true;\n                        try {\n                            recognitionRef.current.stop();\n                        } catch (err) {\n                            console.error('Error stopping recognition on cleanup:', err);\n                        }\n                    }\n                }\n            })[\"useSpeechRecognition.useEffect\"];\n        }\n    }[\"useSpeechRecognition.useEffect\"], []);\n    return {\n        finalTranscript,\n        interimTranscript,\n        isListening,\n        error,\n        isSupported,\n        recognitionState,\n        startListening,\n        stopListening,\n        resetTranscript,\n        setLanguage\n    };\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (useSpeechRecognition);\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/hooks/useSpeechRecognition.ts\n"));

/***/ })

});