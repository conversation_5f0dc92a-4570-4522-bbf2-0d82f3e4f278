/**
 * DeviceSelector Component
 * 
 * Component for selecting camera and microphone devices.
 * Provides device enumeration and switching capabilities.
 */

'use client';

import React, { useEffect } from 'react';
import useMediaStream from '@/hooks/useMediaStream';

interface DeviceSelectorProps {
  className?: string;
  showAudioDevices?: boolean;
  showVideoDevices?: boolean;
  onDeviceChange?: (deviceId: string, type: 'video' | 'audio') => void;
}

const DeviceSelector: React.FC<DeviceSelectorProps> = ({
  className = '',
  showAudioDevices = true,
  showVideoDevices = true,
  onDeviceChange
}) => {
  const mediaStream = useMediaStream();

  // Refresh devices on mount
  useEffect(() => {
    if (mediaStream.isSupported) {
      mediaStream.refreshDevices();
    }
  }, [mediaStream.isSupported]); // Only depend on isSupported, not the entire mediaStream object

  const videoDevices = mediaStream.devices.filter(device => device.kind === 'videoinput');
  const audioDevices = mediaStream.devices.filter(device => device.kind === 'audioinput');

  const handleVideoDeviceChange = async (deviceId: string) => {
    try {
      await mediaStream.switchVideoDevice(deviceId);
      onDeviceChange?.(deviceId, 'video');
    } catch (error) {
      console.error('Failed to switch video device:', error);
    }
  };

  const handleAudioDeviceChange = async (deviceId: string) => {
    try {
      await mediaStream.switchAudioDevice(deviceId);
      onDeviceChange?.(deviceId, 'audio');
    } catch (error) {
      console.error('Failed to switch audio device:', error);
    }
  };

  if (!mediaStream.isSupported) {
    return (
      <div className={`p-4 bg-yellow-50 border border-yellow-200 rounded-lg ${className}`}>
        <p className="text-yellow-800 text-sm">
          Device selection is not supported in this browser.
        </p>
      </div>
    );
  }

  return (
    <div className={`space-y-4 ${className}`}>
      {/* Video devices */}
      {showVideoDevices && videoDevices.length > 0 && (
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            Camera Device
          </label>
          <select
            value={mediaStream.selectedVideoDevice}
            onChange={(e) => handleVideoDeviceChange(e.target.value)}
            disabled={mediaStream.isStreaming}
            className="w-full px-3 py-2 border border-gray-300 rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent disabled:bg-gray-100 disabled:cursor-not-allowed"
          >
            {videoDevices.map((device) => (
              <option key={device.deviceId} value={device.deviceId}>
                📷 {device.label || `Camera ${device.deviceId.slice(0, 8)}`}
              </option>
            ))}
          </select>
          {mediaStream.isStreaming && (
            <p className="text-xs text-gray-500 mt-1">
              Stop the camera to change devices
            </p>
          )}
        </div>
      )}

      {/* Audio devices */}
      {showAudioDevices && audioDevices.length > 0 && (
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            Microphone Device
          </label>
          <select
            value={mediaStream.selectedAudioDevice}
            onChange={(e) => handleAudioDeviceChange(e.target.value)}
            disabled={mediaStream.isStreaming}
            className="w-full px-3 py-2 border border-gray-300 rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent disabled:bg-gray-100 disabled:cursor-not-allowed"
          >
            {audioDevices.map((device) => (
              <option key={device.deviceId} value={device.deviceId}>
                🎤 {device.label || `Microphone ${device.deviceId.slice(0, 8)}`}
              </option>
            ))}
          </select>
          {mediaStream.isStreaming && (
            <p className="text-xs text-gray-500 mt-1">
              Stop the camera to change devices
            </p>
          )}
        </div>
      )}

      {/* Device info */}
      <div className="text-xs text-gray-500 bg-gray-50 p-3 rounded">
        <div className="grid grid-cols-2 gap-2">
          <div>
            <strong>Video devices:</strong> {videoDevices.length}
          </div>
          <div>
            <strong>Audio devices:</strong> {audioDevices.length}
          </div>
          <div>
            <strong>Status:</strong> {mediaStream.isStreaming ? 'Active' : 'Inactive'}
          </div>
          <div>
            <strong>Supported:</strong> {mediaStream.isSupported ? 'Yes' : 'No'}
          </div>
        </div>
      </div>

      {/* Error display */}
      {mediaStream.error && (
        <div className="p-3 bg-red-50 border border-red-200 rounded text-red-700 text-sm">
          <strong>Device Error:</strong> {mediaStream.error}
        </div>
      )}

      {/* Refresh button */}
      <button
        onClick={mediaStream.refreshDevices}
        className="w-full px-3 py-2 text-sm bg-gray-100 hover:bg-gray-200 text-gray-700 rounded border transition-colors"
      >
        🔄 Refresh Devices
      </button>
    </div>
  );
};

export default DeviceSelector;
