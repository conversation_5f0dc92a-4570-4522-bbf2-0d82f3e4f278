'use client';

import { useCaption, useCaptionActions, useCaptionSelectors } from "@/contexts/CaptionContext";
import SpeechController from "@/components/SpeechRecognition/SpeechController";
import VideoPlayerWithCaptions from "@/components/VideoPlayer/VideoPlayerWithCaptions";
import DeviceSelector from "@/components/VideoPlayer/DeviceSelector";
import TranslationSettings from "@/components/Translation/TranslationSettings";

export default function Home() {
  const { state } = useCaption();
  const actions = useCaptionActions();
  const selectors = useCaptionSelectors();

  const handleTestTranscript = () => {
    actions.setFinalTranscript("Hello, this is a test transcript from CAPTION.Ninja!");
    actions.setTranslatedText("¡Hola, esta es una transcripción de prueba de CAPTION.Ninja!");
  };

  const handleClearTranscript = () => {
    actions.resetTranscript();
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 p-8">
      <div className="max-w-4xl mx-auto">
        <header className="text-center mb-8">
          <h1 className="text-4xl font-bold text-gray-900 mb-2">
            CAPTION.Ninja
          </h1>
          <p className="text-lg text-gray-600">
            Real-time Speech-to-Text with Translation & Video Streaming
          </p>
        </header>

        {/* Video Player Section */}
        <div className="mb-8">
          <VideoPlayerWithCaptions
            className="w-full max-w-4xl mx-auto"
            showControls={true}
            autoStart={false}
            aspectRatio="16:9"
            quality="high"
          />
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6 mb-8">
          {/* Speech Recognition Controller */}
          <div className="lg:col-span-2">
            <SpeechController />
          </div>

          {/* System Status & Device Selector */}
          <div className="space-y-6">
            {/* System Status */}
            <div className="bg-white rounded-lg shadow-md p-6">
              <h2 className="text-xl font-semibold text-gray-800 mb-4">
                System Status
              </h2>
              <div className="space-y-2 text-sm">
                <div>
                  <span className="font-medium">Session:</span> {state.sessionId.split('-').pop()}
                </div>
                <div>
                  <span className="font-medium">Recording:</span>{" "}
                  <span className={state.isRecording ? "text-green-600" : "text-red-600"}>
                    {state.isRecording ? "🔴 Live" : "⭕ Stopped"}
                  </span>
                </div>
                <div>
                  <span className="font-medium">Translation:</span>{" "}
                  <span className={state.settings.translationEnabled ? "text-green-600" : "text-gray-500"}>
                    {state.settings.translationEnabled ? "✅ Enabled" : "❌ Disabled"}
                  </span>
                </div>
                <div>
                  <span className="font-medium">Language:</span> {state.settings.speechLanguage}
                </div>
                <div>
                  <span className="font-medium">History:</span> {state.transcriptHistory.length} entries
                </div>
                <div>
                  <span className="font-medium">Words:</span> {selectors.getTranscriptWordCount()}
                </div>
              </div>

              {/* Quick Test Controls */}
              <div className="mt-4 pt-4 border-t border-gray-200">
                <h3 className="text-sm font-medium text-gray-700 mb-2">Quick Tests</h3>
                <div className="space-y-2">
                  <button
                    onClick={handleTestTranscript}
                    className="w-full px-3 py-2 text-sm bg-blue-500 text-white rounded hover:bg-blue-600 transition-colors"
                  >
                    Add Test Transcript
                  </button>
                  <button
                    onClick={handleClearTranscript}
                    className="w-full px-3 py-2 text-sm bg-red-500 text-white rounded hover:bg-red-600 transition-colors"
                  >
                    Clear All
                  </button>
                  <button
                    onClick={() => actions.updateSettings({
                      translationEnabled: !state.settings.translationEnabled
                    })}
                    className="w-full px-3 py-2 text-sm bg-purple-500 text-white rounded hover:bg-purple-600 transition-colors"
                  >
                    Toggle Translation
                  </button>
                </div>
              </div>
            </div>

            {/* Device Selector */}
            <div className="bg-white rounded-lg shadow-md p-6">
              <h2 className="text-xl font-semibold text-gray-800 mb-4">
                Device Settings
              </h2>
              <DeviceSelector
                showAudioDevices={true}
                showVideoDevices={true}
              />
            </div>

            {/* Translation Settings */}
            <div className="bg-white rounded-lg shadow-md p-6">
              <TranslationSettings />
            </div>
          </div>
        </div>

        {/* Transcript History Display */}
        <div className="bg-white rounded-lg shadow-md p-6">
          <h2 className="text-xl font-semibold text-gray-800 mb-4">
            Transcript History
          </h2>

          {state.transcriptHistory.length > 0 ? (
            <div className="space-y-3 max-h-96 overflow-y-auto">
              {state.transcriptHistory.slice(-10).reverse().map((entry, index) => (
                <div key={entry.id} className="p-3 bg-gray-50 border border-gray-200 rounded-lg">
                  <div className="flex items-start justify-between mb-2">
                    <div className="flex items-center gap-2">
                      <span className="text-xs bg-blue-100 text-blue-800 px-2 py-1 rounded">
                        #{state.transcriptHistory.length - index}
                      </span>
                      <span className="text-xs text-gray-500">
                        {new Date(entry.timestamp).toLocaleTimeString()}
                      </span>
                      {entry.languageCode && (
                        <span className="text-xs bg-green-100 text-green-800 px-2 py-1 rounded">
                          {entry.languageCode}
                        </span>
                      )}
                    </div>
                  </div>
                  <p className="text-gray-800 text-sm leading-relaxed">{entry.text}</p>
                  {entry.translatedText && (
                    <p className="text-blue-600 text-sm mt-2 italic border-l-2 border-blue-200 pl-2">
                      {entry.translatedText}
                    </p>
                  )}
                </div>
              ))}
              {state.transcriptHistory.length > 10 && (
                <div className="text-center text-sm text-gray-500 py-2">
                  Showing last 10 entries of {state.transcriptHistory.length} total
                </div>
              )}
            </div>
          ) : (
            <div className="text-center py-8 text-gray-500">
              <div className="text-4xl mb-2">🎤</div>
              <p>No transcript history yet</p>
              <p className="text-sm mt-1">Start recording to see your speech transcribed here</p>
            </div>
          )}
        </div>

        {/* Error Display */}
        {(state.speechError || state.translationError || state.ttsError) && (
          <div className="bg-red-50 border border-red-200 rounded-lg p-4">
            <h3 className="font-medium text-red-800 mb-2">Errors:</h3>
            {state.speechError && (
              <p className="text-red-700 text-sm">Speech: {state.speechError}</p>
            )}
            {state.translationError && (
              <p className="text-red-700 text-sm">Translation: {state.translationError}</p>
            )}
            {state.ttsError && (
              <p className="text-red-700 text-sm">TTS: {state.ttsError}</p>
            )}
          </div>
        )}

        <footer className="text-center text-gray-600 text-sm">
          <p>CAPTION.Ninja Next.js Migration - Context Testing Page</p>
          <p className="mt-1">
            This page demonstrates the centralized state management system
          </p>
        </footer>
      </div>
    </div>
  );
}
