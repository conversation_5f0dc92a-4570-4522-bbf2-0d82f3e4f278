"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./src/hooks/useAutoTranslation.ts":
/*!*****************************************!*\
  !*** ./src/hooks/useAutoTranslation.ts ***!
  \*****************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _contexts_CaptionContext__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/contexts/CaptionContext */ \"(app-pages-browser)/./src/contexts/CaptionContext.tsx\");\n/* harmony import */ var _useTranslation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./useTranslation */ \"(app-pages-browser)/./src/hooks/useTranslation.ts\");\n/**\n * useAutoTranslation Hook\n * \n * React hook that automatically translates speech recognition results\n * when translation is enabled in the caption context.\n */ \n\n\nconst useAutoTranslation = function() {\n    let options = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : {};\n    const { debounceDelay = 500, translateInterim = false, maxRetries = 3 } = options;\n    const { state } = (0,_contexts_CaptionContext__WEBPACK_IMPORTED_MODULE_1__.useCaption)();\n    const actions = (0,_contexts_CaptionContext__WEBPACK_IMPORTED_MODULE_1__.useCaptionActions)();\n    const translation = (0,_useTranslation__WEBPACK_IMPORTED_MODULE_2__[\"default\"])();\n    const debounceTimeoutRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(null);\n    const lastTranslatedTextRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)('');\n    const retryCountRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(0);\n    const translateWithRetryRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)();\n    // Get API key from localStorage\n    const getApiKey = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useAutoTranslation.useCallback[getApiKey]\": ()=>{\n            if (true) {\n                return localStorage.getItem('caption-ninja-google-api-key') || '';\n            }\n            return '';\n        }\n    }[\"useAutoTranslation.useCallback[getApiKey]\"], []);\n    // Translate text with retry logic\n    const translateWithRetry = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useAutoTranslation.useCallback[translateWithRetry]\": async function(text) {\n            let isInterim = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : false;\n            if (!text.trim()) return;\n            const apiKey = getApiKey();\n            if (!apiKey) {\n                console.warn('No Google Cloud Translation API key found');\n                return;\n            }\n            // Skip if source and target languages are the same\n            if (state.settings.sourceLanguage === state.settings.targetLanguage) {\n                actions.setTranslatedText(text);\n                return;\n            }\n            try {\n                actions.setTranslatingStatus(true);\n                actions.setTranslationError(null);\n                const result = await translation.translateText(text, {\n                    sourceLanguage: state.settings.sourceLanguage,\n                    targetLanguage: state.settings.targetLanguage,\n                    apiKey\n                });\n                if (result) {\n                    actions.setTranslatedText(result.translatedText);\n                    lastTranslatedTextRef.current = result.translatedText;\n                    retryCountRef.current = 0; // Reset retry count on success\n                } else {\n                    throw new Error('No translation result received');\n                }\n            } catch (error) {\n                console.error('Translation failed:', error);\n                // Retry logic\n                if (retryCountRef.current < maxRetries) {\n                    retryCountRef.current++;\n                    console.log(\"Retrying translation (attempt \".concat(retryCountRef.current, \"/\").concat(maxRetries, \")\"));\n                    // Exponential backoff\n                    const delay = Math.pow(2, retryCountRef.current - 1) * 1000;\n                    setTimeout({\n                        \"useAutoTranslation.useCallback[translateWithRetry]\": ()=>{\n                            translateWithRetry(text, isInterim);\n                        }\n                    }[\"useAutoTranslation.useCallback[translateWithRetry]\"], delay);\n                } else {\n                    const errorMessage = error instanceof Error ? error.message : 'Translation failed';\n                    actions.setTranslationError(errorMessage);\n                    retryCountRef.current = 0; // Reset for next translation\n                }\n            } finally{\n                actions.setTranslatingStatus(false);\n            }\n        }\n    }[\"useAutoTranslation.useCallback[translateWithRetry]\"], [\n        state.settings.sourceLanguage,\n        state.settings.targetLanguage,\n        translation,\n        actions,\n        getApiKey,\n        maxRetries\n    ]);\n    // Debounced translation function - use ref to avoid dependency issues\n    const debouncedTranslate = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useAutoTranslation.useCallback[debouncedTranslate]\": function(text) {\n            let isInterim = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : false;\n            // Clear existing timeout\n            if (debounceTimeoutRef.current) {\n                clearTimeout(debounceTimeoutRef.current);\n            }\n            // Set new timeout\n            debounceTimeoutRef.current = setTimeout({\n                \"useAutoTranslation.useCallback[debouncedTranslate]\": ()=>{\n                    // Use the current translateWithRetry function from ref to avoid stale closures\n                    const currentTranslateWithRetry = translateWithRetry;\n                    currentTranslateWithRetry(text, isInterim);\n                }\n            }[\"useAutoTranslation.useCallback[debouncedTranslate]\"], isInterim ? debounceDelay / 2 : debounceDelay); // Faster for interim results\n        }\n    }[\"useAutoTranslation.useCallback[debouncedTranslate]\"], [\n        debounceDelay\n    ]); // Remove translateWithRetry from dependencies to prevent cascades\n    // Auto-translate final transcript\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)({\n        \"useAutoTranslation.useEffect\": ()=>{\n            if (!state.settings.translationEnabled || !state.finalTranscript) {\n                return;\n            }\n            // Skip if already translated this text\n            if (state.finalTranscript === lastTranslatedTextRef.current) {\n                return;\n            }\n            debouncedTranslate(state.finalTranscript, false);\n        }\n    }[\"useAutoTranslation.useEffect\"], [\n        state.finalTranscript,\n        state.settings.translationEnabled,\n        debouncedTranslate\n    ]);\n    // Auto-translate interim transcript (if enabled)\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)({\n        \"useAutoTranslation.useEffect\": ()=>{\n            if (!state.settings.translationEnabled || !translateInterim || !state.interimTranscript) {\n                return;\n            }\n            debouncedTranslate(state.interimTranscript, true);\n        }\n    }[\"useAutoTranslation.useEffect\"], [\n        state.interimTranscript,\n        state.settings.translationEnabled,\n        translateInterim,\n        debouncedTranslate\n    ]);\n    // Clear translation when translation is disabled\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)({\n        \"useAutoTranslation.useEffect\": ()=>{\n            if (!state.settings.translationEnabled) {\n                actions.setTranslatedText('');\n                actions.setTranslationError(null);\n                lastTranslatedTextRef.current = '';\n            }\n        }\n    }[\"useAutoTranslation.useEffect\"], [\n        state.settings.translationEnabled,\n        actions\n    ]);\n    // Cleanup on unmount\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)({\n        \"useAutoTranslation.useEffect\": ()=>{\n            return ({\n                \"useAutoTranslation.useEffect\": ()=>{\n                    if (debounceTimeoutRef.current) {\n                        clearTimeout(debounceTimeoutRef.current);\n                    }\n                    translation.cancelTranslation();\n                }\n            })[\"useAutoTranslation.useEffect\"];\n        }\n    }[\"useAutoTranslation.useEffect\"], [\n        translation\n    ]);\n    // Manual translation function\n    const translateText = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useAutoTranslation.useCallback[translateText]\": (text)=>{\n            if (!text.trim()) return;\n            translateWithRetry(text, false);\n        }\n    }[\"useAutoTranslation.useCallback[translateText]\"], [\n        translateWithRetry\n    ]);\n    // Clear translation\n    const clearTranslation = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useAutoTranslation.useCallback[clearTranslation]\": ()=>{\n            actions.setTranslatedText('');\n            actions.setTranslationError(null);\n            lastTranslatedTextRef.current = '';\n            if (debounceTimeoutRef.current) {\n                clearTimeout(debounceTimeoutRef.current);\n            }\n        }\n    }[\"useAutoTranslation.useCallback[clearTranslation]\"], [\n        actions\n    ]);\n    return {\n        translateText,\n        clearTranslation,\n        isTranslating: state.isTranslating,\n        translationError: state.translationError,\n        translatedText: state.translatedText,\n        translationStats: translation.getTranslationStats()\n    };\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (useAutoTranslation);\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/hooks/useAutoTranslation.ts\n"));

/***/ })

});