/**
 * useAutoTranslation Hook
 * 
 * React hook that automatically translates speech recognition results
 * when translation is enabled in the caption context.
 */

import { useEffect, useCallback, useRef } from 'react';
import { useCaption, useCaptionActions } from '@/contexts/CaptionContext';
import useTranslation from './useTranslation';

interface AutoTranslationOptions {
  debounceDelay?: number;
  translateInterim?: boolean;
  maxRetries?: number;
}

const useAutoTranslation = (options: AutoTranslationOptions = {}) => {
  const {
    debounceDelay = 500,
    translateInterim = false,
    maxRetries = 3
  } = options;

  const { state } = useCaption();
  const actions = useCaptionActions();
  const translation = useTranslation();

  const debounceTimeoutRef = useRef<NodeJS.Timeout | null>(null);
  const lastTranslatedTextRef = useRef<string>('');
  const retryCountRef = useRef<number>(0);

  // Get API key from localStorage
  const getApiKey = useCallback(() => {
    if (typeof window !== 'undefined') {
      return localStorage.getItem('caption-ninja-google-api-key') || '';
    }
    return '';
  }, []);

  // Translate text with retry logic
  const translateWithRetry = useCallback(async (
    text: string,
    isInterim: boolean = false
  ) => {
    if (!text.trim()) return;

    const apiKey = getApiKey();
    if (!apiKey) {
      console.warn('No Google Cloud Translation API key found');
      return;
    }

    // Skip if source and target languages are the same
    if (state.settings.sourceLanguage === state.settings.targetLanguage) {
      actions.setTranslatedText(text);
      return;
    }

    try {
      actions.setTranslatingStatus(true);
      actions.setTranslationError(null);

      const result = await translation.translateText(text, {
        sourceLanguage: state.settings.sourceLanguage,
        targetLanguage: state.settings.targetLanguage,
        apiKey
      });

      if (result) {
        actions.setTranslatedText(result.translatedText);
        lastTranslatedTextRef.current = result.translatedText;
        retryCountRef.current = 0; // Reset retry count on success
      } else {
        throw new Error('No translation result received');
      }
    } catch (error) {
      console.error('Translation failed:', error);
      
      // Retry logic
      if (retryCountRef.current < maxRetries) {
        retryCountRef.current++;
        console.log(`Retrying translation (attempt ${retryCountRef.current}/${maxRetries})`);
        
        // Exponential backoff
        const delay = Math.pow(2, retryCountRef.current - 1) * 1000;
        setTimeout(() => {
          translateWithRetry(text, isInterim);
        }, delay);
      } else {
        const errorMessage = error instanceof Error ? error.message : 'Translation failed';
        actions.setTranslationError(errorMessage);
        retryCountRef.current = 0; // Reset for next translation
      }
    } finally {
      actions.setTranslatingStatus(false);
    }
  }, [
    state.settings.sourceLanguage,
    state.settings.targetLanguage,
    translation,
    actions,
    getApiKey,
    maxRetries
  ]);

  // Debounced translation function
  const debouncedTranslate = useCallback((text: string, isInterim: boolean = false) => {
    // Clear existing timeout
    if (debounceTimeoutRef.current) {
      clearTimeout(debounceTimeoutRef.current);
    }

    // Set new timeout
    debounceTimeoutRef.current = setTimeout(() => {
      translateWithRetry(text, isInterim);
    }, isInterim ? debounceDelay / 2 : debounceDelay); // Faster for interim results
  }, [translateWithRetry, debounceDelay]);

  // Auto-translate final transcript
  useEffect(() => {
    if (!state.settings.translationEnabled || !state.finalTranscript) {
      return;
    }

    // Skip if already translated this text
    if (state.finalTranscript === lastTranslatedTextRef.current) {
      return;
    }

    debouncedTranslate(state.finalTranscript, false);
  }, [
    state.finalTranscript,
    state.settings.translationEnabled,
    debouncedTranslate
  ]);

  // Auto-translate interim transcript (if enabled)
  useEffect(() => {
    if (!state.settings.translationEnabled || !translateInterim || !state.interimTranscript) {
      return;
    }

    debouncedTranslate(state.interimTranscript, true);
  }, [
    state.interimTranscript,
    state.settings.translationEnabled,
    translateInterim,
    debouncedTranslate
  ]);

  // Clear translation when translation is disabled
  useEffect(() => {
    if (!state.settings.translationEnabled) {
      actions.setTranslatedText('');
      actions.setTranslationError(null);
      lastTranslatedTextRef.current = '';
    }
  }, [state.settings.translationEnabled, actions]);

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      if (debounceTimeoutRef.current) {
        clearTimeout(debounceTimeoutRef.current);
      }
      translation.cancelTranslation();
    };
  }, [translation]);

  // Manual translation function
  const translateText = useCallback((text: string) => {
    if (!text.trim()) return;
    translateWithRetry(text, false);
  }, [translateWithRetry]);

  // Clear translation
  const clearTranslation = useCallback(() => {
    actions.setTranslatedText('');
    actions.setTranslationError(null);
    lastTranslatedTextRef.current = '';
    
    if (debounceTimeoutRef.current) {
      clearTimeout(debounceTimeoutRef.current);
    }
  }, [actions]);

  return {
    translateText,
    clearTranslation,
    isTranslating: state.isTranslating,
    translationError: state.translationError,
    translatedText: state.translatedText,
    translationStats: translation.getTranslationStats()
  };
};

export default useAutoTranslation;
