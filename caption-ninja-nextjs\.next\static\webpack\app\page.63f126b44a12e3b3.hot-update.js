"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./src/hooks/useSpeechRecognition.ts":
/*!*******************************************!*\
  !*** ./src/hooks/useSpeechRecognition.ts ***!
  \*******************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _utils_logger__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/utils/logger */ \"(app-pages-browser)/./src/utils/logger.ts\");\n/**\n * useSpeechRecognition Hook\n * \n * React hook for managing speech recognition functionality.\n * Based on the speech recognition implementation from index.html.\n */ \n\nconst useSpeechRecognition = function() {\n    let options = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : {};\n    // Memoize default options to prevent recreation on every render\n    const defaultOptions = (0,react__WEBPACK_IMPORTED_MODULE_0__.useMemo)({\n        \"useSpeechRecognition.useMemo[defaultOptions]\": ()=>({\n                language: 'en-US',\n                continuous: true,\n                interimResults: true,\n                maxAlternatives: 1\n            })\n    }[\"useSpeechRecognition.useMemo[defaultOptions]\"], []);\n    // Memoize merged options to prevent infinite re-renders\n    const mergedOptions = (0,react__WEBPACK_IMPORTED_MODULE_0__.useMemo)({\n        \"useSpeechRecognition.useMemo[mergedOptions]\": ()=>({\n                ...defaultOptions,\n                ...options\n            })\n    }[\"useSpeechRecognition.useMemo[mergedOptions]\"], [\n        defaultOptions,\n        options.language,\n        options.continuous,\n        options.interimResults,\n        options.maxAlternatives\n    ]);\n    const [finalTranscript, setFinalTranscript] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)('');\n    const [interimTranscript, setInterimTranscript] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)('');\n    const [isListening, setIsListening] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(false);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(null);\n    const [isSupported, setIsSupported] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(false);\n    const [recognitionState, setRecognitionState] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)('idle');\n    const recognitionRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(null);\n    const optionsRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(mergedOptions);\n    const restartTimeoutRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(null);\n    const restartAttemptsRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(0);\n    const isStoppedManuallyRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(false);\n    const MAX_RESTART_ATTEMPTS = 3;\n    const RESTART_DELAY = 1000;\n    // Update options ref when merged options change\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)({\n        \"useSpeechRecognition.useEffect\": ()=>{\n            optionsRef.current = mergedOptions;\n        }\n    }[\"useSpeechRecognition.useEffect\"], [\n        mergedOptions\n    ]);\n    // Initialize speech recognition\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)({\n        \"useSpeechRecognition.useEffect\": ()=>{\n            if (false) {}\n            const SpeechRecognition = window.SpeechRecognition || window.webkitSpeechRecognition;\n            if (!SpeechRecognition) {\n                setIsSupported(false);\n                setError('Speech recognition is not supported in this browser');\n                return;\n            }\n            setIsSupported(true);\n            const recognition = new SpeechRecognition();\n            recognitionRef.current = recognition;\n            // Configure recognition with current merged options\n            recognition.continuous = mergedOptions.continuous;\n            recognition.interimResults = mergedOptions.interimResults;\n            recognition.lang = mergedOptions.language;\n            recognition.maxAlternatives = mergedOptions.maxAlternatives;\n            // Event handlers with state machine\n            recognition.onstart = ({\n                \"useSpeechRecognition.useEffect\": ()=>{\n                    _utils_logger__WEBPACK_IMPORTED_MODULE_1__[\"default\"].speechRecognition.started(mergedOptions.language);\n                    _utils_logger__WEBPACK_IMPORTED_MODULE_1__[\"default\"].speechRecognition.stateChange(recognitionState, 'listening');\n                    setIsListening(true);\n                    setRecognitionState('listening');\n                    setError(null);\n                    // Reset restart attempts on successful start\n                    restartAttemptsRef.current = 0;\n                }\n            })[\"useSpeechRecognition.useEffect\"];\n            recognition.onend = ({\n                \"useSpeechRecognition.useEffect\": ()=>{\n                    _utils_logger__WEBPACK_IMPORTED_MODULE_1__[\"default\"].speechRecognition.stopped();\n                    setIsListening(false);\n                    // Only attempt restart if we're not in stopping state and not stopped manually\n                    if (recognitionState !== 'stopping' && !isStoppedManuallyRef.current && mergedOptions.continuous) {\n                        if (restartAttemptsRef.current < MAX_RESTART_ATTEMPTS) {\n                            console.log(\"Attempting to restart recognition (attempt \".concat(restartAttemptsRef.current + 1, \")\"));\n                            setRecognitionState('restarting');\n                            restartAttemptsRef.current++;\n                            // Use exponential backoff for restart attempts\n                            const backoffDelay = RESTART_DELAY * Math.pow(2, restartAttemptsRef.current - 1);\n                            restartTimeoutRef.current = setTimeout({\n                                \"useSpeechRecognition.useEffect\": ()=>{\n                                    try {\n                                        if (recognitionRef.current && !isStoppedManuallyRef.current && recognitionState !== 'stopping') {\n                                            setRecognitionState('starting');\n                                            recognitionRef.current.start();\n                                        }\n                                    } catch (err) {\n                                        console.error('Failed to restart recognition:', err);\n                                        setError('Failed to restart speech recognition');\n                                        setRecognitionState('error');\n                                    }\n                                }\n                            }[\"useSpeechRecognition.useEffect\"], backoffDelay);\n                        } else {\n                            setError('Maximum restart attempts reached. Please restart manually.');\n                            setRecognitionState('error');\n                        }\n                    } else {\n                        setRecognitionState('idle');\n                    }\n                }\n            })[\"useSpeechRecognition.useEffect\"];\n            recognition.onerror = ({\n                \"useSpeechRecognition.useEffect\": (event)=>{\n                    console.error('Speech recognition error:', event);\n                    // Handle different error types with state machine\n                    switch(event.error){\n                        case 'no-speech':\n                            setError('No speech detected. Still listening...');\n                            break;\n                        case 'audio-capture':\n                            setError('No microphone detected. Please check your microphone.');\n                            setIsListening(false);\n                            setRecognitionState('error');\n                            break;\n                        case 'not-allowed':\n                            setError('Microphone access denied. Please allow microphone access.');\n                            setIsListening(false);\n                            setRecognitionState('error');\n                            break;\n                        case 'network':\n                            setError('Network error occurred. Will attempt to reconnect...');\n                            setRecognitionState('error');\n                            break;\n                        case 'aborted':\n                            console.log('Recognition aborted');\n                            if (!isStoppedManuallyRef.current) {\n                                setError('Recognition was aborted unexpectedly');\n                                setRecognitionState('error');\n                            } else {\n                                setRecognitionState('idle');\n                            }\n                            break;\n                        default:\n                            setError(\"Speech recognition error: \".concat(event.error));\n                            setRecognitionState('error');\n                            setIsListening(false);\n                    }\n                }\n            })[\"useSpeechRecognition.useEffect\"];\n            recognition.onresult = ({\n                \"useSpeechRecognition.useEffect\": (event)=>{\n                    if (typeof event.results === 'undefined') {\n                        console.log('Undefined results in event:', event);\n                        return;\n                    }\n                    let interim = '';\n                    let final = '';\n                    // Process only new results since last event\n                    for(let i = event.resultIndex; i < event.results.length; i++){\n                        const transcript = event.results[i][0].transcript.trim();\n                        if (event.results[i].isFinal) {\n                            final += (final ? ' ' : '') + transcript;\n                        } else {\n                            interim += (interim ? ' ' : '') + transcript;\n                        }\n                    }\n                    setInterimTranscript(interim);\n                    if (final) {\n                        setFinalTranscript({\n                            \"useSpeechRecognition.useEffect\": (prev)=>prev + (prev ? ' ' : '') + final\n                        }[\"useSpeechRecognition.useEffect\"]);\n                        // Reset restart attempts on successful recognition\n                        restartAttemptsRef.current = 0;\n                    }\n                }\n            })[\"useSpeechRecognition.useEffect\"];\n            return ({\n                \"useSpeechRecognition.useEffect\": ()=>{\n                    if (recognition) {\n                        recognition.stop();\n                    }\n                }\n            })[\"useSpeechRecognition.useEffect\"];\n        }\n    }[\"useSpeechRecognition.useEffect\"], [\n        mergedOptions\n    ]);\n    // Update language when it changes\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)({\n        \"useSpeechRecognition.useEffect\": ()=>{\n            if (recognitionRef.current && mergedOptions.language) {\n                recognitionRef.current.lang = mergedOptions.language;\n            }\n        }\n    }[\"useSpeechRecognition.useEffect\"], [\n        mergedOptions.language\n    ]);\n    const startListening = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useSpeechRecognition.useCallback[startListening]\": ()=>{\n            if (!recognitionRef.current || !isSupported) {\n                setError('Speech recognition is not available');\n                setRecognitionState('error');\n                return;\n            }\n            // Prevent starting if already in a transitional state\n            if (recognitionState === 'starting' || recognitionState === 'restarting' || isListening) {\n                return;\n            }\n            // Clear any pending restart timeout\n            if (restartTimeoutRef.current) {\n                clearTimeout(restartTimeoutRef.current);\n                restartTimeoutRef.current = null;\n            }\n            isStoppedManuallyRef.current = false;\n            restartAttemptsRef.current = 0;\n            setError(null);\n            setRecognitionState('starting');\n            try {\n                recognitionRef.current.start();\n                console.log('Speech recognition start requested');\n            } catch (err) {\n                console.error('Failed to start speech recognition:', err);\n                if (err instanceof Error && err.name === 'InvalidStateError') {\n                    // Recognition is already running, try to stop and restart\n                    try {\n                        setRecognitionState('stopping');\n                        recognitionRef.current.stop();\n                        setTimeout({\n                            \"useSpeechRecognition.useCallback[startListening]\": ()=>{\n                                try {\n                                    if (!isStoppedManuallyRef.current) {\n                                        setRecognitionState('starting');\n                                        recognitionRef.current.start();\n                                    }\n                                } catch (retryErr) {\n                                    setError('Failed to start speech recognition after retry');\n                                    setRecognitionState('error');\n                                }\n                            }\n                        }[\"useSpeechRecognition.useCallback[startListening]\"], 100);\n                    } catch (stopErr) {\n                        setError('Failed to restart speech recognition');\n                        setRecognitionState('error');\n                    }\n                } else {\n                    setError('Failed to start speech recognition');\n                    setRecognitionState('error');\n                }\n            }\n        }\n    }[\"useSpeechRecognition.useCallback[startListening]\"], [\n        recognitionState,\n        isListening,\n        isSupported\n    ]);\n    const stopListening = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useSpeechRecognition.useCallback[stopListening]\": ()=>{\n            if (!recognitionRef.current) return;\n            isStoppedManuallyRef.current = true;\n            setRecognitionState('stopping');\n            // Clear any pending restart timeout\n            if (restartTimeoutRef.current) {\n                clearTimeout(restartTimeoutRef.current);\n                restartTimeoutRef.current = null;\n            }\n            try {\n                recognitionRef.current.stop();\n                console.log('Speech recognition stopped manually');\n            } catch (err) {\n                console.error('Failed to stop speech recognition:', err);\n                setError('Failed to stop speech recognition');\n                setRecognitionState('error');\n            }\n        }\n    }[\"useSpeechRecognition.useCallback[stopListening]\"], []);\n    const resetTranscript = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useSpeechRecognition.useCallback[resetTranscript]\": ()=>{\n            setFinalTranscript('');\n            setInterimTranscript('');\n            setError(null);\n            restartAttemptsRef.current = 0;\n        }\n    }[\"useSpeechRecognition.useCallback[resetTranscript]\"], []);\n    const setLanguage = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useSpeechRecognition.useCallback[setLanguage]\": (language)=>{\n            if (recognitionRef.current) {\n                recognitionRef.current.lang = language;\n                optionsRef.current.language = language;\n            }\n        }\n    }[\"useSpeechRecognition.useCallback[setLanguage]\"], []);\n    // Cleanup on unmount\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)({\n        \"useSpeechRecognition.useEffect\": ()=>{\n            return ({\n                \"useSpeechRecognition.useEffect\": ()=>{\n                    if (restartTimeoutRef.current) {\n                        clearTimeout(restartTimeoutRef.current);\n                    }\n                    if (recognitionRef.current) {\n                        isStoppedManuallyRef.current = true;\n                        try {\n                            recognitionRef.current.stop();\n                        } catch (err) {\n                            console.error('Error stopping recognition on cleanup:', err);\n                        }\n                    }\n                }\n            })[\"useSpeechRecognition.useEffect\"];\n        }\n    }[\"useSpeechRecognition.useEffect\"], []);\n    return {\n        finalTranscript,\n        interimTranscript,\n        isListening,\n        error,\n        isSupported,\n        recognitionState,\n        startListening,\n        stopListening,\n        resetTranscript,\n        setLanguage\n    };\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (useSpeechRecognition);\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/hooks/useSpeechRecognition.ts\n"));

/***/ })

});