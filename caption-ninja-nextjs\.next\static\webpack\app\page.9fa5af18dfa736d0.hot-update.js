"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./src/hooks/useSpeechRecognition.ts":
/*!*******************************************!*\
  !*** ./src/hooks/useSpeechRecognition.ts ***!
  \*******************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n/**\n * useSpeechRecognition Hook\n * \n * React hook for managing speech recognition functionality.\n * Based on the speech recognition implementation from index.html.\n */ \nconst useSpeechRecognition = function() {\n    let options = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : {};\n    // Memoize default options to prevent recreation on every render\n    const defaultOptions = (0,react__WEBPACK_IMPORTED_MODULE_0__.useMemo)({\n        \"useSpeechRecognition.useMemo[defaultOptions]\": ()=>({\n                language: 'en-US',\n                continuous: true,\n                interimResults: true,\n                maxAlternatives: 1\n            })\n    }[\"useSpeechRecognition.useMemo[defaultOptions]\"], []);\n    // Memoize merged options to prevent infinite re-renders\n    const mergedOptions = (0,react__WEBPACK_IMPORTED_MODULE_0__.useMemo)({\n        \"useSpeechRecognition.useMemo[mergedOptions]\": ()=>({\n                ...defaultOptions,\n                ...options\n            })\n    }[\"useSpeechRecognition.useMemo[mergedOptions]\"], [\n        defaultOptions,\n        options.language,\n        options.continuous,\n        options.interimResults,\n        options.maxAlternatives\n    ]);\n    const [finalTranscript, setFinalTranscript] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)('');\n    const [interimTranscript, setInterimTranscript] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)('');\n    const [isListening, setIsListening] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(false);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(null);\n    const [isSupported, setIsSupported] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(false);\n    const [recognitionState, setRecognitionState] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)('idle');\n    const recognitionRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(null);\n    const optionsRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(mergedOptions);\n    const restartTimeoutRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(null);\n    const restartAttemptsRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(0);\n    const isStoppedManuallyRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(false);\n    const MAX_RESTART_ATTEMPTS = 3;\n    const RESTART_DELAY = 1000;\n    // Update options ref when merged options change\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)({\n        \"useSpeechRecognition.useEffect\": ()=>{\n            optionsRef.current = mergedOptions;\n        }\n    }[\"useSpeechRecognition.useEffect\"], [\n        mergedOptions\n    ]);\n    // Initialize speech recognition\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)({\n        \"useSpeechRecognition.useEffect\": ()=>{\n            if (false) {}\n            const SpeechRecognition = window.SpeechRecognition || window.webkitSpeechRecognition;\n            if (!SpeechRecognition) {\n                setIsSupported(false);\n                setError('Speech recognition is not supported in this browser');\n                return;\n            }\n            setIsSupported(true);\n            const recognition = new SpeechRecognition();\n            recognitionRef.current = recognition;\n            // Configure recognition with current merged options\n            recognition.continuous = mergedOptions.continuous;\n            recognition.interimResults = mergedOptions.interimResults;\n            recognition.lang = mergedOptions.language;\n            recognition.maxAlternatives = mergedOptions.maxAlternatives;\n            // Event handlers with state machine\n            recognition.onstart = ({\n                \"useSpeechRecognition.useEffect\": ()=>{\n                    console.log('Speech recognition started');\n                    setIsListening(true);\n                    setRecognitionState('listening');\n                    setError(null);\n                    // Reset restart attempts on successful start\n                    restartAttemptsRef.current = 0;\n                }\n            })[\"useSpeechRecognition.useEffect\"];\n            recognition.onend = ({\n                \"useSpeechRecognition.useEffect\": ()=>{\n                    console.log('Speech recognition ended');\n                    setIsListening(false);\n                    // Only attempt restart if we're not in stopping state and not stopped manually\n                    if (recognitionState !== 'stopping' && !isStoppedManuallyRef.current && mergedOptions.continuous) {\n                        if (restartAttemptsRef.current < MAX_RESTART_ATTEMPTS) {\n                            console.log(\"Attempting to restart recognition (attempt \".concat(restartAttemptsRef.current + 1, \")\"));\n                            setRecognitionState('restarting');\n                            restartAttemptsRef.current++;\n                            // Use exponential backoff for restart attempts\n                            const backoffDelay = RESTART_DELAY * Math.pow(2, restartAttemptsRef.current - 1);\n                            restartTimeoutRef.current = setTimeout({\n                                \"useSpeechRecognition.useEffect\": ()=>{\n                                    try {\n                                        if (recognitionRef.current && !isStoppedManuallyRef.current && recognitionState !== 'stopping') {\n                                            setRecognitionState('starting');\n                                            recognitionRef.current.start();\n                                        }\n                                    } catch (err) {\n                                        console.error('Failed to restart recognition:', err);\n                                        setError('Failed to restart speech recognition');\n                                        setRecognitionState('error');\n                                    }\n                                }\n                            }[\"useSpeechRecognition.useEffect\"], backoffDelay);\n                        } else {\n                            setError('Maximum restart attempts reached. Please restart manually.');\n                            setRecognitionState('error');\n                        }\n                    } else {\n                        setRecognitionState('idle');\n                    }\n                }\n            })[\"useSpeechRecognition.useEffect\"];\n            recognition.onerror = ({\n                \"useSpeechRecognition.useEffect\": (event)=>{\n                    console.error('Speech recognition error:', event);\n                    // Handle different error types with state machine\n                    switch(event.error){\n                        case 'no-speech':\n                            setError('No speech detected. Still listening...');\n                            break;\n                        case 'audio-capture':\n                            setError('No microphone detected. Please check your microphone.');\n                            setIsListening(false);\n                            setRecognitionState('error');\n                            break;\n                        case 'not-allowed':\n                            setError('Microphone access denied. Please allow microphone access.');\n                            setIsListening(false);\n                            setRecognitionState('error');\n                            break;\n                        case 'network':\n                            setError('Network error occurred. Will attempt to reconnect...');\n                            setRecognitionState('error');\n                            break;\n                        case 'aborted':\n                            console.log('Recognition aborted');\n                            if (!isStoppedManuallyRef.current) {\n                                setError('Recognition was aborted unexpectedly');\n                                setRecognitionState('error');\n                            } else {\n                                setRecognitionState('idle');\n                            }\n                            break;\n                        default:\n                            setError(\"Speech recognition error: \".concat(event.error));\n                            setRecognitionState('error');\n                            setIsListening(false);\n                    }\n                }\n            })[\"useSpeechRecognition.useEffect\"];\n            recognition.onresult = ({\n                \"useSpeechRecognition.useEffect\": (event)=>{\n                    if (typeof event.results === 'undefined') {\n                        console.log('Undefined results in event:', event);\n                        return;\n                    }\n                    let interim = '';\n                    let final = '';\n                    // Process only new results since last event\n                    for(let i = event.resultIndex; i < event.results.length; i++){\n                        const transcript = event.results[i][0].transcript.trim();\n                        if (event.results[i].isFinal) {\n                            final += (final ? ' ' : '') + transcript;\n                        } else {\n                            interim += (interim ? ' ' : '') + transcript;\n                        }\n                    }\n                    setInterimTranscript(interim);\n                    if (final) {\n                        setFinalTranscript({\n                            \"useSpeechRecognition.useEffect\": (prev)=>prev + (prev ? ' ' : '') + final\n                        }[\"useSpeechRecognition.useEffect\"]);\n                        // Reset restart attempts on successful recognition\n                        restartAttemptsRef.current = 0;\n                    }\n                }\n            })[\"useSpeechRecognition.useEffect\"];\n            return ({\n                \"useSpeechRecognition.useEffect\": ()=>{\n                    if (recognition) {\n                        recognition.stop();\n                    }\n                }\n            })[\"useSpeechRecognition.useEffect\"];\n        }\n    }[\"useSpeechRecognition.useEffect\"], [\n        mergedOptions\n    ]);\n    // Update language when it changes\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)({\n        \"useSpeechRecognition.useEffect\": ()=>{\n            if (recognitionRef.current && mergedOptions.language) {\n                recognitionRef.current.lang = mergedOptions.language;\n            }\n        }\n    }[\"useSpeechRecognition.useEffect\"], [\n        mergedOptions.language\n    ]);\n    const startListening = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useSpeechRecognition.useCallback[startListening]\": ()=>{\n            if (!recognitionRef.current || !isSupported) {\n                setError('Speech recognition is not available');\n                return;\n            }\n            if (isListening) {\n                return;\n            }\n            // Clear any pending restart timeout\n            if (restartTimeoutRef.current) {\n                clearTimeout(restartTimeoutRef.current);\n                restartTimeoutRef.current = null;\n            }\n            isStoppedManuallyRef.current = false;\n            restartAttemptsRef.current = 0;\n            setError(null);\n            try {\n                recognitionRef.current.start();\n                console.log('Speech recognition started');\n            } catch (err) {\n                console.error('Failed to start speech recognition:', err);\n                if (err instanceof Error && err.name === 'InvalidStateError') {\n                    // Recognition is already running, try to stop and restart\n                    try {\n                        recognitionRef.current.stop();\n                        setTimeout({\n                            \"useSpeechRecognition.useCallback[startListening]\": ()=>{\n                                try {\n                                    recognitionRef.current.start();\n                                } catch (retryErr) {\n                                    setError('Failed to start speech recognition after retry');\n                                }\n                            }\n                        }[\"useSpeechRecognition.useCallback[startListening]\"], 100);\n                    } catch (stopErr) {\n                        setError('Failed to restart speech recognition');\n                    }\n                } else {\n                    setError('Failed to start speech recognition');\n                }\n            }\n        }\n    }[\"useSpeechRecognition.useCallback[startListening]\"], [\n        isListening,\n        isSupported\n    ]);\n    const stopListening = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useSpeechRecognition.useCallback[stopListening]\": ()=>{\n            if (!recognitionRef.current) return;\n            isStoppedManuallyRef.current = true;\n            // Clear any pending restart timeout\n            if (restartTimeoutRef.current) {\n                clearTimeout(restartTimeoutRef.current);\n                restartTimeoutRef.current = null;\n            }\n            try {\n                recognitionRef.current.stop();\n                console.log('Speech recognition stopped manually');\n            } catch (err) {\n                console.error('Failed to stop speech recognition:', err);\n                setError('Failed to stop speech recognition');\n            }\n        }\n    }[\"useSpeechRecognition.useCallback[stopListening]\"], []);\n    const resetTranscript = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useSpeechRecognition.useCallback[resetTranscript]\": ()=>{\n            setFinalTranscript('');\n            setInterimTranscript('');\n            setError(null);\n            restartAttemptsRef.current = 0;\n        }\n    }[\"useSpeechRecognition.useCallback[resetTranscript]\"], []);\n    const setLanguage = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useSpeechRecognition.useCallback[setLanguage]\": (language)=>{\n            if (recognitionRef.current) {\n                recognitionRef.current.lang = language;\n                optionsRef.current.language = language;\n            }\n        }\n    }[\"useSpeechRecognition.useCallback[setLanguage]\"], []);\n    // Cleanup on unmount\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)({\n        \"useSpeechRecognition.useEffect\": ()=>{\n            return ({\n                \"useSpeechRecognition.useEffect\": ()=>{\n                    if (restartTimeoutRef.current) {\n                        clearTimeout(restartTimeoutRef.current);\n                    }\n                    if (recognitionRef.current) {\n                        isStoppedManuallyRef.current = true;\n                        try {\n                            recognitionRef.current.stop();\n                        } catch (err) {\n                            console.error('Error stopping recognition on cleanup:', err);\n                        }\n                    }\n                }\n            })[\"useSpeechRecognition.useEffect\"];\n        }\n    }[\"useSpeechRecognition.useEffect\"], []);\n    return {\n        finalTranscript,\n        interimTranscript,\n        isListening,\n        error,\n        isSupported,\n        startListening,\n        stopListening,\n        resetTranscript,\n        setLanguage\n    };\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (useSpeechRecognition);\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/hooks/useSpeechRecognition.ts\n"));

/***/ })

});