/**
 * useTranslation Hook
 *
 * React hook for managing translation functionality via Google Cloud Translation API.
 * Based on the translation implementation from translate.html.
 */

import { useState, useCallback, useRef, useEffect } from 'react';

interface TranslationOptions {
  sourceLanguage: string;
  targetLanguage: string;
  apiKey?: string;
}

interface TranslationMetadata {
  model: string;
  processingTime: number;
  characterCount: number;
  wordCount: number;
  estimatedCost?: number;
  cacheHit: boolean;
  timestamp: number;
}

interface TranslationResult {
  translatedText: string;
  detectedLanguage?: string;
  confidence?: number;
  metadata?: TranslationMetadata;
}

interface TranslationCache {
  [key: string]: TranslationResult;
}

interface TranslationHook {
  translateText: (text: string, options: TranslationOptions) => Promise<TranslationResult | null>;
  isTranslating: boolean;
  error: string | null;
  clearCache: () => void;
  getCacheSize: () => number;
  cancelTranslation: () => void;
  getTranslationStats: () => {
    totalTranslations: number;
    totalCharacters: number;
    averageProcessingTime: number;
    cacheSize: number;
    cacheHitRate: number;
  };
}

const useTranslation = (): TranslationHook => {
  const [isTranslating, setIsTranslating] = useState<boolean>(false);
  const [error, setError] = useState<string | null>(null);

  const cacheRef = useRef<TranslationCache>({});
  const abortControllerRef = useRef<AbortController | null>(null);
  const requestQueueRef = useRef<Array<{ text: string; options: TranslationOptions; resolve: (result: TranslationResult | null) => void; reject: (error: Error) => void }>>([]);
  const isProcessingRef = useRef<boolean>(false);

  // Load cache from localStorage on mount
  useEffect(() => {
    if (typeof window !== 'undefined') {
      try {
        const savedCache = localStorage.getItem('caption-ninja-translation-cache');
        if (savedCache) {
          const cache = JSON.parse(savedCache);
          // Filter out expired entries
          const now = Date.now();
          const validCache: TranslationCache = {};

          Object.entries(cache).forEach(([key, entry]: [string, any]) => {
            if (entry.timestamp && now - entry.timestamp < 24 * 60 * 60 * 1000) { // 24 hours
              validCache[key] = entry;
            }
          });

          cacheRef.current = validCache;
        }
      } catch (error) {
        console.warn('Failed to load translation cache:', error);
      }
    }
  }, []);

  // Save cache to localStorage when it changes
  const saveCache = useCallback(() => {
    if (typeof window !== 'undefined') {
      try {
        localStorage.setItem('caption-ninja-translation-cache', JSON.stringify(cacheRef.current));
      } catch (error) {
        console.warn('Failed to save translation cache:', error);
      }
    }
  }, []);

  // Generate cache key
  const getCacheKey = (text: string, sourceLanguage: string, targetLanguage: string): string => {
    return `${sourceLanguage}-${targetLanguage}-${text}`;
  };

  // Process translation queue to avoid overwhelming the API
  const processTranslationQueue = useCallback(async () => {
    if (isProcessingRef.current || requestQueueRef.current.length === 0) {
      return;
    }

    isProcessingRef.current = true;
    setIsTranslating(true);

    while (requestQueueRef.current.length > 0) {
      const request = requestQueueRef.current.shift();
      if (!request) continue;

      try {
        const result = await performTranslation(request.text, request.options);
        request.resolve(result);
      } catch (error) {
        request.reject(error as Error);
      }

      // Small delay between requests to avoid rate limiting
      if (requestQueueRef.current.length > 0) {
        await new Promise(resolve => setTimeout(resolve, 100));
      }
    }

    isProcessingRef.current = false;
    setIsTranslating(false);
  }, [performTranslation]);

  // Actual translation function
  const performTranslation = useCallback(async (
    text: string,
    options: TranslationOptions
  ): Promise<TranslationResult | null> => {
    const { sourceLanguage, targetLanguage, apiKey } = options;

    // Check cache first
    const cacheKey = getCacheKey(text, sourceLanguage, targetLanguage);
    const cachedResult = cacheRef.current[cacheKey];

    if (cachedResult) {
      // Check if cache entry is still valid (24 hours)
      const now = Date.now();
      if (cachedResult.metadata && now - cachedResult.metadata.timestamp < 24 * 60 * 60 * 1000) {
        return { ...cachedResult, metadata: { ...cachedResult.metadata, cacheHit: true } };
      } else {
        // Remove expired cache entry
        delete cacheRef.current[cacheKey];
      }
    }

    // Skip translation if source and target are the same
    if (sourceLanguage === targetLanguage) {
      const result: TranslationResult = {
        translatedText: text,
        detectedLanguage: sourceLanguage,
        confidence: 1.0,
        metadata: {
          model: 'passthrough',
          processingTime: 0,
          characterCount: text.length,
          wordCount: text.split(/\s+/).length,
          cacheHit: false,
          timestamp: Date.now()
        }
      };
      return result;
    }

    setError(null);

    try {
      const response = await fetch('/api/translate', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          text,
          sourceLanguage: sourceLanguage === 'auto' ? undefined : sourceLanguage,
          targetLanguage,
          apiKey
        }),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || `Translation failed: ${response.status}`);
      }

      const result: TranslationResult = await response.json();

      // Add timestamp to metadata
      if (result.metadata) {
        result.metadata.timestamp = Date.now();
      }

      // Cache the result
      cacheRef.current[cacheKey] = result;

      // Limit cache size and save to localStorage
      const cacheKeys = Object.keys(cacheRef.current);
      if (cacheKeys.length > 100) {
        const keysToDelete = cacheKeys.slice(0, cacheKeys.length - 100);
        keysToDelete.forEach(key => {
          delete cacheRef.current[key];
        });
      }

      saveCache();

      return result;
    } catch (err) {
      if (err instanceof Error) {
        setError(err.message);
        throw err;
      } else {
        const error = new Error('Translation failed with unknown error');
        setError(error.message);
        throw error;
      }
    }
  }, [saveCache]);

  // Main translation function with queueing
  const translateText = useCallback(async (
    text: string,
    options: TranslationOptions
  ): Promise<TranslationResult | null> => {
    if (!text.trim()) {
      return null;
    }

    return new Promise((resolve, reject) => {
      // Cancel previous requests for the same text
      requestQueueRef.current = requestQueueRef.current.filter(req => req.text !== text);

      // Add to queue
      requestQueueRef.current.push({ text, options, resolve, reject });

      // Process queue
      processTranslationQueue();
    });
  }, [processTranslationQueue]);

  // Clear translation cache
  const clearCache = useCallback(() => {
    cacheRef.current = {};
    if (typeof window !== 'undefined') {
      localStorage.removeItem('caption-ninja-translation-cache');
    }
  }, []);

  // Get cache size
  const getCacheSize = useCallback(() => {
    return Object.keys(cacheRef.current).length;
  }, []);

  // Cancel all pending translations
  const cancelTranslation = useCallback(() => {
    requestQueueRef.current = [];
    if (abortControllerRef.current) {
      abortControllerRef.current.abort();
      abortControllerRef.current = null;
    }
    setIsTranslating(false);
  }, []);

  // Get translation statistics
  const getTranslationStats = useCallback(() => {
    const cache = cacheRef.current;
    const entries = Object.values(cache);

    const totalTranslations = entries.length;
    const totalCharacters = entries.reduce((sum, entry) =>
      sum + (entry.metadata?.characterCount || 0), 0
    );
    const averageProcessingTime = entries.length > 0
      ? entries.reduce((sum, entry) => sum + (entry.metadata?.processingTime || 0), 0) / entries.length
      : 0;

    return {
      totalTranslations,
      totalCharacters,
      averageProcessingTime,
      cacheSize: totalTranslations,
      cacheHitRate: 0 // Would need to track this separately
    };
  }, []);

  return {
    translateText,
    isTranslating,
    error,
    clearCache,
    getCacheSize,
    cancelTranslation,
    getTranslationStats
  };
};

export default useTranslation;
