"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./src/hooks/useAutoTranslation.ts":
/*!*****************************************!*\
  !*** ./src/hooks/useAutoTranslation.ts ***!
  \*****************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _contexts_CaptionContext__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/contexts/CaptionContext */ \"(app-pages-browser)/./src/contexts/CaptionContext.tsx\");\n/* harmony import */ var _useTranslation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./useTranslation */ \"(app-pages-browser)/./src/hooks/useTranslation.ts\");\n/**\n * useAutoTranslation Hook\n * \n * React hook that automatically translates speech recognition results\n * when translation is enabled in the caption context.\n */ \n\n\nconst useAutoTranslation = function() {\n    let options = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : {};\n    const { debounceDelay = 500, translateInterim = false, maxRetries = 3 } = options;\n    const { state } = (0,_contexts_CaptionContext__WEBPACK_IMPORTED_MODULE_1__.useCaption)();\n    const actions = (0,_contexts_CaptionContext__WEBPACK_IMPORTED_MODULE_1__.useCaptionActions)();\n    const translation = (0,_useTranslation__WEBPACK_IMPORTED_MODULE_2__[\"default\"])();\n    const debounceTimeoutRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(null);\n    const lastTranslatedTextRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)('');\n    const retryCountRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(0);\n    // Get API key from localStorage\n    const getApiKey = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useAutoTranslation.useCallback[getApiKey]\": ()=>{\n            if (true) {\n                return localStorage.getItem('caption-ninja-google-api-key') || '';\n            }\n            return '';\n        }\n    }[\"useAutoTranslation.useCallback[getApiKey]\"], []);\n    // Translate text with retry logic\n    const translateWithRetry = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useAutoTranslation.useCallback[translateWithRetry]\": async function(text) {\n            let isInterim = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : false;\n            if (!text.trim()) return;\n            const apiKey = getApiKey();\n            if (!apiKey) {\n                console.warn('No Google Cloud Translation API key found');\n                return;\n            }\n            // Skip if source and target languages are the same\n            if (state.settings.sourceLanguage === state.settings.targetLanguage) {\n                actions.setTranslatedText(text);\n                return;\n            }\n            try {\n                actions.setTranslatingStatus(true);\n                actions.setTranslationError(null);\n                const result = await translation.translateText(text, {\n                    sourceLanguage: state.settings.sourceLanguage,\n                    targetLanguage: state.settings.targetLanguage,\n                    apiKey\n                });\n                if (result) {\n                    actions.setTranslatedText(result.translatedText);\n                    lastTranslatedTextRef.current = result.translatedText;\n                    retryCountRef.current = 0; // Reset retry count on success\n                } else {\n                    throw new Error('No translation result received');\n                }\n            } catch (error) {\n                console.error('Translation failed:', error);\n                // Retry logic\n                if (retryCountRef.current < maxRetries) {\n                    retryCountRef.current++;\n                    console.log(\"Retrying translation (attempt \".concat(retryCountRef.current, \"/\").concat(maxRetries, \")\"));\n                    // Exponential backoff\n                    const delay = Math.pow(2, retryCountRef.current - 1) * 1000;\n                    setTimeout({\n                        \"useAutoTranslation.useCallback[translateWithRetry]\": ()=>{\n                            translateWithRetry(text, isInterim);\n                        }\n                    }[\"useAutoTranslation.useCallback[translateWithRetry]\"], delay);\n                } else {\n                    const errorMessage = error instanceof Error ? error.message : 'Translation failed';\n                    actions.setTranslationError(errorMessage);\n                    retryCountRef.current = 0; // Reset for next translation\n                }\n            } finally{\n                actions.setTranslatingStatus(false);\n            }\n        }\n    }[\"useAutoTranslation.useCallback[translateWithRetry]\"], [\n        state.settings.sourceLanguage,\n        state.settings.targetLanguage,\n        translation,\n        actions,\n        getApiKey,\n        maxRetries\n    ]);\n    // Debounced translation function - use ref to avoid dependency issues\n    const debouncedTranslate = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useAutoTranslation.useCallback[debouncedTranslate]\": function(text) {\n            let isInterim = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : false;\n            // Clear existing timeout\n            if (debounceTimeoutRef.current) {\n                clearTimeout(debounceTimeoutRef.current);\n            }\n            // Set new timeout\n            debounceTimeoutRef.current = setTimeout({\n                \"useAutoTranslation.useCallback[debouncedTranslate]\": ()=>{\n                    // Use the current translateWithRetry function from ref to avoid stale closures\n                    const currentTranslateWithRetry = translateWithRetry;\n                    currentTranslateWithRetry(text, isInterim);\n                }\n            }[\"useAutoTranslation.useCallback[debouncedTranslate]\"], isInterim ? debounceDelay / 2 : debounceDelay); // Faster for interim results\n        }\n    }[\"useAutoTranslation.useCallback[debouncedTranslate]\"], [\n        debounceDelay\n    ]); // Remove translateWithRetry from dependencies to prevent cascades\n    // Auto-translate final transcript\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)({\n        \"useAutoTranslation.useEffect\": ()=>{\n            if (!state.settings.translationEnabled || !state.finalTranscript) {\n                return;\n            }\n            // Skip if already translated this text\n            if (state.finalTranscript === lastTranslatedTextRef.current) {\n                return;\n            }\n            debouncedTranslate(state.finalTranscript, false);\n        }\n    }[\"useAutoTranslation.useEffect\"], [\n        state.finalTranscript,\n        state.settings.translationEnabled,\n        debouncedTranslate\n    ]);\n    // Auto-translate interim transcript (if enabled)\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)({\n        \"useAutoTranslation.useEffect\": ()=>{\n            if (!state.settings.translationEnabled || !translateInterim || !state.interimTranscript) {\n                return;\n            }\n            debouncedTranslate(state.interimTranscript, true);\n        }\n    }[\"useAutoTranslation.useEffect\"], [\n        state.interimTranscript,\n        state.settings.translationEnabled,\n        translateInterim,\n        debouncedTranslate\n    ]);\n    // Clear translation when translation is disabled\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)({\n        \"useAutoTranslation.useEffect\": ()=>{\n            if (!state.settings.translationEnabled) {\n                actions.setTranslatedText('');\n                actions.setTranslationError(null);\n                lastTranslatedTextRef.current = '';\n            }\n        }\n    }[\"useAutoTranslation.useEffect\"], [\n        state.settings.translationEnabled,\n        actions\n    ]);\n    // Cleanup on unmount\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)({\n        \"useAutoTranslation.useEffect\": ()=>{\n            return ({\n                \"useAutoTranslation.useEffect\": ()=>{\n                    if (debounceTimeoutRef.current) {\n                        clearTimeout(debounceTimeoutRef.current);\n                    }\n                    translation.cancelTranslation();\n                }\n            })[\"useAutoTranslation.useEffect\"];\n        }\n    }[\"useAutoTranslation.useEffect\"], [\n        translation\n    ]);\n    // Manual translation function\n    const translateText = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useAutoTranslation.useCallback[translateText]\": (text)=>{\n            if (!text.trim()) return;\n            translateWithRetry(text, false);\n        }\n    }[\"useAutoTranslation.useCallback[translateText]\"], [\n        translateWithRetry\n    ]);\n    // Clear translation\n    const clearTranslation = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useAutoTranslation.useCallback[clearTranslation]\": ()=>{\n            actions.setTranslatedText('');\n            actions.setTranslationError(null);\n            lastTranslatedTextRef.current = '';\n            if (debounceTimeoutRef.current) {\n                clearTimeout(debounceTimeoutRef.current);\n            }\n        }\n    }[\"useAutoTranslation.useCallback[clearTranslation]\"], [\n        actions\n    ]);\n    return {\n        translateText,\n        clearTranslation,\n        isTranslating: state.isTranslating,\n        translationError: state.translationError,\n        translatedText: state.translatedText,\n        translationStats: translation.getTranslationStats()\n    };\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (useAutoTranslation);\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/hooks/useAutoTranslation.ts\n"));

/***/ })

});