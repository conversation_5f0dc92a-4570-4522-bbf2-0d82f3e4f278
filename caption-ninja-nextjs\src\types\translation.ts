/**
 * Translation Types
 * 
 * TypeScript type definitions for translation functionality.
 */

// Translation options
export interface TranslationOptions {
  sourceLanguage: string;
  targetLanguage: string;
  apiKey?: string;
  model?: string;
  format?: 'text' | 'html';
}

// Translation result
export interface TranslationResult {
  translatedText: string;
  detectedLanguage?: string;
  confidence?: number;
  alternatives?: TranslationAlternative[];
  metadata?: TranslationMetadata;
}

// Translation alternative
export interface TranslationAlternative {
  text: string;
  confidence: number;
  backTranslation?: string;
}

// Translation metadata
export interface TranslationMetadata {
  model: string;
  processingTime: number;
  characterCount: number;
  wordCount: number;
  estimatedCost?: number;
  cacheHit: boolean;
}

// Translation error types
export type TranslationErrorType = 
  | 'invalid_api_key'
  | 'quota_exceeded'
  | 'language_not_supported'
  | 'text_too_long'
  | 'network_error'
  | 'service_unavailable'
  | 'invalid_request'
  | 'permission_denied'
  | 'unknown_error';

// Translation error
export interface TranslationError {
  type: TranslationErrorType;
  message: string;
  code?: string;
  details?: any;
  timestamp: number;
  retryable: boolean;
}

// Translation status
export type TranslationStatus = 
  | 'idle'
  | 'translating'
  | 'completed'
  | 'error'
  | 'cancelled';

// Translation state
export interface TranslationState {
  status: TranslationStatus;
  isEnabled: boolean;
  sourceLanguage: string;
  targetLanguage: string;
  currentTranslation: TranslationResult | null;
  error: TranslationError | null;
  queue: TranslationRequest[];
  cache: TranslationCache;
  metrics: TranslationMetrics;
}

// Translation request
export interface TranslationRequest {
  id: string;
  text: string;
  options: TranslationOptions;
  timestamp: number;
  priority: 'low' | 'normal' | 'high';
  retryCount: number;
  maxRetries: number;
}

// Translation cache entry
export interface TranslationCacheEntry {
  key: string;
  result: TranslationResult;
  timestamp: number;
  accessCount: number;
  lastAccessed: number;
  expiresAt: number;
}

// Translation cache
export interface TranslationCache {
  entries: { [key: string]: TranslationCacheEntry };
  maxSize: number;
  currentSize: number;
  hitRate: number;
  totalRequests: number;
  totalHits: number;
}

// Translation metrics
export interface TranslationMetrics {
  totalTranslations: number;
  totalCharacters: number;
  totalWords: number;
  averageProcessingTime: number;
  successRate: number;
  errorRate: number;
  cacheHitRate: number;
  languagePairStats: { [pair: string]: LanguagePairStats };
  dailyUsage: { [date: string]: number };
  estimatedCost: number;
}

// Language pair statistics
export interface LanguagePairStats {
  count: number;
  averageProcessingTime: number;
  successRate: number;
  averageConfidence: number;
  totalCharacters: number;
}

// Translation language
export interface TranslationLanguage {
  code: string;
  name: string;
  nativeName: string;
  flag?: string;
  isSupported: boolean;
  confidence?: number;
  region?: string;
  script?: string;
  direction: 'ltr' | 'rtl';
}

// Translation service configuration
export interface TranslationServiceConfig {
  provider: 'google' | 'microsoft' | 'amazon' | 'custom';
  apiKey: string;
  endpoint?: string;
  model?: string;
  maxTextLength: number;
  timeout: number;
  retryAttempts: number;
  retryDelay: number;
  enableCaching: boolean;
  cacheSize: number;
  cacheTTL: number;
  enableBatching: boolean;
  batchSize: number;
  batchDelay: number;
}

// Translation quality assessment
export interface TranslationQualityAssessment {
  score: number; // 0-100
  fluency: number;
  adequacy: number;
  consistency: number;
  terminology: number;
  grammar: number;
  issues: TranslationIssue[];
  suggestions: string[];
}

// Translation issue
export interface TranslationIssue {
  type: 'grammar' | 'terminology' | 'fluency' | 'consistency' | 'cultural';
  severity: 'low' | 'medium' | 'high';
  description: string;
  position?: {
    start: number;
    end: number;
  };
  suggestion?: string;
}

// Translation hook return type
export interface TranslationHook {
  translateText: (text: string, options: TranslationOptions) => Promise<TranslationResult | null>;
  isTranslating: boolean;
  error: string | null;
  clearCache: () => void;
  getCacheSize: () => number;
  getMetrics: () => TranslationMetrics;
  cancelTranslation: () => void;
  retryTranslation: () => Promise<TranslationResult | null>;
}

// Translation provider props
export interface TranslationProviderProps {
  children: React.ReactNode;
  config: TranslationServiceConfig;
  onError?: (error: TranslationError) => void;
  onSuccess?: (result: TranslationResult) => void;
}

// Translation context value
export interface TranslationContextValue {
  state: TranslationState;
  actions: {
    translate: (text: string, options: TranslationOptions) => Promise<TranslationResult | null>;
    setLanguages: (source: string, target: string) => void;
    toggleEnabled: () => void;
    clearCache: () => void;
    cancelAll: () => void;
  };
  config: TranslationServiceConfig;
  metrics: TranslationMetrics;
}

// Translation batch request
export interface TranslationBatchRequest {
  id: string;
  texts: string[];
  options: TranslationOptions;
  timestamp: number;
  callback?: (results: TranslationResult[]) => void;
}

// Translation streaming result
export interface TranslationStreamingResult {
  partialText: string;
  isComplete: boolean;
  confidence?: number;
  alternatives?: string[];
}

// Translation feedback
export interface TranslationFeedback {
  translationId: string;
  rating: number; // 1-5
  issues: string[];
  suggestions: string[];
  correctedText?: string;
  timestamp: number;
  userId?: string;
}

// Translation glossary entry
export interface TranslationGlossaryEntry {
  sourceText: string;
  targetText: string;
  context?: string;
  domain?: string;
  notes?: string;
  createdAt: number;
  updatedAt: number;
}

// Translation glossary
export interface TranslationGlossary {
  id: string;
  name: string;
  sourceLanguage: string;
  targetLanguage: string;
  entries: TranslationGlossaryEntry[];
  isActive: boolean;
  createdAt: number;
  updatedAt: number;
}

// Translation model information
export interface TranslationModelInfo {
  id: string;
  name: string;
  provider: string;
  version: string;
  supportedLanguages: string[];
  capabilities: string[];
  performance: {
    speed: number;
    accuracy: number;
    cost: number;
  };
  limitations: {
    maxTextLength: number;
    rateLimit: number;
    dailyQuota: number;
  };
}

// Translation usage statistics
export interface TranslationUsageStats {
  period: 'daily' | 'weekly' | 'monthly' | 'yearly';
  totalTranslations: number;
  totalCharacters: number;
  totalCost: number;
  topLanguagePairs: { pair: string; count: number }[];
  averageQuality: number;
  errorRate: number;
  peakUsageTime: string;
  trends: {
    translations: number[];
    characters: number[];
    cost: number[];
    quality: number[];
  };
}
