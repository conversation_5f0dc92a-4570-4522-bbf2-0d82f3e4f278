"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./src/hooks/useSpeechRecognition.ts":
/*!*******************************************!*\
  !*** ./src/hooks/useSpeechRecognition.ts ***!
  \*******************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n/**\n * useSpeechRecognition Hook\n * \n * React hook for managing speech recognition functionality.\n * Based on the speech recognition implementation from index.html.\n */ \nconst useSpeechRecognition = function() {\n    let options = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : {};\n    // Memoize default options to prevent recreation on every render\n    const defaultOptions = (0,react__WEBPACK_IMPORTED_MODULE_0__.useMemo)({\n        \"useSpeechRecognition.useMemo[defaultOptions]\": ()=>({\n                language: 'en-US',\n                continuous: true,\n                interimResults: true,\n                maxAlternatives: 1\n            })\n    }[\"useSpeechRecognition.useMemo[defaultOptions]\"], []);\n    // Memoize merged options to prevent infinite re-renders\n    const mergedOptions = (0,react__WEBPACK_IMPORTED_MODULE_0__.useMemo)({\n        \"useSpeechRecognition.useMemo[mergedOptions]\": ()=>({\n                ...defaultOptions,\n                ...options\n            })\n    }[\"useSpeechRecognition.useMemo[mergedOptions]\"], [\n        defaultOptions,\n        options.language,\n        options.continuous,\n        options.interimResults,\n        options.maxAlternatives\n    ]);\n    const [finalTranscript, setFinalTranscript] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)('');\n    const [interimTranscript, setInterimTranscript] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)('');\n    const [isListening, setIsListening] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(false);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(null);\n    const [isSupported, setIsSupported] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(false);\n    const [recognitionState, setRecognitionState] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)('idle');\n    const recognitionRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(null);\n    const optionsRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(mergedOptions);\n    const restartTimeoutRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(null);\n    const restartAttemptsRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(0);\n    const isStoppedManuallyRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(false);\n    const MAX_RESTART_ATTEMPTS = 3;\n    const RESTART_DELAY = 1000;\n    // Update options ref when merged options change\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)({\n        \"useSpeechRecognition.useEffect\": ()=>{\n            optionsRef.current = mergedOptions;\n        }\n    }[\"useSpeechRecognition.useEffect\"], [\n        mergedOptions\n    ]);\n    // Initialize speech recognition\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)({\n        \"useSpeechRecognition.useEffect\": ()=>{\n            if (false) {}\n            const SpeechRecognition = window.SpeechRecognition || window.webkitSpeechRecognition;\n            if (!SpeechRecognition) {\n                setIsSupported(false);\n                setError('Speech recognition is not supported in this browser');\n                return;\n            }\n            setIsSupported(true);\n            const recognition = new SpeechRecognition();\n            recognitionRef.current = recognition;\n            // Configure recognition with current merged options\n            recognition.continuous = mergedOptions.continuous;\n            recognition.interimResults = mergedOptions.interimResults;\n            recognition.lang = mergedOptions.language;\n            recognition.maxAlternatives = mergedOptions.maxAlternatives;\n            // Event handlers with state machine\n            recognition.onstart = ({\n                \"useSpeechRecognition.useEffect\": ()=>{\n                    console.log('Speech recognition started');\n                    setIsListening(true);\n                    setRecognitionState('listening');\n                    setError(null);\n                    // Reset restart attempts on successful start\n                    restartAttemptsRef.current = 0;\n                }\n            })[\"useSpeechRecognition.useEffect\"];\n            recognition.onend = ({\n                \"useSpeechRecognition.useEffect\": ()=>{\n                    console.log('Speech recognition ended');\n                    setIsListening(false);\n                    // Only attempt restart if we're not in stopping state and not stopped manually\n                    if (recognitionState !== 'stopping' && !isStoppedManuallyRef.current && mergedOptions.continuous) {\n                        if (restartAttemptsRef.current < MAX_RESTART_ATTEMPTS) {\n                            console.log(\"Attempting to restart recognition (attempt \".concat(restartAttemptsRef.current + 1, \")\"));\n                            setRecognitionState('restarting');\n                            restartAttemptsRef.current++;\n                            // Use exponential backoff for restart attempts\n                            const backoffDelay = RESTART_DELAY * Math.pow(2, restartAttemptsRef.current - 1);\n                            restartTimeoutRef.current = setTimeout({\n                                \"useSpeechRecognition.useEffect\": ()=>{\n                                    try {\n                                        if (recognitionRef.current && !isStoppedManuallyRef.current && recognitionState !== 'stopping') {\n                                            setRecognitionState('starting');\n                                            recognitionRef.current.start();\n                                        }\n                                    } catch (err) {\n                                        console.error('Failed to restart recognition:', err);\n                                        setError('Failed to restart speech recognition');\n                                        setRecognitionState('error');\n                                    }\n                                }\n                            }[\"useSpeechRecognition.useEffect\"], backoffDelay);\n                        } else {\n                            setError('Maximum restart attempts reached. Please restart manually.');\n                            setRecognitionState('error');\n                        }\n                    } else {\n                        setRecognitionState('idle');\n                    }\n                }\n            })[\"useSpeechRecognition.useEffect\"];\n            recognition.onerror = ({\n                \"useSpeechRecognition.useEffect\": (event)=>{\n                    console.error('Speech recognition error:', event);\n                    // Handle different error types with state machine\n                    switch(event.error){\n                        case 'no-speech':\n                            setError('No speech detected. Still listening...');\n                            break;\n                        case 'audio-capture':\n                            setError('No microphone detected. Please check your microphone.');\n                            setIsListening(false);\n                            setRecognitionState('error');\n                            break;\n                        case 'not-allowed':\n                            setError('Microphone access denied. Please allow microphone access.');\n                            setIsListening(false);\n                            setRecognitionState('error');\n                            break;\n                        case 'network':\n                            setError('Network error occurred. Will attempt to reconnect...');\n                            setRecognitionState('error');\n                            break;\n                        case 'aborted':\n                            console.log('Recognition aborted');\n                            if (!isStoppedManuallyRef.current) {\n                                setError('Recognition was aborted unexpectedly');\n                                setRecognitionState('error');\n                            } else {\n                                setRecognitionState('idle');\n                            }\n                            break;\n                        default:\n                            setError(\"Speech recognition error: \".concat(event.error));\n                            setRecognitionState('error');\n                            setIsListening(false);\n                    }\n                }\n            })[\"useSpeechRecognition.useEffect\"];\n            recognition.onresult = ({\n                \"useSpeechRecognition.useEffect\": (event)=>{\n                    if (typeof event.results === 'undefined') {\n                        console.log('Undefined results in event:', event);\n                        return;\n                    }\n                    let interim = '';\n                    let final = '';\n                    // Process only new results since last event\n                    for(let i = event.resultIndex; i < event.results.length; i++){\n                        const transcript = event.results[i][0].transcript.trim();\n                        if (event.results[i].isFinal) {\n                            final += (final ? ' ' : '') + transcript;\n                        } else {\n                            interim += (interim ? ' ' : '') + transcript;\n                        }\n                    }\n                    setInterimTranscript(interim);\n                    if (final) {\n                        setFinalTranscript({\n                            \"useSpeechRecognition.useEffect\": (prev)=>prev + (prev ? ' ' : '') + final\n                        }[\"useSpeechRecognition.useEffect\"]);\n                        // Reset restart attempts on successful recognition\n                        restartAttemptsRef.current = 0;\n                    }\n                }\n            })[\"useSpeechRecognition.useEffect\"];\n            return ({\n                \"useSpeechRecognition.useEffect\": ()=>{\n                    if (recognition) {\n                        recognition.stop();\n                    }\n                }\n            })[\"useSpeechRecognition.useEffect\"];\n        }\n    }[\"useSpeechRecognition.useEffect\"], [\n        mergedOptions\n    ]);\n    // Update language when it changes\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)({\n        \"useSpeechRecognition.useEffect\": ()=>{\n            if (recognitionRef.current && mergedOptions.language) {\n                recognitionRef.current.lang = mergedOptions.language;\n            }\n        }\n    }[\"useSpeechRecognition.useEffect\"], [\n        mergedOptions.language\n    ]);\n    const startListening = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useSpeechRecognition.useCallback[startListening]\": ()=>{\n            if (!recognitionRef.current || !isSupported) {\n                setError('Speech recognition is not available');\n                setRecognitionState('error');\n                return;\n            }\n            // Prevent starting if already in a transitional state\n            if (recognitionState === 'starting' || recognitionState === 'restarting' || isListening) {\n                return;\n            }\n            // Clear any pending restart timeout\n            if (restartTimeoutRef.current) {\n                clearTimeout(restartTimeoutRef.current);\n                restartTimeoutRef.current = null;\n            }\n            isStoppedManuallyRef.current = false;\n            restartAttemptsRef.current = 0;\n            setError(null);\n            setRecognitionState('starting');\n            try {\n                recognitionRef.current.start();\n                console.log('Speech recognition start requested');\n            } catch (err) {\n                console.error('Failed to start speech recognition:', err);\n                if (err instanceof Error && err.name === 'InvalidStateError') {\n                    // Recognition is already running, try to stop and restart\n                    try {\n                        setRecognitionState('stopping');\n                        recognitionRef.current.stop();\n                        setTimeout({\n                            \"useSpeechRecognition.useCallback[startListening]\": ()=>{\n                                try {\n                                    if (!isStoppedManuallyRef.current) {\n                                        setRecognitionState('starting');\n                                        recognitionRef.current.start();\n                                    }\n                                } catch (retryErr) {\n                                    setError('Failed to start speech recognition after retry');\n                                    setRecognitionState('error');\n                                }\n                            }\n                        }[\"useSpeechRecognition.useCallback[startListening]\"], 100);\n                    } catch (stopErr) {\n                        setError('Failed to restart speech recognition');\n                        setRecognitionState('error');\n                    }\n                } else {\n                    setError('Failed to start speech recognition');\n                    setRecognitionState('error');\n                }\n            }\n        }\n    }[\"useSpeechRecognition.useCallback[startListening]\"], [\n        recognitionState,\n        isListening,\n        isSupported\n    ]);\n    const stopListening = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useSpeechRecognition.useCallback[stopListening]\": ()=>{\n            if (!recognitionRef.current) return;\n            isStoppedManuallyRef.current = true;\n            setRecognitionState('stopping');\n            // Clear any pending restart timeout\n            if (restartTimeoutRef.current) {\n                clearTimeout(restartTimeoutRef.current);\n                restartTimeoutRef.current = null;\n            }\n            try {\n                recognitionRef.current.stop();\n                console.log('Speech recognition stopped manually');\n            } catch (err) {\n                console.error('Failed to stop speech recognition:', err);\n                setError('Failed to stop speech recognition');\n                setRecognitionState('error');\n            }\n        }\n    }[\"useSpeechRecognition.useCallback[stopListening]\"], []);\n    const resetTranscript = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useSpeechRecognition.useCallback[resetTranscript]\": ()=>{\n            setFinalTranscript('');\n            setInterimTranscript('');\n            setError(null);\n            restartAttemptsRef.current = 0;\n        }\n    }[\"useSpeechRecognition.useCallback[resetTranscript]\"], []);\n    const setLanguage = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useSpeechRecognition.useCallback[setLanguage]\": (language)=>{\n            if (recognitionRef.current) {\n                recognitionRef.current.lang = language;\n                optionsRef.current.language = language;\n            }\n        }\n    }[\"useSpeechRecognition.useCallback[setLanguage]\"], []);\n    // Cleanup on unmount\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)({\n        \"useSpeechRecognition.useEffect\": ()=>{\n            return ({\n                \"useSpeechRecognition.useEffect\": ()=>{\n                    if (restartTimeoutRef.current) {\n                        clearTimeout(restartTimeoutRef.current);\n                    }\n                    if (recognitionRef.current) {\n                        isStoppedManuallyRef.current = true;\n                        try {\n                            recognitionRef.current.stop();\n                        } catch (err) {\n                            console.error('Error stopping recognition on cleanup:', err);\n                        }\n                    }\n                }\n            })[\"useSpeechRecognition.useEffect\"];\n        }\n    }[\"useSpeechRecognition.useEffect\"], []);\n    return {\n        finalTranscript,\n        interimTranscript,\n        isListening,\n        error,\n        isSupported,\n        recognitionState,\n        startListening,\n        stopListening,\n        resetTranscript,\n        setLanguage\n    };\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (useSpeechRecognition);\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/hooks/useSpeechRecognition.ts\n"));

/***/ })

});