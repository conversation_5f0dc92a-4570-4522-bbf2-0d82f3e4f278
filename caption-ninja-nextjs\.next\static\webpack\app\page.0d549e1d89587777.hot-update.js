"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./src/hooks/useAutoTranslation.ts":
/*!*****************************************!*\
  !*** ./src/hooks/useAutoTranslation.ts ***!
  \*****************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _contexts_CaptionContext__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/contexts/CaptionContext */ \"(app-pages-browser)/./src/contexts/CaptionContext.tsx\");\n/* harmony import */ var _useTranslation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./useTranslation */ \"(app-pages-browser)/./src/hooks/useTranslation.ts\");\n/**\n * useAutoTranslation Hook\n * \n * React hook that automatically translates speech recognition results\n * when translation is enabled in the caption context.\n */ \n\n\nconst useAutoTranslation = function() {\n    let options = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : {};\n    const { debounceDelay = 500, translateInterim = false, maxRetries = 3 } = options;\n    const { state } = (0,_contexts_CaptionContext__WEBPACK_IMPORTED_MODULE_1__.useCaption)();\n    const actions = (0,_contexts_CaptionContext__WEBPACK_IMPORTED_MODULE_1__.useCaptionActions)();\n    const translation = (0,_useTranslation__WEBPACK_IMPORTED_MODULE_2__[\"default\"])();\n    const debounceTimeoutRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(null);\n    const lastTranslatedTextRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)('');\n    const retryCountRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(0);\n    const translateWithRetryRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)();\n    // Get API key from localStorage\n    const getApiKey = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useAutoTranslation.useCallback[getApiKey]\": ()=>{\n            if (true) {\n                return localStorage.getItem('caption-ninja-google-api-key') || '';\n            }\n            return '';\n        }\n    }[\"useAutoTranslation.useCallback[getApiKey]\"], []);\n    // Translate text with retry logic\n    const translateWithRetry = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useAutoTranslation.useCallback[translateWithRetry]\": async function(text) {\n            let isInterim = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : false;\n            if (!text.trim()) return;\n            const apiKey = getApiKey();\n            if (!apiKey) {\n                console.warn('No Google Cloud Translation API key found');\n                return;\n            }\n            // Skip if source and target languages are the same\n            if (state.settings.sourceLanguage === state.settings.targetLanguage) {\n                actions.setTranslatedText(text);\n                return;\n            }\n            try {\n                actions.setTranslatingStatus(true);\n                actions.setTranslationError(null);\n                const result = await translation.translateText(text, {\n                    sourceLanguage: state.settings.sourceLanguage,\n                    targetLanguage: state.settings.targetLanguage,\n                    apiKey\n                });\n                if (result) {\n                    actions.setTranslatedText(result.translatedText);\n                    lastTranslatedTextRef.current = result.translatedText;\n                    retryCountRef.current = 0; // Reset retry count on success\n                } else {\n                    throw new Error('No translation result received');\n                }\n            } catch (error) {\n                console.error('Translation failed:', error);\n                // Retry logic\n                if (retryCountRef.current < maxRetries) {\n                    retryCountRef.current++;\n                    console.log(\"Retrying translation (attempt \".concat(retryCountRef.current, \"/\").concat(maxRetries, \")\"));\n                    // Exponential backoff\n                    const delay = Math.pow(2, retryCountRef.current - 1) * 1000;\n                    setTimeout({\n                        \"useAutoTranslation.useCallback[translateWithRetry]\": ()=>{\n                            translateWithRetry(text, isInterim);\n                        }\n                    }[\"useAutoTranslation.useCallback[translateWithRetry]\"], delay);\n                } else {\n                    const errorMessage = error instanceof Error ? error.message : 'Translation failed';\n                    actions.setTranslationError(errorMessage);\n                    retryCountRef.current = 0; // Reset for next translation\n                }\n            } finally{\n                actions.setTranslatingStatus(false);\n            }\n        }\n    }[\"useAutoTranslation.useCallback[translateWithRetry]\"], [\n        state.settings.sourceLanguage,\n        state.settings.targetLanguage,\n        translation,\n        actions,\n        getApiKey,\n        maxRetries\n    ]);\n    // Update ref to current function to avoid stale closures\n    translateWithRetryRef.current = translateWithRetry;\n    // Debounced translation function - use ref to avoid dependency issues\n    const debouncedTranslate = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useAutoTranslation.useCallback[debouncedTranslate]\": function(text) {\n            let isInterim = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : false;\n            // Clear existing timeout\n            if (debounceTimeoutRef.current) {\n                clearTimeout(debounceTimeoutRef.current);\n            }\n            // Set new timeout\n            debounceTimeoutRef.current = setTimeout({\n                \"useAutoTranslation.useCallback[debouncedTranslate]\": ()=>{\n                    // Use the current translateWithRetry function from ref to avoid stale closures\n                    const currentTranslateWithRetry = translateWithRetry;\n                    currentTranslateWithRetry(text, isInterim);\n                }\n            }[\"useAutoTranslation.useCallback[debouncedTranslate]\"], isInterim ? debounceDelay / 2 : debounceDelay); // Faster for interim results\n        }\n    }[\"useAutoTranslation.useCallback[debouncedTranslate]\"], [\n        debounceDelay\n    ]); // Remove translateWithRetry from dependencies to prevent cascades\n    // Auto-translate final transcript\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)({\n        \"useAutoTranslation.useEffect\": ()=>{\n            if (!state.settings.translationEnabled || !state.finalTranscript) {\n                return;\n            }\n            // Skip if already translated this text\n            if (state.finalTranscript === lastTranslatedTextRef.current) {\n                return;\n            }\n            debouncedTranslate(state.finalTranscript, false);\n        }\n    }[\"useAutoTranslation.useEffect\"], [\n        state.finalTranscript,\n        state.settings.translationEnabled,\n        debouncedTranslate\n    ]);\n    // Auto-translate interim transcript (if enabled)\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)({\n        \"useAutoTranslation.useEffect\": ()=>{\n            if (!state.settings.translationEnabled || !translateInterim || !state.interimTranscript) {\n                return;\n            }\n            debouncedTranslate(state.interimTranscript, true);\n        }\n    }[\"useAutoTranslation.useEffect\"], [\n        state.interimTranscript,\n        state.settings.translationEnabled,\n        translateInterim,\n        debouncedTranslate\n    ]);\n    // Clear translation when translation is disabled\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)({\n        \"useAutoTranslation.useEffect\": ()=>{\n            if (!state.settings.translationEnabled) {\n                actions.setTranslatedText('');\n                actions.setTranslationError(null);\n                lastTranslatedTextRef.current = '';\n            }\n        }\n    }[\"useAutoTranslation.useEffect\"], [\n        state.settings.translationEnabled,\n        actions\n    ]);\n    // Cleanup on unmount\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)({\n        \"useAutoTranslation.useEffect\": ()=>{\n            return ({\n                \"useAutoTranslation.useEffect\": ()=>{\n                    if (debounceTimeoutRef.current) {\n                        clearTimeout(debounceTimeoutRef.current);\n                    }\n                    translation.cancelTranslation();\n                }\n            })[\"useAutoTranslation.useEffect\"];\n        }\n    }[\"useAutoTranslation.useEffect\"], [\n        translation\n    ]);\n    // Manual translation function\n    const translateText = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useAutoTranslation.useCallback[translateText]\": (text)=>{\n            if (!text.trim()) return;\n            translateWithRetry(text, false);\n        }\n    }[\"useAutoTranslation.useCallback[translateText]\"], [\n        translateWithRetry\n    ]);\n    // Clear translation\n    const clearTranslation = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useAutoTranslation.useCallback[clearTranslation]\": ()=>{\n            actions.setTranslatedText('');\n            actions.setTranslationError(null);\n            lastTranslatedTextRef.current = '';\n            if (debounceTimeoutRef.current) {\n                clearTimeout(debounceTimeoutRef.current);\n            }\n        }\n    }[\"useAutoTranslation.useCallback[clearTranslation]\"], [\n        actions\n    ]);\n    return {\n        translateText,\n        clearTranslation,\n        isTranslating: state.isTranslating,\n        translationError: state.translationError,\n        translatedText: state.translatedText,\n        translationStats: translation.getTranslationStats()\n    };\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (useAutoTranslation);\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/hooks/useAutoTranslation.ts\n"));

/***/ })

});