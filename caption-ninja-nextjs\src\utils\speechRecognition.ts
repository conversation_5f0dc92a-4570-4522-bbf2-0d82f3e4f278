/**
 * Speech Recognition Utilities
 * 
 * Utility functions for speech recognition functionality.
 * Based on the speech recognition logic from index.html.
 */

// Language codes supported by Web Speech API
export const SUPPORTED_LANGUAGES = [
  { code: 'en-US', name: 'English (United States)', flag: '🇺🇸' },
  { code: 'en-GB', name: 'English (United Kingdom)', flag: '🇬🇧' },
  { code: 'ha-NG', name: 'Hausa (Nigeria)', flag: '🇳🇬' },
  { code: 'es-ES', name: 'Spanish (Spain)', flag: '🇪🇸' },
  { code: 'es-MX', name: 'Spanish (Mexico)', flag: '🇲🇽' },
  { code: 'fr-FR', name: 'French (France)', flag: '🇫🇷' },
  { code: 'de-DE', name: 'German (Germany)', flag: '🇩🇪' },
  { code: 'it-IT', name: 'Italian (Italy)', flag: '🇮🇹' },
  { code: 'pt-BR', name: 'Portuguese (Brazil)', flag: '🇧🇷' },
  { code: 'pt-PT', name: 'Portuguese (Portugal)', flag: '🇵🇹' },
  { code: 'ru-RU', name: 'Russian (Russia)', flag: '🇷🇺' },
  { code: 'ja-<PERSON>', name: 'Japanese (Japan)', flag: '🇯🇵' },
  { code: 'ko-KR', name: 'Korean (South Korea)', flag: '🇰🇷' },
  { code: 'zh-CN', name: 'Chinese (Simplified)', flag: '🇨🇳' },
  { code: 'zh-TW', name: 'Chinese (Traditional)', flag: '🇹🇼' },
  { code: 'ar-SA', name: 'Arabic (Saudi Arabia)', flag: '🇸🇦' },
  { code: 'hi-IN', name: 'Hindi (India)', flag: '🇮🇳' },
  { code: 'th-TH', name: 'Thai (Thailand)', flag: '🇹🇭' },
  { code: 'vi-VN', name: 'Vietnamese (Vietnam)', flag: '🇻🇳' },
  { code: 'tr-TR', name: 'Turkish (Turkey)', flag: '🇹🇷' },
  { code: 'pl-PL', name: 'Polish (Poland)', flag: '🇵🇱' },
  { code: 'nl-NL', name: 'Dutch (Netherlands)', flag: '🇳🇱' },
  { code: 'sv-SE', name: 'Swedish (Sweden)', flag: '🇸🇪' },
  { code: 'da-DK', name: 'Danish (Denmark)', flag: '🇩🇰' },
  { code: 'no-NO', name: 'Norwegian (Norway)', flag: '🇳🇴' },
  { code: 'fi-FI', name: 'Finnish (Finland)', flag: '🇫🇮' },
];

// Check if speech recognition is supported
export const isSpeechRecognitionSupported = (): boolean => {
  if (typeof window === 'undefined') return false;
  return !!(window.SpeechRecognition || window.webkitSpeechRecognition);
};

// Get speech recognition constructor
export const getSpeechRecognition = (): any => {
  if (typeof window === 'undefined') return null;
  return window.SpeechRecognition || window.webkitSpeechRecognition || null;
};

// Detect text direction (RTL/LTR) based on language
export const detectTextDirection = (languageCode: string): 'rtl' | 'ltr' => {
  const rtlLanguages = ['ar', 'he', 'fa', 'ur'];
  const langPrefix = languageCode.split('-')[0];
  return rtlLanguages.includes(langPrefix) ? 'rtl' : 'ltr';
};

// Get language display name
export const getLanguageDisplayName = (languageCode: string): string => {
  const language = SUPPORTED_LANGUAGES.find(lang => lang.code === languageCode);
  return language ? language.name : languageCode;
};

// Get language flag emoji
export const getLanguageFlag = (languageCode: string): string => {
  const language = SUPPORTED_LANGUAGES.find(lang => lang.code === languageCode);
  return language ? language.flag : '🌐';
};

// Validate language code
export const isValidLanguageCode = (languageCode: string): boolean => {
  return SUPPORTED_LANGUAGES.some(lang => lang.code === languageCode);
};

// Get default language based on browser locale
export const getDefaultLanguage = (): string => {
  if (typeof window === 'undefined') return 'en-US';
  
  const browserLang = navigator.language || 'en-US';
  
  // Try to find exact match first
  if (isValidLanguageCode(browserLang)) {
    return browserLang;
  }
  
  // Try to find language family match (e.g., 'en' for 'en-AU')
  const langPrefix = browserLang.split('-')[0];
  const familyMatch = SUPPORTED_LANGUAGES.find(lang => 
    lang.code.startsWith(langPrefix + '-')
  );
  
  return familyMatch ? familyMatch.code : 'en-US';
};

// Format confidence score
export const formatConfidence = (confidence: number): string => {
  return `${Math.round(confidence * 100)}%`;
};

// Clean transcript text (remove extra spaces, normalize punctuation)
export const cleanTranscript = (text: string): string => {
  return text
    .trim()
    .replace(/\s+/g, ' ') // Replace multiple spaces with single space
    .replace(/\s+([.!?])/g, '$1') // Remove space before punctuation
    .replace(/([.!?])\s*([a-zA-Z])/g, '$1 $2'); // Ensure space after punctuation
};

// Split transcript into sentences
export const splitIntoSentences = (text: string): string[] => {
  // Simple sentence splitting - can be enhanced for better accuracy
  return text
    .split(/[.!?]+/)
    .map(sentence => sentence.trim())
    .filter(sentence => sentence.length > 0);
};

// Estimate speech duration based on text length and speaking rate
export const estimateSpeechDuration = (text: string, wordsPerMinute = 150): number => {
  const wordCount = text.split(/\s+/).length;
  const durationMinutes = wordCount / wordsPerMinute;
  return Math.max(durationMinutes * 60 * 1000, 1000); // Minimum 1 second
};

// Check if text contains specific language characters
export const containsLanguageSpecificChars = (text: string, languageCode: string): boolean => {
  const langPrefix = languageCode.split('-')[0];
  
  const languagePatterns: { [key: string]: RegExp } = {
    'ar': /[\u0600-\u06FF]/,
    'zh': /[\u4e00-\u9fff]/,
    'ja': /[\u3040-\u309f\u30a0-\u30ff\u4e00-\u9fff]/,
    'ko': /[\uac00-\ud7af]/,
    'hi': /[\u0900-\u097f]/,
    'th': /[\u0e00-\u0e7f]/,
    'ru': /[\u0400-\u04ff]/,
  };
  
  const pattern = languagePatterns[langPrefix];
  return pattern ? pattern.test(text) : false;
};

// Generate unique ID for transcript entries
export const generateTranscriptId = (): string => {
  return `transcript-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
};

// Parse speech recognition error
export const parseSpeechRecognitionError = (error: string): string => {
  const errorMessages: { [key: string]: string } = {
    'no-speech': 'No speech was detected. Please try speaking again.',
    'audio-capture': 'Audio capture failed. Please check your microphone.',
    'not-allowed': 'Microphone access was denied. Please allow microphone access.',
    'network': 'Network error occurred. Please check your internet connection.',
    'service-not-allowed': 'Speech recognition service is not allowed.',
    'bad-grammar': 'Grammar error in speech recognition.',
    'language-not-supported': 'The selected language is not supported.',
  };
  
  return errorMessages[error] || `Speech recognition error: ${error}`;
};

// Check microphone permissions
export const checkMicrophonePermissions = async (): Promise<boolean> => {
  if (typeof window === 'undefined' || !navigator.permissions) {
    return false;
  }
  
  try {
    const permission = await navigator.permissions.query({ name: 'microphone' as PermissionName });
    return permission.state === 'granted';
  } catch (error) {
    // Fallback: try to access microphone directly
    try {
      const stream = await navigator.mediaDevices.getUserMedia({ audio: true });
      stream.getTracks().forEach(track => track.stop());
      return true;
    } catch {
      return false;
    }
  }
};
