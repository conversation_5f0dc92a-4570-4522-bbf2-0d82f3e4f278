"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./src/hooks/useMediaStream.ts":
/*!*************************************!*\
  !*** ./src/hooks/useMediaStream.ts ***!
  \*************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n/**\n * useMediaStream Hook\n * \n * React hook for managing camera and microphone access via getUserMedia.\n * Provides camera device selection and stream management.\n */ \nconst useMediaStream = ()=>{\n    const [stream, setStream] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(null);\n    const [isStreaming, setIsStreaming] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(false);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(null);\n    const [devices, setDevices] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)([]);\n    const [selectedVideoDevice, setSelectedVideoDevice] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)('');\n    const [selectedAudioDevice, setSelectedAudioDevice] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)('');\n    const [isSupported, setIsSupported] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(false);\n    const streamRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(null);\n    // Progressive constraint fallback to prevent timeout errors\n    const tryGetUserMedia = async (options)=>{\n        const constraints = [\n            // First attempt: Full constraints with exact device IDs\n            options,\n            // Second attempt: Ideal constraints without exact device IDs\n            {\n                video: options.video ? {\n                    width: {\n                        ideal: 1280\n                    },\n                    height: {\n                        ideal: 720\n                    },\n                    frameRate: {\n                        ideal: 30\n                    }\n                } : false,\n                audio: options.audio ? {\n                    echoCancellation: true,\n                    noiseSuppression: true,\n                    autoGainControl: true\n                } : false\n            },\n            // Third attempt: Basic constraints\n            {\n                video: options.video ? {\n                    width: {\n                        ideal: 640\n                    },\n                    height: {\n                        ideal: 480\n                    }\n                } : false,\n                audio: options.audio ? true : false\n            },\n            // Final attempt: Minimal constraints\n            {\n                video: options.video ? true : false,\n                audio: options.audio ? true : false\n            }\n        ];\n        let lastError = null;\n        for(let i = 0; i < constraints.length; i++){\n            try {\n                console.log(\"Attempting getUserMedia with constraint set \".concat(i + 1, \"/\").concat(constraints.length));\n                const stream = await navigator.mediaDevices.getUserMedia(constraints[i]);\n                if (i > 0) {\n                    console.log(\"Successfully obtained media stream using fallback constraint set \".concat(i + 1));\n                }\n                return stream;\n            } catch (error) {\n                lastError = error;\n                console.warn(\"Constraint set \".concat(i + 1, \" failed:\"), error);\n                // If it's a permission error, don't try fallbacks\n                if (lastError.name === 'NotAllowedError') {\n                    throw lastError;\n                }\n            }\n        }\n        // If all attempts failed, throw the last error\n        throw lastError || new Error('Failed to access media devices');\n    };\n    // Refresh available media devices\n    const refreshDevices = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useMediaStream.useCallback[refreshDevices]\": async ()=>{\n            if (!isSupported) return;\n            try {\n                const deviceList = await navigator.mediaDevices.enumerateDevices();\n                const mediaDevices = deviceList.map({\n                    \"useMediaStream.useCallback[refreshDevices].mediaDevices\": (device)=>({\n                            deviceId: device.deviceId,\n                            label: device.label || \"\".concat(device.kind, \" \").concat(device.deviceId.slice(0, 8)),\n                            kind: device.kind\n                        })\n                }[\"useMediaStream.useCallback[refreshDevices].mediaDevices\"]);\n                setDevices(mediaDevices);\n                // Set default devices if none selected (only on first load)\n                if (!selectedVideoDevice && mediaDevices.length > 0) {\n                    const videoDevice = mediaDevices.find({\n                        \"useMediaStream.useCallback[refreshDevices].videoDevice\": (d)=>d.kind === 'videoinput'\n                    }[\"useMediaStream.useCallback[refreshDevices].videoDevice\"]);\n                    if (videoDevice) {\n                        setSelectedVideoDevice(videoDevice.deviceId);\n                    }\n                }\n                if (!selectedAudioDevice && mediaDevices.length > 0) {\n                    const audioDevice = mediaDevices.find({\n                        \"useMediaStream.useCallback[refreshDevices].audioDevice\": (d)=>d.kind === 'audioinput'\n                    }[\"useMediaStream.useCallback[refreshDevices].audioDevice\"]);\n                    if (audioDevice) {\n                        setSelectedAudioDevice(audioDevice.deviceId);\n                    }\n                }\n            } catch (err) {\n                console.error('Failed to enumerate media devices:', err);\n                setError('Failed to enumerate media devices');\n            }\n        }\n    }[\"useMediaStream.useCallback[refreshDevices]\"], [\n        isSupported\n    ]); // Remove selectedVideoDevice and selectedAudioDevice from dependencies\n    // Check for getUserMedia support\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)({\n        \"useMediaStream.useEffect\": ()=>{\n            if ( true && navigator.mediaDevices && typeof navigator.mediaDevices.getUserMedia === 'function') {\n                setIsSupported(true);\n            } else {\n                setIsSupported(false);\n                setError('Media devices are not supported in this browser');\n            }\n        }\n    }[\"useMediaStream.useEffect\"], []);\n    // Refresh devices when supported status changes\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)({\n        \"useMediaStream.useEffect\": ()=>{\n            if (isSupported) {\n                refreshDevices();\n            }\n        }\n    }[\"useMediaStream.useEffect\"], [\n        isSupported,\n        refreshDevices\n    ]);\n    // Start media stream\n    const startStream = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useMediaStream.useCallback[startStream]\": async function() {\n            let options = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : {};\n            if (!isSupported) {\n                setError('Media devices are not supported');\n                return;\n            }\n            if (isStreaming) {\n                return;\n            }\n            const defaultOptions = {\n                video: {\n                    deviceId: selectedVideoDevice ? {\n                        exact: selectedVideoDevice\n                    } : undefined,\n                    width: {\n                        ideal: 1280\n                    },\n                    height: {\n                        ideal: 720\n                    },\n                    frameRate: {\n                        ideal: 30\n                    }\n                },\n                audio: {\n                    deviceId: selectedAudioDevice ? {\n                        exact: selectedAudioDevice\n                    } : undefined,\n                    echoCancellation: true,\n                    noiseSuppression: true,\n                    autoGainControl: true\n                }\n            };\n            const streamOptions = {\n                ...defaultOptions,\n                ...options\n            };\n            try {\n                setError(null);\n                const mediaStream = await navigator.mediaDevices.getUserMedia(streamOptions);\n                streamRef.current = mediaStream;\n                setStream(mediaStream);\n                setIsStreaming(true);\n                // Refresh devices after getting permission (labels will be available)\n                await refreshDevices();\n            } catch (err) {\n                const error = err;\n                console.error('Failed to access media devices:', err);\n                // Provide more specific error messages\n                let errorMessage = 'Failed to access media devices';\n                if (error.name === 'NotAllowedError') {\n                    errorMessage = 'Camera/microphone access denied. Please allow permissions and try again.';\n                } else if (error.name === 'NotFoundError') {\n                    errorMessage = 'No camera or microphone found. Please connect a device and try again.';\n                } else if (error.name === 'NotReadableError') {\n                    errorMessage = 'Camera/microphone is already in use by another application.';\n                } else if (error.name === 'OverconstrainedError') {\n                    errorMessage = 'Camera/microphone constraints cannot be satisfied. Try different settings.';\n                } else if (error.message) {\n                    errorMessage = \"Media access error: \".concat(error.message);\n                }\n                setError(errorMessage);\n                setIsStreaming(false);\n            }\n        }\n    }[\"useMediaStream.useCallback[startStream]\"], [\n        isSupported,\n        isStreaming,\n        selectedVideoDevice,\n        selectedAudioDevice,\n        refreshDevices\n    ]);\n    // Stop media stream\n    const stopStream = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useMediaStream.useCallback[stopStream]\": ()=>{\n            if (streamRef.current) {\n                streamRef.current.getTracks().forEach({\n                    \"useMediaStream.useCallback[stopStream]\": (track)=>{\n                        track.stop();\n                    }\n                }[\"useMediaStream.useCallback[stopStream]\"]);\n                streamRef.current = null;\n                setStream(null);\n                setIsStreaming(false);\n            }\n        }\n    }[\"useMediaStream.useCallback[stopStream]\"], []);\n    // Switch video device\n    const switchVideoDevice = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useMediaStream.useCallback[switchVideoDevice]\": async (deviceId)=>{\n            setSelectedVideoDevice(deviceId);\n            if (isStreaming) {\n                // Stop current stream and start with new device\n                stopStream();\n                await startStream({\n                    video: {\n                        deviceId: {\n                            exact: deviceId\n                        },\n                        width: {\n                            ideal: 1280\n                        },\n                        height: {\n                            ideal: 720\n                        },\n                        frameRate: {\n                            ideal: 30\n                        }\n                    },\n                    audio: {\n                        deviceId: selectedAudioDevice ? {\n                            exact: selectedAudioDevice\n                        } : undefined,\n                        echoCancellation: true,\n                        noiseSuppression: true,\n                        autoGainControl: true\n                    }\n                });\n            }\n        }\n    }[\"useMediaStream.useCallback[switchVideoDevice]\"], [\n        isStreaming,\n        selectedAudioDevice,\n        stopStream,\n        startStream\n    ]);\n    // Switch audio device\n    const switchAudioDevice = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useMediaStream.useCallback[switchAudioDevice]\": async (deviceId)=>{\n            setSelectedAudioDevice(deviceId);\n            if (isStreaming) {\n                // Stop current stream and start with new device\n                stopStream();\n                await startStream({\n                    video: {\n                        deviceId: selectedVideoDevice ? {\n                            exact: selectedVideoDevice\n                        } : undefined,\n                        width: {\n                            ideal: 1280\n                        },\n                        height: {\n                            ideal: 720\n                        },\n                        frameRate: {\n                            ideal: 30\n                        }\n                    },\n                    audio: {\n                        deviceId: {\n                            exact: deviceId\n                        },\n                        echoCancellation: true,\n                        noiseSuppression: true,\n                        autoGainControl: true\n                    }\n                });\n            }\n        }\n    }[\"useMediaStream.useCallback[switchAudioDevice]\"], [\n        isStreaming,\n        selectedVideoDevice,\n        stopStream,\n        startStream\n    ]);\n    // Cleanup on unmount\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)({\n        \"useMediaStream.useEffect\": ()=>{\n            return ({\n                \"useMediaStream.useEffect\": ()=>{\n                    stopStream();\n                }\n            })[\"useMediaStream.useEffect\"];\n        }\n    }[\"useMediaStream.useEffect\"], [\n        stopStream\n    ]);\n    return {\n        stream,\n        isStreaming,\n        error,\n        devices,\n        selectedVideoDevice,\n        selectedAudioDevice,\n        startStream,\n        stopStream,\n        switchVideoDevice,\n        switchAudioDevice,\n        refreshDevices,\n        isSupported\n    };\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (useMediaStream);\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/hooks/useMediaStream.ts\n"));

/***/ })

});