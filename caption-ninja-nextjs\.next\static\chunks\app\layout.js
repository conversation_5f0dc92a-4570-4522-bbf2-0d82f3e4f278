/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["app/layout"],{

/***/ "(app-pages-browser)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CAliyuSunusi%5C%5COneDrive%20-%20UGSM-Monarch%20Business%20School%20GmbH%5C%5CDocuments%5C%5Ccaptionninja%5C%5Ccaption-ninja-nextjs%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-sans%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistSans%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CAliyuSunusi%5C%5COneDrive%20-%20UGSM-Monarch%20Business%20School%20GmbH%5C%5CDocuments%5C%5Ccaptionninja%5C%5Ccaption-ninja-nextjs%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist_Mono%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-mono%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistMono%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CAliyuSunusi%5C%5COneDrive%20-%20UGSM-Monarch%20Business%20School%20GmbH%5C%5CDocuments%5C%5Ccaptionninja%5C%5Ccaption-ninja-nextjs%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CAliyuSunusi%5C%5COneDrive%20-%20UGSM-Monarch%20Business%20School%20GmbH%5C%5CDocuments%5C%5Ccaptionninja%5C%5Ccaption-ninja-nextjs%5C%5Csrc%5C%5Ccontexts%5C%5CCaptionContext.tsx%22%2C%22ids%22%3A%5B%22CaptionProvider%22%5D%7D&server=false!":
/*!*************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CAliyuSunusi%5C%5COneDrive%20-%20UGSM-Monarch%20Business%20School%20GmbH%5C%5CDocuments%5C%5Ccaptionninja%5C%5Ccaption-ninja-nextjs%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-sans%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistSans%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CAliyuSunusi%5C%5COneDrive%20-%20UGSM-Monarch%20Business%20School%20GmbH%5C%5CDocuments%5C%5Ccaptionninja%5C%5Ccaption-ninja-nextjs%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist_Mono%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-mono%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistMono%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CAliyuSunusi%5C%5COneDrive%20-%20UGSM-Monarch%20Business%20School%20GmbH%5C%5CDocuments%5C%5Ccaptionninja%5C%5Ccaption-ninja-nextjs%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CAliyuSunusi%5C%5COneDrive%20-%20UGSM-Monarch%20Business%20School%20GmbH%5C%5CDocuments%5C%5Ccaptionninja%5C%5Ccaption-ninja-nextjs%5C%5Csrc%5C%5Ccontexts%5C%5CCaptionContext.tsx%22%2C%22ids%22%3A%5B%22CaptionProvider%22%5D%7D&server=false! ***!
  \*************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval(__webpack_require__.ts("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/font/google/target.css?{\"path\":\"src\\\\app\\\\layout.tsx\",\"import\":\"Geist\",\"arguments\":[{\"variable\":\"--font-geist-sans\",\"subsets\":[\"latin\"]}],\"variableName\":\"geistSans\"} */ \"(app-pages-browser)/./node_modules/next/font/google/target.css?{\\\"path\\\":\\\"src\\\\\\\\app\\\\\\\\layout.tsx\\\",\\\"import\\\":\\\"Geist\\\",\\\"arguments\\\":[{\\\"variable\\\":\\\"--font-geist-sans\\\",\\\"subsets\\\":[\\\"latin\\\"]}],\\\"variableName\\\":\\\"geistSans\\\"}\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/font/google/target.css?{\"path\":\"src\\\\app\\\\layout.tsx\",\"import\":\"Geist_Mono\",\"arguments\":[{\"variable\":\"--font-geist-mono\",\"subsets\":[\"latin\"]}],\"variableName\":\"geistMono\"} */ \"(app-pages-browser)/./node_modules/next/font/google/target.css?{\\\"path\\\":\\\"src\\\\\\\\app\\\\\\\\layout.tsx\\\",\\\"import\\\":\\\"Geist_Mono\\\",\\\"arguments\\\":[{\\\"variable\\\":\\\"--font-geist-mono\\\",\\\"subsets\\\":[\\\"latin\\\"]}],\\\"variableName\\\":\\\"geistMono\\\"}\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/globals.css */ \"(app-pages-browser)/./src/app/globals.css\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/contexts/CaptionContext.tsx */ \"(app-pages-browser)/./src/contexts/CaptionContext.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CAliyuSunusi%5C%5COneDrive%20-%20UGSM-Monarch%20Business%20School%20GmbH%5C%5CDocuments%5C%5Ccaptionninja%5C%5Ccaption-ninja-nextjs%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-sans%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistSans%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CAliyuSunusi%5C%5COneDrive%20-%20UGSM-Monarch%20Business%20School%20GmbH%5C%5CDocuments%5C%5Ccaptionninja%5C%5Ccaption-ninja-nextjs%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist_Mono%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-mono%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistMono%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CAliyuSunusi%5C%5COneDrive%20-%20UGSM-Monarch%20Business%20School%20GmbH%5C%5CDocuments%5C%5Ccaptionninja%5C%5Ccaption-ninja-nextjs%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CAliyuSunusi%5C%5COneDrive%20-%20UGSM-Monarch%20Business%20School%20GmbH%5C%5CDocuments%5C%5Ccaptionninja%5C%5Ccaption-ninja-nextjs%5C%5Csrc%5C%5Ccontexts%5C%5CCaptionContext.tsx%22%2C%22ids%22%3A%5B%22CaptionProvider%22%5D%7D&server=false!\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/next/dist/compiled/mini-css-extract-plugin/hmr/hotModuleReplacement.js":
/*!*********************************************************************************************!*\
  !*** ./node_modules/next/dist/compiled/mini-css-extract-plugin/hmr/hotModuleReplacement.js ***!
  \*********************************************************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval(__webpack_require__.ts("var __dirname = \"/\";\n(()=>{\"use strict\";var e={432:(e,r,t)=>{var n=t(887);var i=Object.create(null);var a=typeof document===\"undefined\";var o=Array.prototype.forEach;function debounce(e,r){var t=0;return function(){var n=this;var i=arguments;var a=function functionCall(){return e.apply(n,i)};clearTimeout(t);t=setTimeout(a,r)}}function noop(){}function getCurrentScriptUrl(e){var r=i[e];if(!r){if(document.currentScript){r=document.currentScript.src}else{var t=document.getElementsByTagName(\"script\");var a=t[t.length-1];if(a){r=a.src}}i[e]=r}return function(e){if(!r){return null}var t=r.split(/([^\\\\/]+)\\.js$/);var i=t&&t[1];if(!i){return[r.replace(\".js\",\".css\")]}if(!e){return[r.replace(\".js\",\".css\")]}return e.split(\",\").map((function(e){var t=new RegExp(\"\".concat(i,\"\\\\.js$\"),\"g\");return n(r.replace(t,\"\".concat(e.replace(/{fileName}/g,i),\".css\")))}))}}function updateCss(e,r){if(!r){if(!e.href){return}r=e.href.split(\"?\")[0]}if(!isUrlRequest(r)){return}if(e.isLoaded===false){return}if(!r||!(r.indexOf(\".css\")>-1)){return}e.visited=true;var t=e.cloneNode();t.isLoaded=false;t.addEventListener(\"load\",(function(){if(t.isLoaded){return}t.isLoaded=true;e.parentNode.removeChild(e)}));t.addEventListener(\"error\",(function(){if(t.isLoaded){return}t.isLoaded=true;e.parentNode.removeChild(e)}));t.href=\"\".concat(r,\"?\").concat(Date.now());if(e.nextSibling){e.parentNode.insertBefore(t,e.nextSibling)}else{e.parentNode.appendChild(t)}}function getReloadUrl(e,r){var t;e=n(e,{stripWWW:false});r.some((function(n){if(e.indexOf(r)>-1){t=n}}));return t}function reloadStyle(e){if(!e){return false}var r=document.querySelectorAll(\"link\");var t=false;o.call(r,(function(r){if(!r.href){return}var n=getReloadUrl(r.href,e);if(!isUrlRequest(n)){return}if(r.visited===true){return}if(n){updateCss(r,n);t=true}}));return t}function reloadAll(){var e=document.querySelectorAll(\"link\");o.call(e,(function(e){if(e.visited===true){return}updateCss(e)}))}function isUrlRequest(e){if(!/^[a-zA-Z][a-zA-Z\\d+\\-.]*:/.test(e)){return false}return true}e.exports=function(e,r){if(a){console.log(\"no window.document found, will not HMR CSS\");return noop}var t=getCurrentScriptUrl(e);function update(){var e=t(r.filename);var n=reloadStyle(e);if(r.locals){console.log(\"[HMR] Detected local css modules. Reload all css\");reloadAll();return}if(n){console.log(\"[HMR] css reload %s\",e.join(\" \"))}else{console.log(\"[HMR] Reload all css\");reloadAll()}}return debounce(update,50)}},887:e=>{function normalizeUrl(e){return e.reduce((function(e,r){switch(r){case\"..\":e.pop();break;case\".\":break;default:e.push(r)}return e}),[]).join(\"/\")}e.exports=function(e){e=e.trim();if(/^data:/i.test(e)){return e}var r=e.indexOf(\"//\")!==-1?e.split(\"//\")[0]+\"//\":\"\";var t=e.replace(new RegExp(r,\"i\"),\"\").split(\"/\");var n=t[0].toLowerCase().replace(/\\.$/,\"\");t[0]=\"\";var i=normalizeUrl(t);return r+n+i}}};var r={};function __nccwpck_require__(t){var n=r[t];if(n!==undefined){return n.exports}var i=r[t]={exports:{}};var a=true;try{e[t](i,i.exports,__nccwpck_require__);a=false}finally{if(a)delete r[t]}return i.exports}if(typeof __nccwpck_require__!==\"undefined\")__nccwpck_require__.ab=__dirname+\"/\";var t=__nccwpck_require__(432);module.exports=t})();//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/dist/compiled/mini-css-extract-plugin/hmr/hotModuleReplacement.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/next/dist/compiled/react/cjs/react-jsx-dev-runtime.development.js":
/*!****************************************************************************************!*\
  !*** ./node_modules/next/dist/compiled/react/cjs/react-jsx-dev-runtime.development.js ***!
  \****************************************************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("/**\n * @license React\n * react-jsx-dev-runtime.development.js\n *\n * Copyright (c) Meta Platforms, Inc. and affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n */\n\n\n true &&\n  (function () {\n    function getComponentNameFromType(type) {\n      if (null == type) return null;\n      if (\"function\" === typeof type)\n        return type.$$typeof === REACT_CLIENT_REFERENCE\n          ? null\n          : type.displayName || type.name || null;\n      if (\"string\" === typeof type) return type;\n      switch (type) {\n        case REACT_FRAGMENT_TYPE:\n          return \"Fragment\";\n        case REACT_PROFILER_TYPE:\n          return \"Profiler\";\n        case REACT_STRICT_MODE_TYPE:\n          return \"StrictMode\";\n        case REACT_SUSPENSE_TYPE:\n          return \"Suspense\";\n        case REACT_SUSPENSE_LIST_TYPE:\n          return \"SuspenseList\";\n        case REACT_ACTIVITY_TYPE:\n          return \"Activity\";\n      }\n      if (\"object\" === typeof type)\n        switch (\n          (\"number\" === typeof type.tag &&\n            console.error(\n              \"Received an unexpected object in getComponentNameFromType(). This is likely a bug in React. Please file an issue.\"\n            ),\n          type.$$typeof)\n        ) {\n          case REACT_PORTAL_TYPE:\n            return \"Portal\";\n          case REACT_CONTEXT_TYPE:\n            return type.displayName || \"Context\";\n          case REACT_CONSUMER_TYPE:\n            return (type._context.displayName || \"Context\") + \".Consumer\";\n          case REACT_FORWARD_REF_TYPE:\n            var innerType = type.render;\n            type = type.displayName;\n            type ||\n              ((type = innerType.displayName || innerType.name || \"\"),\n              (type = \"\" !== type ? \"ForwardRef(\" + type + \")\" : \"ForwardRef\"));\n            return type;\n          case REACT_MEMO_TYPE:\n            return (\n              (innerType = type.displayName || null),\n              null !== innerType\n                ? innerType\n                : getComponentNameFromType(type.type) || \"Memo\"\n            );\n          case REACT_LAZY_TYPE:\n            innerType = type._payload;\n            type = type._init;\n            try {\n              return getComponentNameFromType(type(innerType));\n            } catch (x) {}\n        }\n      return null;\n    }\n    function testStringCoercion(value) {\n      return \"\" + value;\n    }\n    function checkKeyStringCoercion(value) {\n      try {\n        testStringCoercion(value);\n        var JSCompiler_inline_result = !1;\n      } catch (e) {\n        JSCompiler_inline_result = !0;\n      }\n      if (JSCompiler_inline_result) {\n        JSCompiler_inline_result = console;\n        var JSCompiler_temp_const = JSCompiler_inline_result.error;\n        var JSCompiler_inline_result$jscomp$0 =\n          (\"function\" === typeof Symbol &&\n            Symbol.toStringTag &&\n            value[Symbol.toStringTag]) ||\n          value.constructor.name ||\n          \"Object\";\n        JSCompiler_temp_const.call(\n          JSCompiler_inline_result,\n          \"The provided key is an unsupported type %s. This value must be coerced to a string before using it here.\",\n          JSCompiler_inline_result$jscomp$0\n        );\n        return testStringCoercion(value);\n      }\n    }\n    function getTaskName(type) {\n      if (type === REACT_FRAGMENT_TYPE) return \"<>\";\n      if (\n        \"object\" === typeof type &&\n        null !== type &&\n        type.$$typeof === REACT_LAZY_TYPE\n      )\n        return \"<...>\";\n      try {\n        var name = getComponentNameFromType(type);\n        return name ? \"<\" + name + \">\" : \"<...>\";\n      } catch (x) {\n        return \"<...>\";\n      }\n    }\n    function getOwner() {\n      var dispatcher = ReactSharedInternals.A;\n      return null === dispatcher ? null : dispatcher.getOwner();\n    }\n    function UnknownOwner() {\n      return Error(\"react-stack-top-frame\");\n    }\n    function hasValidKey(config) {\n      if (hasOwnProperty.call(config, \"key\")) {\n        var getter = Object.getOwnPropertyDescriptor(config, \"key\").get;\n        if (getter && getter.isReactWarning) return !1;\n      }\n      return void 0 !== config.key;\n    }\n    function defineKeyPropWarningGetter(props, displayName) {\n      function warnAboutAccessingKey() {\n        specialPropKeyWarningShown ||\n          ((specialPropKeyWarningShown = !0),\n          console.error(\n            \"%s: `key` is not a prop. Trying to access it will result in `undefined` being returned. If you need to access the same value within the child component, you should pass it as a different prop. (https://react.dev/link/special-props)\",\n            displayName\n          ));\n      }\n      warnAboutAccessingKey.isReactWarning = !0;\n      Object.defineProperty(props, \"key\", {\n        get: warnAboutAccessingKey,\n        configurable: !0\n      });\n    }\n    function elementRefGetterWithDeprecationWarning() {\n      var componentName = getComponentNameFromType(this.type);\n      didWarnAboutElementRef[componentName] ||\n        ((didWarnAboutElementRef[componentName] = !0),\n        console.error(\n          \"Accessing element.ref was removed in React 19. ref is now a regular prop. It will be removed from the JSX Element type in a future release.\"\n        ));\n      componentName = this.props.ref;\n      return void 0 !== componentName ? componentName : null;\n    }\n    function ReactElement(type, key, props, owner, debugStack, debugTask) {\n      var refProp = props.ref;\n      type = {\n        $$typeof: REACT_ELEMENT_TYPE,\n        type: type,\n        key: key,\n        props: props,\n        _owner: owner\n      };\n      null !== (void 0 !== refProp ? refProp : null)\n        ? Object.defineProperty(type, \"ref\", {\n            enumerable: !1,\n            get: elementRefGetterWithDeprecationWarning\n          })\n        : Object.defineProperty(type, \"ref\", { enumerable: !1, value: null });\n      type._store = {};\n      Object.defineProperty(type._store, \"validated\", {\n        configurable: !1,\n        enumerable: !1,\n        writable: !0,\n        value: 0\n      });\n      Object.defineProperty(type, \"_debugInfo\", {\n        configurable: !1,\n        enumerable: !1,\n        writable: !0,\n        value: null\n      });\n      Object.defineProperty(type, \"_debugStack\", {\n        configurable: !1,\n        enumerable: !1,\n        writable: !0,\n        value: debugStack\n      });\n      Object.defineProperty(type, \"_debugTask\", {\n        configurable: !1,\n        enumerable: !1,\n        writable: !0,\n        value: debugTask\n      });\n      Object.freeze && (Object.freeze(type.props), Object.freeze(type));\n      return type;\n    }\n    function jsxDEVImpl(\n      type,\n      config,\n      maybeKey,\n      isStaticChildren,\n      debugStack,\n      debugTask\n    ) {\n      var children = config.children;\n      if (void 0 !== children)\n        if (isStaticChildren)\n          if (isArrayImpl(children)) {\n            for (\n              isStaticChildren = 0;\n              isStaticChildren < children.length;\n              isStaticChildren++\n            )\n              validateChildKeys(children[isStaticChildren]);\n            Object.freeze && Object.freeze(children);\n          } else\n            console.error(\n              \"React.jsx: Static children should always be an array. You are likely explicitly calling React.jsxs or React.jsxDEV. Use the Babel transform instead.\"\n            );\n        else validateChildKeys(children);\n      if (hasOwnProperty.call(config, \"key\")) {\n        children = getComponentNameFromType(type);\n        var keys = Object.keys(config).filter(function (k) {\n          return \"key\" !== k;\n        });\n        isStaticChildren =\n          0 < keys.length\n            ? \"{key: someKey, \" + keys.join(\": ..., \") + \": ...}\"\n            : \"{key: someKey}\";\n        didWarnAboutKeySpread[children + isStaticChildren] ||\n          ((keys =\n            0 < keys.length ? \"{\" + keys.join(\": ..., \") + \": ...}\" : \"{}\"),\n          console.error(\n            'A props object containing a \"key\" prop is being spread into JSX:\\n  let props = %s;\\n  <%s {...props} />\\nReact keys must be passed directly to JSX without using spread:\\n  let props = %s;\\n  <%s key={someKey} {...props} />',\n            isStaticChildren,\n            children,\n            keys,\n            children\n          ),\n          (didWarnAboutKeySpread[children + isStaticChildren] = !0));\n      }\n      children = null;\n      void 0 !== maybeKey &&\n        (checkKeyStringCoercion(maybeKey), (children = \"\" + maybeKey));\n      hasValidKey(config) &&\n        (checkKeyStringCoercion(config.key), (children = \"\" + config.key));\n      if (\"key\" in config) {\n        maybeKey = {};\n        for (var propName in config)\n          \"key\" !== propName && (maybeKey[propName] = config[propName]);\n      } else maybeKey = config;\n      children &&\n        defineKeyPropWarningGetter(\n          maybeKey,\n          \"function\" === typeof type\n            ? type.displayName || type.name || \"Unknown\"\n            : type\n        );\n      return ReactElement(\n        type,\n        children,\n        maybeKey,\n        getOwner(),\n        debugStack,\n        debugTask\n      );\n    }\n    function validateChildKeys(node) {\n      \"object\" === typeof node &&\n        null !== node &&\n        node.$$typeof === REACT_ELEMENT_TYPE &&\n        node._store &&\n        (node._store.validated = 1);\n    }\n    var React = __webpack_require__(/*! next/dist/compiled/react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\"),\n      REACT_ELEMENT_TYPE = Symbol.for(\"react.transitional.element\"),\n      REACT_PORTAL_TYPE = Symbol.for(\"react.portal\"),\n      REACT_FRAGMENT_TYPE = Symbol.for(\"react.fragment\"),\n      REACT_STRICT_MODE_TYPE = Symbol.for(\"react.strict_mode\"),\n      REACT_PROFILER_TYPE = Symbol.for(\"react.profiler\"),\n      REACT_CONSUMER_TYPE = Symbol.for(\"react.consumer\"),\n      REACT_CONTEXT_TYPE = Symbol.for(\"react.context\"),\n      REACT_FORWARD_REF_TYPE = Symbol.for(\"react.forward_ref\"),\n      REACT_SUSPENSE_TYPE = Symbol.for(\"react.suspense\"),\n      REACT_SUSPENSE_LIST_TYPE = Symbol.for(\"react.suspense_list\"),\n      REACT_MEMO_TYPE = Symbol.for(\"react.memo\"),\n      REACT_LAZY_TYPE = Symbol.for(\"react.lazy\"),\n      REACT_ACTIVITY_TYPE = Symbol.for(\"react.activity\"),\n      REACT_CLIENT_REFERENCE = Symbol.for(\"react.client.reference\"),\n      ReactSharedInternals =\n        React.__CLIENT_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE,\n      hasOwnProperty = Object.prototype.hasOwnProperty,\n      isArrayImpl = Array.isArray,\n      createTask = console.createTask\n        ? console.createTask\n        : function () {\n            return null;\n          };\n    React = {\n      react_stack_bottom_frame: function (callStackForError) {\n        return callStackForError();\n      }\n    };\n    var specialPropKeyWarningShown;\n    var didWarnAboutElementRef = {};\n    var unknownOwnerDebugStack = React.react_stack_bottom_frame.bind(\n      React,\n      UnknownOwner\n    )();\n    var unknownOwnerDebugTask = createTask(getTaskName(UnknownOwner));\n    var didWarnAboutKeySpread = {};\n    exports.Fragment = REACT_FRAGMENT_TYPE;\n    exports.jsxDEV = function (type, config, maybeKey, isStaticChildren) {\n      var trackActualOwner =\n        1e4 > ReactSharedInternals.recentlyCreatedOwnerStacks++;\n      return jsxDEVImpl(\n        type,\n        config,\n        maybeKey,\n        isStaticChildren,\n        trackActualOwner\n          ? Error(\"react-stack-top-frame\")\n          : unknownOwnerDebugStack,\n        trackActualOwner ? createTask(getTaskName(type)) : unknownOwnerDebugTask\n      );\n    };\n  })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/dist/compiled/react/cjs/react-jsx-dev-runtime.development.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js":
/*!******************************************************************!*\
  !*** ./node_modules/next/dist/compiled/react/jsx-dev-runtime.js ***!
  \******************************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("\n\nif (false) {} else {\n  module.exports = __webpack_require__(/*! ./cjs/react-jsx-dev-runtime.development.js */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/cjs/react-jsx-dev-runtime.development.js\");\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9uZXh0L2Rpc3QvY29tcGlsZWQvcmVhY3QvanN4LWRldi1ydW50aW1lLmpzIiwibWFwcGluZ3MiOiJBQUFhOztBQUViLElBQUksS0FBcUMsRUFBRSxFQUUxQyxDQUFDO0FBQ0YsRUFBRSw4TEFBc0U7QUFDeEUiLCJzb3VyY2VzIjpbIkM6XFxVc2Vyc1xcQWxpeXVTdW51c2lcXE9uZURyaXZlIC0gVUdTTS1Nb25hcmNoIEJ1c2luZXNzIFNjaG9vbCBHbWJIXFxEb2N1bWVudHNcXGNhcHRpb25uaW5qYVxcY2FwdGlvbi1uaW5qYS1uZXh0anNcXG5vZGVfbW9kdWxlc1xcbmV4dFxcZGlzdFxcY29tcGlsZWRcXHJlYWN0XFxqc3gtZGV2LXJ1bnRpbWUuanMiXSwic291cmNlc0NvbnRlbnQiOlsiJ3VzZSBzdHJpY3QnO1xuXG5pZiAocHJvY2Vzcy5lbnYuTk9ERV9FTlYgPT09ICdwcm9kdWN0aW9uJykge1xuICBtb2R1bGUuZXhwb3J0cyA9IHJlcXVpcmUoJy4vY2pzL3JlYWN0LWpzeC1kZXYtcnVudGltZS5wcm9kdWN0aW9uLmpzJyk7XG59IGVsc2Uge1xuICBtb2R1bGUuZXhwb3J0cyA9IHJlcXVpcmUoJy4vY2pzL3JlYWN0LWpzeC1kZXYtcnVudGltZS5kZXZlbG9wbWVudC5qcycpO1xufVxuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/next/font/google/target.css?{\"path\":\"src\\\\app\\\\layout.tsx\",\"import\":\"Geist\",\"arguments\":[{\"variable\":\"--font-geist-sans\",\"subsets\":[\"latin\"]}],\"variableName\":\"geistSans\"}":
/*!*************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/font/google/target.css?{"path":"src\\app\\layout.tsx","import":"Geist","arguments":[{"variable":"--font-geist-sans","subsets":["latin"]}],"variableName":"geistSans"} ***!
  \*************************************************************************************************************************************************************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval(__webpack_require__.ts("// extracted by mini-css-extract-plugin\nmodule.exports = {\"style\":{\"fontFamily\":\"'Geist', 'Geist Fallback'\",\"fontStyle\":\"normal\"},\"className\":\"__className_5cfdac\",\"variable\":\"__variable_5cfdac\"};\n    if(true) {\n      // 1757166816125\n      var cssReload = __webpack_require__(/*! ./node_modules/next/dist/compiled/mini-css-extract-plugin/hmr/hotModuleReplacement.js */ \"(app-pages-browser)/./node_modules/next/dist/compiled/mini-css-extract-plugin/hmr/hotModuleReplacement.js\")(module.id, {\"publicPath\":\"/_next/\",\"esModule\":false,\"locals\":true});\n      module.hot.dispose(cssReload);\n      \n    }\n  //# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9uZXh0L2ZvbnQvZ29vZ2xlL3RhcmdldC5jc3M/e1wicGF0aFwiOlwic3JjXFxcXGFwcFxcXFxsYXlvdXQudHN4XCIsXCJpbXBvcnRcIjpcIkdlaXN0XCIsXCJhcmd1bWVudHNcIjpbe1widmFyaWFibGVcIjpcIi0tZm9udC1nZWlzdC1zYW5zXCIsXCJzdWJzZXRzXCI6W1wibGF0aW5cIl19XSxcInZhcmlhYmxlTmFtZVwiOlwiZ2Vpc3RTYW5zXCJ9IiwibWFwcGluZ3MiOiJBQUFBO0FBQ0Esa0JBQWtCLFNBQVMsOERBQThEO0FBQ3pGLE9BQU8sSUFBVTtBQUNqQjtBQUNBLHNCQUFzQixtQkFBTyxDQUFDLHdNQUFtTSxjQUFjLHNEQUFzRDtBQUNyUyxNQUFNLFVBQVU7QUFDaEI7QUFDQTtBQUNBIiwic291cmNlcyI6WyJDOlxcVXNlcnNcXEFsaXl1U3VudXNpXFxPbmVEcml2ZSAtIFVHU00tTW9uYXJjaCBCdXNpbmVzcyBTY2hvb2wgR21iSFxcRG9jdW1lbnRzXFxjYXB0aW9ubmluamFcXGNhcHRpb24tbmluamEtbmV4dGpzXFxub2RlX21vZHVsZXNcXG5leHRcXGZvbnRcXGdvb2dsZVxcdGFyZ2V0LmNzcz97XCJwYXRoXCI6XCJzcmNcXGFwcFxcbGF5b3V0LnRzeFwiLFwiaW1wb3J0XCI6XCJHZWlzdFwiLFwiYXJndW1lbnRzXCI6W3tcInZhcmlhYmxlXCI6XCItLWZvbnQtZ2Vpc3Qtc2Fuc1wiLFwic3Vic2V0c1wiOltcImxhdGluXCJdfV0sXCJ2YXJpYWJsZU5hbWVcIjpcImdlaXN0U2Fuc1wifXxhcHAtcGFnZXMtYnJvd3NlciJdLCJzb3VyY2VzQ29udGVudCI6WyIvLyBleHRyYWN0ZWQgYnkgbWluaS1jc3MtZXh0cmFjdC1wbHVnaW5cbm1vZHVsZS5leHBvcnRzID0ge1wic3R5bGVcIjp7XCJmb250RmFtaWx5XCI6XCInR2Vpc3QnLCAnR2Vpc3QgRmFsbGJhY2snXCIsXCJmb250U3R5bGVcIjpcIm5vcm1hbFwifSxcImNsYXNzTmFtZVwiOlwiX19jbGFzc05hbWVfNWNmZGFjXCIsXCJ2YXJpYWJsZVwiOlwiX192YXJpYWJsZV81Y2ZkYWNcIn07XG4gICAgaWYobW9kdWxlLmhvdCkge1xuICAgICAgLy8gMTc1NzE2NjgxNjEyNVxuICAgICAgdmFyIGNzc1JlbG9hZCA9IHJlcXVpcmUoXCJDOi9Vc2Vycy9BbGl5dVN1bnVzaS9PbmVEcml2ZSAtIFVHU00tTW9uYXJjaCBCdXNpbmVzcyBTY2hvb2wgR21iSC9Eb2N1bWVudHMvY2FwdGlvbm5pbmphL2NhcHRpb24tbmluamEtbmV4dGpzL25vZGVfbW9kdWxlcy9uZXh0L2Rpc3QvY29tcGlsZWQvbWluaS1jc3MtZXh0cmFjdC1wbHVnaW4vaG1yL2hvdE1vZHVsZVJlcGxhY2VtZW50LmpzXCIpKG1vZHVsZS5pZCwge1wicHVibGljUGF0aFwiOlwiL19uZXh0L1wiLFwiZXNNb2R1bGVcIjpmYWxzZSxcImxvY2Fsc1wiOnRydWV9KTtcbiAgICAgIG1vZHVsZS5ob3QuZGlzcG9zZShjc3NSZWxvYWQpO1xuICAgICAgXG4gICAgfVxuICAiXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/font/google/target.css?{\"path\":\"src\\\\app\\\\layout.tsx\",\"import\":\"Geist\",\"arguments\":[{\"variable\":\"--font-geist-sans\",\"subsets\":[\"latin\"]}],\"variableName\":\"geistSans\"}\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/next/font/google/target.css?{\"path\":\"src\\\\app\\\\layout.tsx\",\"import\":\"Geist_Mono\",\"arguments\":[{\"variable\":\"--font-geist-mono\",\"subsets\":[\"latin\"]}],\"variableName\":\"geistMono\"}":
/*!******************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/font/google/target.css?{"path":"src\\app\\layout.tsx","import":"Geist_Mono","arguments":[{"variable":"--font-geist-mono","subsets":["latin"]}],"variableName":"geistMono"} ***!
  \******************************************************************************************************************************************************************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval(__webpack_require__.ts("// extracted by mini-css-extract-plugin\nmodule.exports = {\"style\":{\"fontFamily\":\"'Geist Mono', 'Geist Mono Fallback'\",\"fontStyle\":\"normal\"},\"className\":\"__className_9a8899\",\"variable\":\"__variable_9a8899\"};\n    if(true) {\n      // 1757166816129\n      var cssReload = __webpack_require__(/*! ./node_modules/next/dist/compiled/mini-css-extract-plugin/hmr/hotModuleReplacement.js */ \"(app-pages-browser)/./node_modules/next/dist/compiled/mini-css-extract-plugin/hmr/hotModuleReplacement.js\")(module.id, {\"publicPath\":\"/_next/\",\"esModule\":false,\"locals\":true});\n      module.hot.dispose(cssReload);\n      \n    }\n  //# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9uZXh0L2ZvbnQvZ29vZ2xlL3RhcmdldC5jc3M/e1wicGF0aFwiOlwic3JjXFxcXGFwcFxcXFxsYXlvdXQudHN4XCIsXCJpbXBvcnRcIjpcIkdlaXN0X01vbm9cIixcImFyZ3VtZW50c1wiOlt7XCJ2YXJpYWJsZVwiOlwiLS1mb250LWdlaXN0LW1vbm9cIixcInN1YnNldHNcIjpbXCJsYXRpblwiXX1dLFwidmFyaWFibGVOYW1lXCI6XCJnZWlzdE1vbm9cIn0iLCJtYXBwaW5ncyI6IkFBQUE7QUFDQSxrQkFBa0IsU0FBUyx3RUFBd0U7QUFDbkcsT0FBTyxJQUFVO0FBQ2pCO0FBQ0Esc0JBQXNCLG1CQUFPLENBQUMsd01BQW1NLGNBQWMsc0RBQXNEO0FBQ3JTLE1BQU0sVUFBVTtBQUNoQjtBQUNBO0FBQ0EiLCJzb3VyY2VzIjpbIkM6XFxVc2Vyc1xcQWxpeXVTdW51c2lcXE9uZURyaXZlIC0gVUdTTS1Nb25hcmNoIEJ1c2luZXNzIFNjaG9vbCBHbWJIXFxEb2N1bWVudHNcXGNhcHRpb25uaW5qYVxcY2FwdGlvbi1uaW5qYS1uZXh0anNcXG5vZGVfbW9kdWxlc1xcbmV4dFxcZm9udFxcZ29vZ2xlXFx0YXJnZXQuY3NzP3tcInBhdGhcIjpcInNyY1xcYXBwXFxsYXlvdXQudHN4XCIsXCJpbXBvcnRcIjpcIkdlaXN0X01vbm9cIixcImFyZ3VtZW50c1wiOlt7XCJ2YXJpYWJsZVwiOlwiLS1mb250LWdlaXN0LW1vbm9cIixcInN1YnNldHNcIjpbXCJsYXRpblwiXX1dLFwidmFyaWFibGVOYW1lXCI6XCJnZWlzdE1vbm9cIn18YXBwLXBhZ2VzLWJyb3dzZXIiXSwic291cmNlc0NvbnRlbnQiOlsiLy8gZXh0cmFjdGVkIGJ5IG1pbmktY3NzLWV4dHJhY3QtcGx1Z2luXG5tb2R1bGUuZXhwb3J0cyA9IHtcInN0eWxlXCI6e1wiZm9udEZhbWlseVwiOlwiJ0dlaXN0IE1vbm8nLCAnR2Vpc3QgTW9ubyBGYWxsYmFjaydcIixcImZvbnRTdHlsZVwiOlwibm9ybWFsXCJ9LFwiY2xhc3NOYW1lXCI6XCJfX2NsYXNzTmFtZV85YTg4OTlcIixcInZhcmlhYmxlXCI6XCJfX3ZhcmlhYmxlXzlhODg5OVwifTtcbiAgICBpZihtb2R1bGUuaG90KSB7XG4gICAgICAvLyAxNzU3MTY2ODE2MTI5XG4gICAgICB2YXIgY3NzUmVsb2FkID0gcmVxdWlyZShcIkM6L1VzZXJzL0FsaXl1U3VudXNpL09uZURyaXZlIC0gVUdTTS1Nb25hcmNoIEJ1c2luZXNzIFNjaG9vbCBHbWJIL0RvY3VtZW50cy9jYXB0aW9ubmluamEvY2FwdGlvbi1uaW5qYS1uZXh0anMvbm9kZV9tb2R1bGVzL25leHQvZGlzdC9jb21waWxlZC9taW5pLWNzcy1leHRyYWN0LXBsdWdpbi9obXIvaG90TW9kdWxlUmVwbGFjZW1lbnQuanNcIikobW9kdWxlLmlkLCB7XCJwdWJsaWNQYXRoXCI6XCIvX25leHQvXCIsXCJlc01vZHVsZVwiOmZhbHNlLFwibG9jYWxzXCI6dHJ1ZX0pO1xuICAgICAgbW9kdWxlLmhvdC5kaXNwb3NlKGNzc1JlbG9hZCk7XG4gICAgICBcbiAgICB9XG4gICJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/font/google/target.css?{\"path\":\"src\\\\app\\\\layout.tsx\",\"import\":\"Geist_Mono\",\"arguments\":[{\"variable\":\"--font-geist-mono\",\"subsets\":[\"latin\"]}],\"variableName\":\"geistMono\"}\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/app/globals.css":
/*!*****************************!*\
  !*** ./src/app/globals.css ***!
  \*****************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"5f2b89ce40fe\");\nif (true) { module.hot.accept() }\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL3NyYy9hcHAvZ2xvYmFscy5jc3MiLCJtYXBwaW5ncyI6Ijs7OztBQUFBLGlFQUFlLGNBQWM7QUFDN0IsSUFBSSxJQUFVLElBQUksaUJBQWlCIiwic291cmNlcyI6WyJDOlxcVXNlcnNcXEFsaXl1U3VudXNpXFxPbmVEcml2ZSAtIFVHU00tTW9uYXJjaCBCdXNpbmVzcyBTY2hvb2wgR21iSFxcRG9jdW1lbnRzXFxjYXB0aW9ubmluamFcXGNhcHRpb24tbmluamEtbmV4dGpzXFxzcmNcXGFwcFxcZ2xvYmFscy5jc3MiXSwic291cmNlc0NvbnRlbnQiOlsiZXhwb3J0IGRlZmF1bHQgXCI1ZjJiODljZTQwZmVcIlxuaWYgKG1vZHVsZS5ob3QpIHsgbW9kdWxlLmhvdC5hY2NlcHQoKSB9XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/globals.css\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/contexts/CaptionContext.tsx":
/*!*****************************************!*\
  !*** ./src/contexts/CaptionContext.tsx ***!
  \*****************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   CaptionProvider: () => (/* binding */ CaptionProvider),\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__),\n/* harmony export */   useCaption: () => (/* binding */ useCaption),\n/* harmony export */   useCaptionActions: () => (/* binding */ useCaptionActions),\n/* harmony export */   useCaptionSelectors: () => (/* binding */ useCaptionSelectors)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/**\n * CaptionContext\n *\n * Global context for managing caption-related state across the application.\n * Provides centralized state management for transcripts, translations, and settings.\n */ /* __next_internal_client_entry_do_not_use__ CaptionProvider,useCaption,useCaptionActions,useCaptionSelectors,default auto */ \nvar _s = $RefreshSig$(), _s1 = $RefreshSig$(), _s2 = $RefreshSig$(), _s3 = $RefreshSig$();\n\n// Initial state\nconst initialSettings = {\n    speechLanguage: 'en-US',\n    translationEnabled: false,\n    sourceLanguage: 'auto',\n    targetLanguage: 'en',\n    ttsEnabled: false,\n    ttsAutoPlay: false,\n    selectedVoice: '',\n    ttsRate: 1,\n    ttsPitch: 1,\n    ttsVolume: 1\n};\nconst initialState = {\n    finalTranscript: '',\n    interimTranscript: '',\n    translatedText: '',\n    transcriptHistory: [],\n    isRecording: false,\n    isTranslating: false,\n    isTTSPlaying: false,\n    settings: initialSettings,\n    speechError: null,\n    translationError: null,\n    ttsError: null,\n    sessionId: 'session-initial',\n    lastUpdate: 0\n};\n// Reducer\nconst captionReducer = (state, action)=>{\n    switch(action.type){\n        case 'SET_FINAL_TRANSCRIPT':\n            return {\n                ...state,\n                finalTranscript: action.payload,\n                lastUpdate: Date.now()\n            };\n        case 'SET_INTERIM_TRANSCRIPT':\n            return {\n                ...state,\n                interimTranscript: action.payload,\n                lastUpdate: Date.now()\n            };\n        case 'SET_TRANSLATED_TEXT':\n            return {\n                ...state,\n                translatedText: action.payload,\n                lastUpdate: Date.now()\n            };\n        case 'ADD_TRANSCRIPT_ENTRY':\n            return {\n                ...state,\n                transcriptHistory: [\n                    ...state.transcriptHistory,\n                    action.payload\n                ],\n                lastUpdate: Date.now()\n            };\n        case 'UPDATE_TRANSCRIPT_ENTRY':\n            return {\n                ...state,\n                transcriptHistory: state.transcriptHistory.map((entry)=>entry.id === action.payload.id ? {\n                        ...entry,\n                        ...action.payload.updates\n                    } : entry),\n                lastUpdate: Date.now()\n            };\n        case 'DELETE_TRANSCRIPT_ENTRY':\n            return {\n                ...state,\n                transcriptHistory: state.transcriptHistory.filter((entry)=>entry.id !== action.payload),\n                lastUpdate: Date.now()\n            };\n        case 'CLEAR_TRANSCRIPT_HISTORY':\n            return {\n                ...state,\n                transcriptHistory: [],\n                lastUpdate: Date.now()\n            };\n        case 'SET_RECORDING_STATUS':\n            return {\n                ...state,\n                isRecording: action.payload,\n                lastUpdate: Date.now()\n            };\n        case 'SET_TRANSLATING_STATUS':\n            return {\n                ...state,\n                isTranslating: action.payload,\n                lastUpdate: Date.now()\n            };\n        case 'SET_TTS_PLAYING_STATUS':\n            return {\n                ...state,\n                isTTSPlaying: action.payload,\n                lastUpdate: Date.now()\n            };\n        case 'UPDATE_SETTINGS':\n            return {\n                ...state,\n                settings: {\n                    ...state.settings,\n                    ...action.payload\n                },\n                lastUpdate: Date.now()\n            };\n        case 'SET_SPEECH_ERROR':\n            return {\n                ...state,\n                speechError: action.payload,\n                lastUpdate: Date.now()\n            };\n        case 'SET_TRANSLATION_ERROR':\n            return {\n                ...state,\n                translationError: action.payload,\n                lastUpdate: Date.now()\n            };\n        case 'SET_TTS_ERROR':\n            return {\n                ...state,\n                ttsError: action.payload,\n                lastUpdate: Date.now()\n            };\n        case 'RESET_TRANSCRIPT':\n            return {\n                ...state,\n                finalTranscript: '',\n                interimTranscript: '',\n                translatedText: '',\n                speechError: null,\n                translationError: null,\n                lastUpdate: Date.now()\n            };\n        case 'LOAD_SESSION':\n            return {\n                ...state,\n                ...action.payload,\n                lastUpdate: Date.now()\n            };\n        case 'SET_SESSION_ID':\n            return {\n                ...state,\n                sessionId: action.payload,\n                lastUpdate: Date.now()\n            };\n        default:\n            return state;\n    }\n};\n// Context\nconst CaptionContext = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.createContext)(null);\nconst CaptionProvider = (param)=>{\n    let { children } = param;\n    _s();\n    const [state, dispatch] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useReducer)(captionReducer, initialState);\n    // Initialize client-side data and load saved settings\n    react__WEBPACK_IMPORTED_MODULE_1___default().useEffect({\n        \"CaptionProvider.useEffect\": ()=>{\n            if (true) {\n                // Set unique session ID on client side to avoid hydration mismatch\n                dispatch({\n                    type: 'SET_SESSION_ID',\n                    payload: \"session-\".concat(Date.now())\n                });\n                try {\n                    const savedSettings = localStorage.getItem('caption-ninja-settings');\n                    if (savedSettings) {\n                        const settings = JSON.parse(savedSettings);\n                        dispatch({\n                            type: 'UPDATE_SETTINGS',\n                            payload: settings\n                        });\n                    }\n                } catch (error) {\n                    console.warn('Failed to load saved settings:', error);\n                }\n            }\n        }\n    }[\"CaptionProvider.useEffect\"], []);\n    // Save settings to localStorage when they change\n    react__WEBPACK_IMPORTED_MODULE_1___default().useEffect({\n        \"CaptionProvider.useEffect\": ()=>{\n            if (true) {\n                try {\n                    localStorage.setItem('caption-ninja-settings', JSON.stringify(state.settings));\n                } catch (error) {\n                    console.warn('Failed to save caption settings:', error);\n                }\n            }\n        }\n    }[\"CaptionProvider.useEffect\"], [\n        state.settings\n    ]);\n    // Auto-save session data periodically\n    react__WEBPACK_IMPORTED_MODULE_1___default().useEffect({\n        \"CaptionProvider.useEffect\": ()=>{\n            if ( true && state.transcriptHistory.length > 0) {\n                const saveSession = {\n                    \"CaptionProvider.useEffect.saveSession\": ()=>{\n                        try {\n                            const sessionData = {\n                                transcriptHistory: state.transcriptHistory,\n                                sessionId: state.sessionId,\n                                lastUpdate: state.lastUpdate\n                            };\n                            localStorage.setItem('caption-ninja-last-session', JSON.stringify(sessionData));\n                        } catch (error) {\n                            console.warn('Failed to save session data:', error);\n                        }\n                    }\n                }[\"CaptionProvider.useEffect.saveSession\"];\n                const interval = setInterval(saveSession, 30000); // Save every 30 seconds\n                return ({\n                    \"CaptionProvider.useEffect\": ()=>clearInterval(interval)\n                })[\"CaptionProvider.useEffect\"];\n            }\n        }\n    }[\"CaptionProvider.useEffect\"], [\n        state.transcriptHistory,\n        state.sessionId,\n        state.lastUpdate\n    ]);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(CaptionContext.Provider, {\n        value: {\n            state,\n            dispatch\n        },\n        children: children\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive - UGSM-Monarch Business School GmbH\\\\Documents\\\\captionninja\\\\caption-ninja-nextjs\\\\src\\\\contexts\\\\CaptionContext.tsx\",\n        lineNumber: 317,\n        columnNumber: 5\n    }, undefined);\n};\n_s(CaptionProvider, \"s3jE+e7wLGXN/2uWqdAG2uRSMfA=\");\n_c = CaptionProvider;\n// Custom hook to use the caption context\nconst useCaption = ()=>{\n    _s1();\n    const context = (0,react__WEBPACK_IMPORTED_MODULE_1__.useContext)(CaptionContext);\n    if (!context) {\n        throw new Error('useCaption must be used within a CaptionProvider');\n    }\n    return context;\n};\n_s1(useCaption, \"b9L3QQ+jgeyIrH0NfHrJ8nn7VMU=\");\n// Helper hooks for specific functionality\nconst useCaptionActions = ()=>{\n    _s2();\n    const { dispatch } = useCaption();\n    const addTranscriptEntry = react__WEBPACK_IMPORTED_MODULE_1___default().useCallback({\n        \"useCaptionActions.useCallback[addTranscriptEntry]\": (entry)=>{\n            dispatch({\n                type: 'ADD_TRANSCRIPT_ENTRY',\n                payload: entry\n            });\n        }\n    }[\"useCaptionActions.useCallback[addTranscriptEntry]\"], [\n        dispatch\n    ]);\n    const updateTranscriptEntry = react__WEBPACK_IMPORTED_MODULE_1___default().useCallback({\n        \"useCaptionActions.useCallback[updateTranscriptEntry]\": (id, updates)=>{\n            dispatch({\n                type: 'UPDATE_TRANSCRIPT_ENTRY',\n                payload: {\n                    id,\n                    updates\n                }\n            });\n        }\n    }[\"useCaptionActions.useCallback[updateTranscriptEntry]\"], [\n        dispatch\n    ]);\n    const deleteTranscriptEntry = react__WEBPACK_IMPORTED_MODULE_1___default().useCallback({\n        \"useCaptionActions.useCallback[deleteTranscriptEntry]\": (id)=>{\n            dispatch({\n                type: 'DELETE_TRANSCRIPT_ENTRY',\n                payload: id\n            });\n        }\n    }[\"useCaptionActions.useCallback[deleteTranscriptEntry]\"], [\n        dispatch\n    ]);\n    const clearTranscriptHistory = react__WEBPACK_IMPORTED_MODULE_1___default().useCallback({\n        \"useCaptionActions.useCallback[clearTranscriptHistory]\": ()=>{\n            dispatch({\n                type: 'CLEAR_TRANSCRIPT_HISTORY'\n            });\n        }\n    }[\"useCaptionActions.useCallback[clearTranscriptHistory]\"], [\n        dispatch\n    ]);\n    const setFinalTranscript = react__WEBPACK_IMPORTED_MODULE_1___default().useCallback({\n        \"useCaptionActions.useCallback[setFinalTranscript]\": (transcript)=>{\n            dispatch({\n                type: 'SET_FINAL_TRANSCRIPT',\n                payload: transcript\n            });\n        }\n    }[\"useCaptionActions.useCallback[setFinalTranscript]\"], [\n        dispatch\n    ]);\n    const setInterimTranscript = react__WEBPACK_IMPORTED_MODULE_1___default().useCallback({\n        \"useCaptionActions.useCallback[setInterimTranscript]\": (transcript)=>{\n            dispatch({\n                type: 'SET_INTERIM_TRANSCRIPT',\n                payload: transcript\n            });\n        }\n    }[\"useCaptionActions.useCallback[setInterimTranscript]\"], [\n        dispatch\n    ]);\n    const setTranslatedText = react__WEBPACK_IMPORTED_MODULE_1___default().useCallback({\n        \"useCaptionActions.useCallback[setTranslatedText]\": (text)=>{\n            dispatch({\n                type: 'SET_TRANSLATED_TEXT',\n                payload: text\n            });\n        }\n    }[\"useCaptionActions.useCallback[setTranslatedText]\"], [\n        dispatch\n    ]);\n    const updateSettings = react__WEBPACK_IMPORTED_MODULE_1___default().useCallback({\n        \"useCaptionActions.useCallback[updateSettings]\": (settings)=>{\n            dispatch({\n                type: 'UPDATE_SETTINGS',\n                payload: settings\n            });\n        }\n    }[\"useCaptionActions.useCallback[updateSettings]\"], [\n        dispatch\n    ]);\n    const setRecordingStatus = react__WEBPACK_IMPORTED_MODULE_1___default().useCallback({\n        \"useCaptionActions.useCallback[setRecordingStatus]\": (isRecording)=>{\n            dispatch({\n                type: 'SET_RECORDING_STATUS',\n                payload: isRecording\n            });\n        }\n    }[\"useCaptionActions.useCallback[setRecordingStatus]\"], [\n        dispatch\n    ]);\n    const setTranslatingStatus = react__WEBPACK_IMPORTED_MODULE_1___default().useCallback({\n        \"useCaptionActions.useCallback[setTranslatingStatus]\": (isTranslating)=>{\n            dispatch({\n                type: 'SET_TRANSLATING_STATUS',\n                payload: isTranslating\n            });\n        }\n    }[\"useCaptionActions.useCallback[setTranslatingStatus]\"], [\n        dispatch\n    ]);\n    const setTTSPlayingStatus = react__WEBPACK_IMPORTED_MODULE_1___default().useCallback({\n        \"useCaptionActions.useCallback[setTTSPlayingStatus]\": (isPlaying)=>{\n            dispatch({\n                type: 'SET_TTS_PLAYING_STATUS',\n                payload: isPlaying\n            });\n        }\n    }[\"useCaptionActions.useCallback[setTTSPlayingStatus]\"], [\n        dispatch\n    ]);\n    const setSpeechError = react__WEBPACK_IMPORTED_MODULE_1___default().useCallback({\n        \"useCaptionActions.useCallback[setSpeechError]\": (error)=>{\n            dispatch({\n                type: 'SET_SPEECH_ERROR',\n                payload: error\n            });\n        }\n    }[\"useCaptionActions.useCallback[setSpeechError]\"], [\n        dispatch\n    ]);\n    const setTranslationError = react__WEBPACK_IMPORTED_MODULE_1___default().useCallback({\n        \"useCaptionActions.useCallback[setTranslationError]\": (error)=>{\n            dispatch({\n                type: 'SET_TRANSLATION_ERROR',\n                payload: error\n            });\n        }\n    }[\"useCaptionActions.useCallback[setTranslationError]\"], [\n        dispatch\n    ]);\n    const setTTSError = react__WEBPACK_IMPORTED_MODULE_1___default().useCallback({\n        \"useCaptionActions.useCallback[setTTSError]\": (error)=>{\n            dispatch({\n                type: 'SET_TTS_ERROR',\n                payload: error\n            });\n        }\n    }[\"useCaptionActions.useCallback[setTTSError]\"], [\n        dispatch\n    ]);\n    const resetTranscript = react__WEBPACK_IMPORTED_MODULE_1___default().useCallback({\n        \"useCaptionActions.useCallback[resetTranscript]\": ()=>{\n            dispatch({\n                type: 'RESET_TRANSCRIPT'\n            });\n        }\n    }[\"useCaptionActions.useCallback[resetTranscript]\"], [\n        dispatch\n    ]);\n    return {\n        addTranscriptEntry,\n        updateTranscriptEntry,\n        deleteTranscriptEntry,\n        clearTranscriptHistory,\n        setFinalTranscript,\n        setInterimTranscript,\n        setTranslatedText,\n        updateSettings,\n        setRecordingStatus,\n        setTranslatingStatus,\n        setTTSPlayingStatus,\n        setSpeechError,\n        setTranslationError,\n        setTTSError,\n        resetTranscript\n    };\n};\n_s2(useCaptionActions, \"/jrgnCMiKYSWHqPQveTc/BIHWE4=\", false, function() {\n    return [\n        useCaption\n    ];\n});\n// Hook for accessing caption state selectors\nconst useCaptionSelectors = ()=>{\n    _s3();\n    const { state } = useCaption();\n    const getCurrentTranscript = react__WEBPACK_IMPORTED_MODULE_1___default().useCallback({\n        \"useCaptionSelectors.useCallback[getCurrentTranscript]\": ()=>{\n            return state.finalTranscript + (state.interimTranscript ? ' ' + state.interimTranscript : '');\n        }\n    }[\"useCaptionSelectors.useCallback[getCurrentTranscript]\"], [\n        state.finalTranscript,\n        state.interimTranscript\n    ]);\n    const getTranscriptWordCount = react__WEBPACK_IMPORTED_MODULE_1___default().useCallback({\n        \"useCaptionSelectors.useCallback[getTranscriptWordCount]\": ()=>{\n            const fullTranscript = getCurrentTranscript();\n            return fullTranscript.trim().split(/\\s+/).filter({\n                \"useCaptionSelectors.useCallback[getTranscriptWordCount]\": (word)=>word.length > 0\n            }[\"useCaptionSelectors.useCallback[getTranscriptWordCount]\"]).length;\n        }\n    }[\"useCaptionSelectors.useCallback[getTranscriptWordCount]\"], [\n        getCurrentTranscript\n    ]);\n    const getSessionDuration = react__WEBPACK_IMPORTED_MODULE_1___default().useCallback({\n        \"useCaptionSelectors.useCallback[getSessionDuration]\": ()=>{\n            if (state.transcriptHistory.length === 0) return 0;\n            const firstEntry = state.transcriptHistory[0];\n            const lastEntry = state.transcriptHistory[state.transcriptHistory.length - 1];\n            return lastEntry.timestamp - firstEntry.timestamp;\n        }\n    }[\"useCaptionSelectors.useCallback[getSessionDuration]\"], [\n        state.transcriptHistory\n    ]);\n    const hasActiveTranscript = react__WEBPACK_IMPORTED_MODULE_1___default().useCallback({\n        \"useCaptionSelectors.useCallback[hasActiveTranscript]\": ()=>{\n            return state.finalTranscript.length > 0 || state.interimTranscript.length > 0;\n        }\n    }[\"useCaptionSelectors.useCallback[hasActiveTranscript]\"], [\n        state.finalTranscript,\n        state.interimTranscript\n    ]);\n    const getLanguageStats = react__WEBPACK_IMPORTED_MODULE_1___default().useCallback({\n        \"useCaptionSelectors.useCallback[getLanguageStats]\": ()=>{\n            const stats = {};\n            state.transcriptHistory.forEach({\n                \"useCaptionSelectors.useCallback[getLanguageStats]\": (entry)=>{\n                    const lang = entry.languageCode || 'unknown';\n                    stats[lang] = (stats[lang] || 0) + 1;\n                }\n            }[\"useCaptionSelectors.useCallback[getLanguageStats]\"]);\n            return stats;\n        }\n    }[\"useCaptionSelectors.useCallback[getLanguageStats]\"], [\n        state.transcriptHistory\n    ]);\n    return {\n        getCurrentTranscript,\n        getTranscriptWordCount,\n        getSessionDuration,\n        hasActiveTranscript,\n        getLanguageStats\n    };\n};\n_s3(useCaptionSelectors, \"wR7VlyPqzmpFy4JNrGDJjW/fGlY=\", false, function() {\n    return [\n        useCaption\n    ];\n});\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (CaptionContext);\nvar _c;\n$RefreshReg$(_c, \"CaptionProvider\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/contexts/CaptionContext.tsx\n"));

/***/ })

},
/******/ __webpack_require__ => { // webpackRuntimeModules
/******/ var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
/******/ __webpack_require__.O(0, ["main-app"], () => (__webpack_exec__("(app-pages-browser)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CAliyuSunusi%5C%5COneDrive%20-%20UGSM-Monarch%20Business%20School%20GmbH%5C%5CDocuments%5C%5Ccaptionninja%5C%5Ccaption-ninja-nextjs%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-sans%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistSans%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CAliyuSunusi%5C%5COneDrive%20-%20UGSM-Monarch%20Business%20School%20GmbH%5C%5CDocuments%5C%5Ccaptionninja%5C%5Ccaption-ninja-nextjs%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist_Mono%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-mono%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistMono%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CAliyuSunusi%5C%5COneDrive%20-%20UGSM-Monarch%20Business%20School%20GmbH%5C%5CDocuments%5C%5Ccaptionninja%5C%5Ccaption-ninja-nextjs%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CAliyuSunusi%5C%5COneDrive%20-%20UGSM-Monarch%20Business%20School%20GmbH%5C%5CDocuments%5C%5Ccaptionninja%5C%5Ccaption-ninja-nextjs%5C%5Csrc%5C%5Ccontexts%5C%5CCaptionContext.tsx%22%2C%22ids%22%3A%5B%22CaptionProvider%22%5D%7D&server=false!")));
/******/ var __webpack_exports__ = __webpack_require__.O();
/******/ _N_E = __webpack_exports__;
/******/ }
]);