/**
 * CaptionContext
 *
 * Global context for managing caption-related state across the application.
 * Provides centralized state management for transcripts, translations, and settings.
 */

'use client';

import React, { createContext, useContext, useReducer, ReactNode } from 'react';

// Types
export interface TranscriptEntry {
  id: string;
  text: string;
  timestamp: number;
  duration?: number;
  translatedText?: string;
  isInterim: boolean;
  sessionId?: string;
  languageCode?: string;
  confidence?: number;
}

export interface CaptionSettings {
  speechLanguage: string;
  translationEnabled: boolean;
  sourceLanguage: string;
  targetLanguage: string;
  ttsEnabled: boolean;
  ttsAutoPlay: boolean;
  selectedVoice: string;
  ttsRate: number;
  ttsPitch: number;
  ttsVolume: number;
}

export interface CaptionState {
  // Current transcript data
  finalTranscript: string;
  interimTranscript: string;
  translatedText: string;
  
  // Historical data
  transcriptHistory: TranscriptEntry[];
  
  // Status flags
  isRecording: boolean;
  isTranslating: boolean;
  isTTSPlaying: boolean;
  
  // Settings
  settings: CaptionSettings;
  
  // Error states
  speechError: string | null;
  translationError: string | null;
  ttsError: string | null;
  
  // Metadata
  sessionId: string;
  lastUpdate: number;
}

// Action types
export type CaptionAction =
  | { type: 'SET_FINAL_TRANSCRIPT'; payload: string }
  | { type: 'SET_INTERIM_TRANSCRIPT'; payload: string }
  | { type: 'SET_TRANSLATED_TEXT'; payload: string }
  | { type: 'ADD_TRANSCRIPT_ENTRY'; payload: TranscriptEntry }
  | { type: 'UPDATE_TRANSCRIPT_ENTRY'; payload: { id: string; updates: Partial<TranscriptEntry> } }
  | { type: 'DELETE_TRANSCRIPT_ENTRY'; payload: string }
  | { type: 'CLEAR_TRANSCRIPT_HISTORY' }
  | { type: 'SET_RECORDING_STATUS'; payload: boolean }
  | { type: 'SET_TRANSLATING_STATUS'; payload: boolean }
  | { type: 'SET_TTS_PLAYING_STATUS'; payload: boolean }
  | { type: 'UPDATE_SETTINGS'; payload: Partial<CaptionSettings> }
  | { type: 'SET_SPEECH_ERROR'; payload: string | null }
  | { type: 'SET_TRANSLATION_ERROR'; payload: string | null }
  | { type: 'SET_TTS_ERROR'; payload: string | null }
  | { type: 'RESET_TRANSCRIPT' }
  | { type: 'LOAD_SESSION'; payload: Partial<CaptionState> }
  | { type: 'SET_SESSION_ID'; payload: string };

// Initial state
const initialSettings: CaptionSettings = {
  speechLanguage: 'en-US',
  translationEnabled: false,
  sourceLanguage: 'auto',
  targetLanguage: 'en',
  ttsEnabled: false,
  ttsAutoPlay: false,
  selectedVoice: '',
  ttsRate: 1,
  ttsPitch: 1,
  ttsVolume: 1,
};

const initialState: CaptionState = {
  finalTranscript: '',
  interimTranscript: '',
  translatedText: '',
  transcriptHistory: [],
  isRecording: false,
  isTranslating: false,
  isTTSPlaying: false,
  settings: initialSettings,
  speechError: null,
  translationError: null,
  ttsError: null,
  sessionId: 'session-initial',
  lastUpdate: 0,
};

// Reducer
const captionReducer = (state: CaptionState, action: CaptionAction): CaptionState => {
  switch (action.type) {
    case 'SET_FINAL_TRANSCRIPT':
      return {
        ...state,
        finalTranscript: action.payload,
        lastUpdate: Date.now(),
      };

    case 'SET_INTERIM_TRANSCRIPT':
      return {
        ...state,
        interimTranscript: action.payload,
        lastUpdate: Date.now(),
      };

    case 'SET_TRANSLATED_TEXT':
      return {
        ...state,
        translatedText: action.payload,
        lastUpdate: Date.now(),
      };

    case 'ADD_TRANSCRIPT_ENTRY':
      return {
        ...state,
        transcriptHistory: [...state.transcriptHistory, action.payload],
        lastUpdate: Date.now(),
      };

    case 'UPDATE_TRANSCRIPT_ENTRY':
      return {
        ...state,
        transcriptHistory: state.transcriptHistory.map(entry =>
          entry.id === action.payload.id
            ? { ...entry, ...action.payload.updates }
            : entry
        ),
        lastUpdate: Date.now(),
      };

    case 'DELETE_TRANSCRIPT_ENTRY':
      return {
        ...state,
        transcriptHistory: state.transcriptHistory.filter(
          entry => entry.id !== action.payload
        ),
        lastUpdate: Date.now(),
      };

    case 'CLEAR_TRANSCRIPT_HISTORY':
      return {
        ...state,
        transcriptHistory: [],
        lastUpdate: Date.now(),
      };

    case 'SET_RECORDING_STATUS':
      return {
        ...state,
        isRecording: action.payload,
        lastUpdate: Date.now(),
      };

    case 'SET_TRANSLATING_STATUS':
      return {
        ...state,
        isTranslating: action.payload,
        lastUpdate: Date.now(),
      };

    case 'SET_TTS_PLAYING_STATUS':
      return {
        ...state,
        isTTSPlaying: action.payload,
        lastUpdate: Date.now(),
      };

    case 'UPDATE_SETTINGS':
      return {
        ...state,
        settings: { ...state.settings, ...action.payload },
        lastUpdate: Date.now(),
      };

    case 'SET_SPEECH_ERROR':
      return {
        ...state,
        speechError: action.payload,
        lastUpdate: Date.now(),
      };

    case 'SET_TRANSLATION_ERROR':
      return {
        ...state,
        translationError: action.payload,
        lastUpdate: Date.now(),
      };

    case 'SET_TTS_ERROR':
      return {
        ...state,
        ttsError: action.payload,
        lastUpdate: Date.now(),
      };

    case 'RESET_TRANSCRIPT':
      return {
        ...state,
        finalTranscript: '',
        interimTranscript: '',
        translatedText: '',
        speechError: null,
        translationError: null,
        lastUpdate: Date.now(),
      };

    case 'LOAD_SESSION':
      return {
        ...state,
        ...action.payload,
        lastUpdate: Date.now(),
      };

    case 'SET_SESSION_ID':
      return {
        ...state,
        sessionId: action.payload,
        lastUpdate: Date.now(),
      };

    default:
      return state;
  }
};

// Context
const CaptionContext = createContext<{
  state: CaptionState;
  dispatch: React.Dispatch<CaptionAction>;
} | null>(null);

// Provider component
interface CaptionProviderProps {
  children: ReactNode;
}

export const CaptionProvider: React.FC<CaptionProviderProps> = ({ children }) => {
  const [state, dispatch] = useReducer(captionReducer, initialState);

  // Initialize client-side data and load saved settings
  React.useEffect(() => {
    if (typeof window !== 'undefined') {
      // Set unique session ID on client side to avoid hydration mismatch
      dispatch({ type: 'SET_SESSION_ID', payload: `session-${Date.now()}` });

      try {
        const savedSettings = localStorage.getItem('caption-ninja-settings');
        if (savedSettings) {
          const settings = JSON.parse(savedSettings);
          dispatch({ type: 'UPDATE_SETTINGS', payload: settings });
        }
      } catch (error) {
        console.warn('Failed to load saved settings:', error);
      }
    }
  }, []);

  // Save settings to localStorage when they change
  React.useEffect(() => {
    if (typeof window !== 'undefined') {
      try {
        localStorage.setItem('caption-ninja-settings', JSON.stringify(state.settings));
      } catch (error) {
        console.warn('Failed to save caption settings:', error);
      }
    }
  }, [state.settings]);

  // Auto-save session data periodically
  React.useEffect(() => {
    if (typeof window !== 'undefined' && state.transcriptHistory.length > 0) {
      const saveSession = () => {
        try {
          const sessionData = {
            transcriptHistory: state.transcriptHistory,
            sessionId: state.sessionId,
            lastUpdate: state.lastUpdate,
          };
          localStorage.setItem('caption-ninja-last-session', JSON.stringify(sessionData));
        } catch (error) {
          console.warn('Failed to save session data:', error);
        }
      };

      const interval = setInterval(saveSession, 30000); // Save every 30 seconds
      return () => clearInterval(interval);
    }
  }, [state.transcriptHistory, state.sessionId, state.lastUpdate]);

  return (
    <CaptionContext.Provider value={{ state, dispatch }}>
      {children}
    </CaptionContext.Provider>
  );
};

// Custom hook to use the caption context
export const useCaption = () => {
  const context = useContext(CaptionContext);
  if (!context) {
    throw new Error('useCaption must be used within a CaptionProvider');
  }
  return context;
};

// Helper hooks for specific functionality
export const useCaptionActions = () => {
  const { dispatch } = useCaption();

  const addTranscriptEntry = React.useCallback((entry: TranscriptEntry) => {
    dispatch({ type: 'ADD_TRANSCRIPT_ENTRY', payload: entry });
  }, [dispatch]);

  const updateTranscriptEntry = React.useCallback((id: string, updates: Partial<TranscriptEntry>) => {
    dispatch({ type: 'UPDATE_TRANSCRIPT_ENTRY', payload: { id, updates } });
  }, [dispatch]);

  const deleteTranscriptEntry = React.useCallback((id: string) => {
    dispatch({ type: 'DELETE_TRANSCRIPT_ENTRY', payload: id });
  }, [dispatch]);

  const clearTranscriptHistory = React.useCallback(() => {
    dispatch({ type: 'CLEAR_TRANSCRIPT_HISTORY' });
  }, [dispatch]);

  const setFinalTranscript = React.useCallback((transcript: string) => {
    dispatch({ type: 'SET_FINAL_TRANSCRIPT', payload: transcript });
  }, [dispatch]);

  const setInterimTranscript = React.useCallback((transcript: string) => {
    dispatch({ type: 'SET_INTERIM_TRANSCRIPT', payload: transcript });
  }, [dispatch]);

  const setTranslatedText = React.useCallback((text: string) => {
    dispatch({ type: 'SET_TRANSLATED_TEXT', payload: text });
  }, [dispatch]);

  const updateSettings = React.useCallback((settings: Partial<CaptionSettings>) => {
    dispatch({ type: 'UPDATE_SETTINGS', payload: settings });
  }, [dispatch]);

  const setRecordingStatus = React.useCallback((isRecording: boolean) => {
    dispatch({ type: 'SET_RECORDING_STATUS', payload: isRecording });
  }, [dispatch]);

  const setTranslatingStatus = React.useCallback((isTranslating: boolean) => {
    dispatch({ type: 'SET_TRANSLATING_STATUS', payload: isTranslating });
  }, [dispatch]);

  const setTTSPlayingStatus = React.useCallback((isPlaying: boolean) => {
    dispatch({ type: 'SET_TTS_PLAYING_STATUS', payload: isPlaying });
  }, [dispatch]);

  const setSpeechError = React.useCallback((error: string | null) => {
    dispatch({ type: 'SET_SPEECH_ERROR', payload: error });
  }, [dispatch]);

  const setTranslationError = React.useCallback((error: string | null) => {
    dispatch({ type: 'SET_TRANSLATION_ERROR', payload: error });
  }, [dispatch]);

  const setTTSError = React.useCallback((error: string | null) => {
    dispatch({ type: 'SET_TTS_ERROR', payload: error });
  }, [dispatch]);

  const resetTranscript = React.useCallback(() => {
    dispatch({ type: 'RESET_TRANSCRIPT' });
  }, [dispatch]);

  return {
    addTranscriptEntry,
    updateTranscriptEntry,
    deleteTranscriptEntry,
    clearTranscriptHistory,
    setFinalTranscript,
    setInterimTranscript,
    setTranslatedText,
    updateSettings,
    setRecordingStatus,
    setTranslatingStatus,
    setTTSPlayingStatus,
    setSpeechError,
    setTranslationError,
    setTTSError,
    resetTranscript,
  };
};

// Hook for accessing caption state selectors
export const useCaptionSelectors = () => {
  const { state } = useCaption();

  const getCurrentTranscript = React.useCallback(() => {
    return state.finalTranscript + (state.interimTranscript ? ' ' + state.interimTranscript : '');
  }, [state.finalTranscript, state.interimTranscript]);

  const getTranscriptWordCount = React.useCallback(() => {
    const fullTranscript = getCurrentTranscript();
    return fullTranscript.trim().split(/\s+/).filter(word => word.length > 0).length;
  }, [getCurrentTranscript]);

  const getSessionDuration = React.useCallback(() => {
    if (state.transcriptHistory.length === 0) return 0;
    const firstEntry = state.transcriptHistory[0];
    const lastEntry = state.transcriptHistory[state.transcriptHistory.length - 1];
    return lastEntry.timestamp - firstEntry.timestamp;
  }, [state.transcriptHistory]);

  const hasActiveTranscript = React.useCallback(() => {
    return state.finalTranscript.length > 0 || state.interimTranscript.length > 0;
  }, [state.finalTranscript, state.interimTranscript]);

  const getLanguageStats = React.useCallback(() => {
    const stats: { [language: string]: number } = {};
    state.transcriptHistory.forEach(entry => {
      const lang = entry.languageCode || 'unknown';
      stats[lang] = (stats[lang] || 0) + 1;
    });
    return stats;
  }, [state.transcriptHistory]);

  return {
    getCurrentTranscript,
    getTranscriptWordCount,
    getSessionDuration,
    hasActiveTranscript,
    getLanguageStats,
  };
};

export default CaptionContext;
