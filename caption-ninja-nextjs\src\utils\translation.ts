/**
 * Translation Utilities
 * 
 * Utility functions for translation functionality.
 * Based on the translation logic from translate.html.
 */

// Language codes supported by Google Cloud Translation API
export const TRANSLATION_LANGUAGES = [
  { code: 'auto', name: 'Auto-detect', nativeName: 'Auto-detect' },
  { code: 'en', name: 'English', nativeName: 'English' },
  { code: 'ha', name: 'Hausa', nativeName: 'Hausa' },
  { code: 'es', name: 'Spanish', nativeName: 'Español' },
  { code: 'fr', name: 'French', nativeName: 'Français' },
  { code: 'de', name: 'German', nativeName: 'Deutsch' },
  { code: 'it', name: 'Italian', nativeName: 'Italiano' },
  { code: 'pt', name: 'Portuguese', nativeName: 'Português' },
  { code: 'ru', name: 'Russian', nativeName: 'Русский' },
  { code: 'ja', name: 'Japanese', nativeName: '日本語' },
  { code: 'ko', name: 'Korean', nativeName: '한국어' },
  { code: 'zh', name: 'Chinese', nativeName: '中文' },
  { code: 'ar', name: 'Arabic', nativeName: 'العربية' },
  { code: 'hi', name: 'Hindi', nativeName: 'हिन्दी' },
  { code: 'th', name: 'Thai', nativeName: 'ไทย' },
  { code: 'vi', name: 'Vietnamese', nativeName: 'Tiếng Việt' },
  { code: 'tr', name: 'Turkish', nativeName: 'Türkçe' },
  { code: 'pl', name: 'Polish', nativeName: 'Polski' },
  { code: 'nl', name: 'Dutch', nativeName: 'Nederlands' },
  { code: 'sv', name: 'Swedish', nativeName: 'Svenska' },
  { code: 'da', name: 'Danish', nativeName: 'Dansk' },
  { code: 'no', name: 'Norwegian', nativeName: 'Norsk' },
  { code: 'fi', name: 'Finnish', nativeName: 'Suomi' },
  { code: 'yo', name: 'Yoruba', nativeName: 'Yorùbá' },
  { code: 'ig', name: 'Igbo', nativeName: 'Igbo' },
  { code: 'sw', name: 'Swahili', nativeName: 'Kiswahili' },
  { code: 'am', name: 'Amharic', nativeName: 'አማርኛ' },
];

// Common language pairs for quick selection
export const COMMON_LANGUAGE_PAIRS = [
  { from: 'en', to: 'ha', label: 'English → Hausa' },
  { from: 'ha', to: 'en', label: 'Hausa → English' },
  { from: 'en', to: 'es', label: 'English → Spanish' },
  { from: 'es', to: 'en', label: 'Spanish → English' },
  { from: 'en', to: 'fr', label: 'English → French' },
  { from: 'fr', to: 'en', label: 'French → English' },
  { from: 'en', to: 'de', label: 'English → German' },
  { from: 'de', to: 'en', label: 'German → English' },
  { from: 'en', to: 'zh', label: 'English → Chinese' },
  { from: 'zh', to: 'en', label: 'Chinese → English' },
];

// Get language display name
export const getTranslationLanguageName = (languageCode: string): string => {
  const language = TRANSLATION_LANGUAGES.find(lang => lang.code === languageCode);
  return language ? language.name : languageCode;
};

// Get language native name
export const getTranslationLanguageNativeName = (languageCode: string): string => {
  const language = TRANSLATION_LANGUAGES.find(lang => lang.code === languageCode);
  return language ? language.nativeName : languageCode;
};

// Validate translation language code
export const isValidTranslationLanguage = (languageCode: string): boolean => {
  return TRANSLATION_LANGUAGES.some(lang => lang.code === languageCode);
};

// Convert speech recognition language code to translation language code
export const speechToTranslationLanguage = (speechLangCode: string): string => {
  // Map speech recognition language codes to translation language codes
  const mapping: { [key: string]: string } = {
    'en-US': 'en',
    'en-GB': 'en',
    'ha-NG': 'ha',
    'es-ES': 'es',
    'es-MX': 'es',
    'fr-FR': 'fr',
    'de-DE': 'de',
    'it-IT': 'it',
    'pt-BR': 'pt',
    'pt-PT': 'pt',
    'ru-RU': 'ru',
    'ja-JP': 'ja',
    'ko-KR': 'ko',
    'zh-CN': 'zh',
    'zh-TW': 'zh',
    'ar-SA': 'ar',
    'hi-IN': 'hi',
    'th-TH': 'th',
    'vi-VN': 'vi',
    'tr-TR': 'tr',
    'pl-PL': 'pl',
    'nl-NL': 'nl',
    'sv-SE': 'sv',
    'da-DK': 'da',
    'no-NO': 'no',
    'fi-FI': 'fi',
  };

  return mapping[speechLangCode] || speechLangCode.split('-')[0];
};

// Check if text needs translation
export const needsTranslation = (
  text: string,
  sourceLanguage: string,
  targetLanguage: string
): boolean => {
  if (!text.trim()) return false;
  if (sourceLanguage === targetLanguage) return false;
  if (sourceLanguage === 'auto') return true; // Always translate when auto-detecting
  return true;
};

// Estimate translation cost (rough estimate for Google Cloud Translation)
export const estimateTranslationCost = (text: string): number => {
  // Google Cloud Translation pricing is approximately $20 per 1M characters
  const characters = text.length;
  const costPer1MChars = 20; // USD
  return (characters / 1000000) * costPer1MChars;
};

// Split text into chunks for translation (to handle API limits)
export const splitTextForTranslation = (text: string, maxChunkSize = 5000): string[] => {
  if (text.length <= maxChunkSize) {
    return [text];
  }

  const chunks: string[] = [];
  const sentences = text.split(/[.!?]+/).filter(s => s.trim());
  
  let currentChunk = '';
  
  for (const sentence of sentences) {
    const trimmedSentence = sentence.trim();
    if (!trimmedSentence) continue;
    
    const sentenceWithPunctuation = trimmedSentence + '.';
    
    if ((currentChunk + sentenceWithPunctuation).length <= maxChunkSize) {
      currentChunk += (currentChunk ? ' ' : '') + sentenceWithPunctuation;
    } else {
      if (currentChunk) {
        chunks.push(currentChunk);
      }
      currentChunk = sentenceWithPunctuation;
    }
  }
  
  if (currentChunk) {
    chunks.push(currentChunk);
  }
  
  return chunks.length > 0 ? chunks : [text];
};

// Merge translated chunks back together
export const mergeTranslatedChunks = (chunks: string[]): string => {
  return chunks.join(' ').trim();
};

// Detect language from text (simple heuristic)
export const detectLanguageHeuristic = (text: string): string | null => {
  const patterns: { [key: string]: RegExp } = {
    'ar': /[\u0600-\u06FF]/,
    'zh': /[\u4e00-\u9fff]/,
    'ja': /[\u3040-\u309f\u30a0-\u30ff]/,
    'ko': /[\uac00-\ud7af]/,
    'hi': /[\u0900-\u097f]/,
    'th': /[\u0e00-\u0e7f]/,
    'ru': /[\u0400-\u04ff]/,
    'ha': /\b(da|na|ya|ba|ta|ka|ga|sa|za|fa|ma|wa|la|ra|ha)\b/i, // Common Hausa words
  };

  for (const [lang, pattern] of Object.entries(patterns)) {
    if (pattern.test(text)) {
      return lang;
    }
  }

  return null; // Could not detect
};

// Format translation confidence score
export const formatTranslationConfidence = (confidence?: number): string => {
  if (confidence === undefined) return 'N/A';
  return `${Math.round(confidence * 100)}%`;
};

// Check if translation is cached
export const generateTranslationCacheKey = (
  text: string,
  sourceLanguage: string,
  targetLanguage: string
): string => {
  return `${sourceLanguage}-${targetLanguage}-${text}`;
};

// Validate API key format (basic validation)
export const isValidGoogleCloudApiKey = (apiKey: string): boolean => {
  // Google Cloud API keys typically start with 'AIza' and are 39 characters long
  return /^AIza[0-9A-Za-z_-]{35}$/.test(apiKey);
};

// Supported translation languages (Google Cloud Translation API)
export const SUPPORTED_TRANSLATION_LANGUAGES = [
  { code: 'af', name: 'Afrikaans', flag: '🇿🇦' },
  { code: 'sq', name: 'Albanian', flag: '🇦🇱' },
  { code: 'am', name: 'Amharic', flag: '🇪🇹' },
  { code: 'ar', name: 'Arabic', flag: '🇸🇦' },
  { code: 'hy', name: 'Armenian', flag: '🇦🇲' },
  { code: 'az', name: 'Azerbaijani', flag: '🇦🇿' },
  { code: 'eu', name: 'Basque', flag: '🇪🇸' },
  { code: 'be', name: 'Belarusian', flag: '🇧🇾' },
  { code: 'bn', name: 'Bengali', flag: '🇧🇩' },
  { code: 'bs', name: 'Bosnian', flag: '🇧🇦' },
  { code: 'bg', name: 'Bulgarian', flag: '🇧🇬' },
  { code: 'ca', name: 'Catalan', flag: '🇪🇸' },
  { code: 'ceb', name: 'Cebuano', flag: '🇵🇭' },
  { code: 'zh', name: 'Chinese (Simplified)', flag: '🇨🇳' },
  { code: 'zh-TW', name: 'Chinese (Traditional)', flag: '🇹🇼' },
  { code: 'co', name: 'Corsican', flag: '🇫🇷' },
  { code: 'hr', name: 'Croatian', flag: '🇭🇷' },
  { code: 'cs', name: 'Czech', flag: '🇨🇿' },
  { code: 'da', name: 'Danish', flag: '🇩🇰' },
  { code: 'nl', name: 'Dutch', flag: '🇳🇱' },
  { code: 'en', name: 'English', flag: '🇺🇸' },
  { code: 'eo', name: 'Esperanto', flag: '🌍' },
  { code: 'et', name: 'Estonian', flag: '🇪🇪' },
  { code: 'fi', name: 'Finnish', flag: '🇫🇮' },
  { code: 'fr', name: 'French', flag: '🇫🇷' },
  { code: 'fy', name: 'Frisian', flag: '🇳🇱' },
  { code: 'gl', name: 'Galician', flag: '🇪🇸' },
  { code: 'ka', name: 'Georgian', flag: '🇬🇪' },
  { code: 'de', name: 'German', flag: '🇩🇪' },
  { code: 'el', name: 'Greek', flag: '🇬🇷' },
  { code: 'gu', name: 'Gujarati', flag: '🇮🇳' },
  { code: 'ht', name: 'Haitian Creole', flag: '🇭🇹' },
  { code: 'ha', name: 'Hausa', flag: '🇳🇬' },
  { code: 'haw', name: 'Hawaiian', flag: '🇺🇸' },
  { code: 'he', name: 'Hebrew', flag: '🇮🇱' },
  { code: 'hi', name: 'Hindi', flag: '🇮🇳' },
  { code: 'hmn', name: 'Hmong', flag: '🇱🇦' },
  { code: 'hu', name: 'Hungarian', flag: '🇭🇺' },
  { code: 'is', name: 'Icelandic', flag: '🇮🇸' },
  { code: 'ig', name: 'Igbo', flag: '🇳🇬' },
  { code: 'id', name: 'Indonesian', flag: '🇮🇩' },
  { code: 'ga', name: 'Irish', flag: '🇮🇪' },
  { code: 'it', name: 'Italian', flag: '🇮🇹' },
  { code: 'ja', name: 'Japanese', flag: '🇯🇵' },
  { code: 'jv', name: 'Javanese', flag: '🇮🇩' },
  { code: 'kn', name: 'Kannada', flag: '🇮🇳' },
  { code: 'kk', name: 'Kazakh', flag: '🇰🇿' },
  { code: 'km', name: 'Khmer', flag: '🇰🇭' },
  { code: 'rw', name: 'Kinyarwanda', flag: '🇷🇼' },
  { code: 'ko', name: 'Korean', flag: '🇰🇷' },
  { code: 'ku', name: 'Kurdish', flag: '🇹🇷' },
  { code: 'ky', name: 'Kyrgyz', flag: '🇰🇬' },
  { code: 'lo', name: 'Lao', flag: '🇱🇦' },
  { code: 'lv', name: 'Latvian', flag: '🇱🇻' },
  { code: 'lt', name: 'Lithuanian', flag: '🇱🇹' },
  { code: 'lb', name: 'Luxembourgish', flag: '🇱🇺' },
  { code: 'mk', name: 'Macedonian', flag: '🇲🇰' },
  { code: 'mg', name: 'Malagasy', flag: '🇲🇬' },
  { code: 'ms', name: 'Malay', flag: '🇲🇾' },
  { code: 'ml', name: 'Malayalam', flag: '🇮🇳' },
  { code: 'mt', name: 'Maltese', flag: '🇲🇹' },
  { code: 'mi', name: 'Maori', flag: '🇳🇿' },
  { code: 'mr', name: 'Marathi', flag: '🇮🇳' },
  { code: 'mn', name: 'Mongolian', flag: '🇲🇳' },
  { code: 'my', name: 'Myanmar (Burmese)', flag: '🇲🇲' },
  { code: 'ne', name: 'Nepali', flag: '🇳🇵' },
  { code: 'no', name: 'Norwegian', flag: '🇳🇴' },
  { code: 'ny', name: 'Nyanja (Chichewa)', flag: '🇲🇼' },
  { code: 'or', name: 'Odia (Oriya)', flag: '🇮🇳' },
  { code: 'ps', name: 'Pashto', flag: '🇦🇫' },
  { code: 'fa', name: 'Persian', flag: '🇮🇷' },
  { code: 'pl', name: 'Polish', flag: '🇵🇱' },
  { code: 'pt', name: 'Portuguese', flag: '🇵🇹' },
  { code: 'pa', name: 'Punjabi', flag: '🇮🇳' },
  { code: 'ro', name: 'Romanian', flag: '🇷🇴' },
  { code: 'ru', name: 'Russian', flag: '🇷🇺' },
  { code: 'sm', name: 'Samoan', flag: '🇼🇸' },
  { code: 'gd', name: 'Scots Gaelic', flag: '🏴󠁧󠁢󠁳󠁣󠁴󠁿' },
  { code: 'sr', name: 'Serbian', flag: '🇷🇸' },
  { code: 'st', name: 'Sesotho', flag: '🇱🇸' },
  { code: 'sn', name: 'Shona', flag: '🇿🇼' },
  { code: 'sd', name: 'Sindhi', flag: '🇵🇰' },
  { code: 'si', name: 'Sinhala (Sinhalese)', flag: '🇱🇰' },
  { code: 'sk', name: 'Slovak', flag: '🇸🇰' },
  { code: 'sl', name: 'Slovenian', flag: '🇸🇮' },
  { code: 'so', name: 'Somali', flag: '🇸🇴' },
  { code: 'es', name: 'Spanish', flag: '🇪🇸' },
  { code: 'su', name: 'Sundanese', flag: '🇮🇩' },
  { code: 'sw', name: 'Swahili', flag: '🇰🇪' },
  { code: 'sv', name: 'Swedish', flag: '🇸🇪' },
  { code: 'tl', name: 'Tagalog (Filipino)', flag: '🇵🇭' },
  { code: 'tg', name: 'Tajik', flag: '🇹🇯' },
  { code: 'ta', name: 'Tamil', flag: '🇮🇳' },
  { code: 'tt', name: 'Tatar', flag: '🇷🇺' },
  { code: 'te', name: 'Telugu', flag: '🇮🇳' },
  { code: 'th', name: 'Thai', flag: '🇹🇭' },
  { code: 'tr', name: 'Turkish', flag: '🇹🇷' },
  { code: 'tk', name: 'Turkmen', flag: '🇹🇲' },
  { code: 'uk', name: 'Ukrainian', flag: '🇺🇦' },
  { code: 'ur', name: 'Urdu', flag: '🇵🇰' },
  { code: 'ug', name: 'Uyghur', flag: '🇨🇳' },
  { code: 'uz', name: 'Uzbek', flag: '🇺🇿' },
  { code: 'vi', name: 'Vietnamese', flag: '🇻🇳' },
  { code: 'cy', name: 'Welsh', flag: '🏴󠁧󠁢󠁷󠁬󠁳󠁿' },
  { code: 'xh', name: 'Xhosa', flag: '🇿🇦' },
  { code: 'yi', name: 'Yiddish', flag: '🇮🇱' },
  { code: 'yo', name: 'Yoruba', flag: '🇳🇬' },
  { code: 'zu', name: 'Zulu', flag: '🇿🇦' }
];

// Parse translation API error
export const parseTranslationError = (error: any): string => {
  if (typeof error === 'string') {
    return error;
  }

  if (error?.message) {
    return error.message;
  }

  if (error?.error?.message) {
    return error.error.message;
  }

  // Common Google Cloud Translation API errors
  const commonErrors: { [key: string]: string } = {
    'INVALID_ARGUMENT': 'Invalid translation request parameters.',
    'PERMISSION_DENIED': 'Translation API access denied. Check your API key.',
    'RESOURCE_EXHAUSTED': 'Translation quota exceeded. Please try again later.',
    'UNAVAILABLE': 'Translation service is temporarily unavailable.',
    'DEADLINE_EXCEEDED': 'Translation request timed out.',
    'UNAUTHENTICATED': 'Invalid or missing API key.',
  };

  const errorCode = error?.code || error?.error?.code;
  if (errorCode && commonErrors[errorCode]) {
    return commonErrors[errorCode];
  }

  return 'Translation failed with unknown error.';
};

// Get suggested target language based on source language
export const getSuggestedTargetLanguage = (sourceLanguage: string): string => {
  const suggestions: { [key: string]: string } = {
    'en': 'ha', // English -> Hausa
    'ha': 'en', // Hausa -> English
    'es': 'en', // Spanish -> English
    'fr': 'en', // French -> English
    'de': 'en', // German -> English
    'zh': 'en', // Chinese -> English
    'ja': 'en', // Japanese -> English
    'ko': 'en', // Korean -> English
    'ar': 'en', // Arabic -> English
    'hi': 'en', // Hindi -> English
  };

  return suggestions[sourceLanguage] || 'en';
};
