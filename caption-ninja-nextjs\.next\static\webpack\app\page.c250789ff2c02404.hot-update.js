"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./src/hooks/useSpeechRecognition.ts":
/*!*******************************************!*\
  !*** ./src/hooks/useSpeechRecognition.ts ***!
  \*******************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n/**\n * useSpeechRecognition Hook\n * \n * React hook for managing speech recognition functionality.\n * Based on the speech recognition implementation from index.html.\n */ \nconst useSpeechRecognition = function() {\n    let options = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : {};\n    // Memoize default options to prevent recreation on every render\n    const defaultOptions = (0,react__WEBPACK_IMPORTED_MODULE_0__.useMemo)({\n        \"useSpeechRecognition.useMemo[defaultOptions]\": ()=>({\n                language: 'en-US',\n                continuous: true,\n                interimResults: true,\n                maxAlternatives: 1\n            })\n    }[\"useSpeechRecognition.useMemo[defaultOptions]\"], []);\n    // Memoize merged options to prevent infinite re-renders\n    const mergedOptions = (0,react__WEBPACK_IMPORTED_MODULE_0__.useMemo)({\n        \"useSpeechRecognition.useMemo[mergedOptions]\": ()=>({\n                ...defaultOptions,\n                ...options\n            })\n    }[\"useSpeechRecognition.useMemo[mergedOptions]\"], [\n        defaultOptions,\n        options.language,\n        options.continuous,\n        options.interimResults,\n        options.maxAlternatives\n    ]);\n    const [finalTranscript, setFinalTranscript] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)('');\n    const [interimTranscript, setInterimTranscript] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)('');\n    const [isListening, setIsListening] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(false);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(null);\n    const [isSupported, setIsSupported] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(false);\n    const recognitionRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(null);\n    const optionsRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(mergedOptions);\n    const restartTimeoutRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(null);\n    const restartAttemptsRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(0);\n    const isStoppedManuallyRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(false);\n    const MAX_RESTART_ATTEMPTS = 3;\n    const RESTART_DELAY = 1000;\n    // Update options ref when merged options change\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)({\n        \"useSpeechRecognition.useEffect\": ()=>{\n            optionsRef.current = mergedOptions;\n        }\n    }[\"useSpeechRecognition.useEffect\"], [\n        mergedOptions\n    ]);\n    // Initialize speech recognition\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)({\n        \"useSpeechRecognition.useEffect\": ()=>{\n            if (false) {}\n            const SpeechRecognition = window.SpeechRecognition || window.webkitSpeechRecognition;\n            if (!SpeechRecognition) {\n                setIsSupported(false);\n                setError('Speech recognition is not supported in this browser');\n                return;\n            }\n            setIsSupported(true);\n            const recognition = new SpeechRecognition();\n            recognitionRef.current = recognition;\n            // Configure recognition with current merged options\n            recognition.continuous = mergedOptions.continuous;\n            recognition.interimResults = mergedOptions.interimResults;\n            recognition.lang = mergedOptions.language;\n            recognition.maxAlternatives = mergedOptions.maxAlternatives;\n            // Event handlers\n            recognition.onstart = ({\n                \"useSpeechRecognition.useEffect\": ()=>{\n                    setIsListening(true);\n                    setError(null);\n                }\n            })[\"useSpeechRecognition.useEffect\"];\n            recognition.onend = ({\n                \"useSpeechRecognition.useEffect\": ()=>{\n                    console.log('Speech recognition ended');\n                    setIsListening(false);\n                    // Auto-restart if not stopped manually and continuous mode is enabled\n                    if (!isStoppedManuallyRef.current && optionsRef.current.continuous) {\n                        if (restartAttemptsRef.current < MAX_RESTART_ATTEMPTS) {\n                            console.log(\"Attempting to restart recognition (attempt \".concat(restartAttemptsRef.current + 1, \")\"));\n                            restartAttemptsRef.current++;\n                            restartTimeoutRef.current = setTimeout({\n                                \"useSpeechRecognition.useEffect\": ()=>{\n                                    try {\n                                        if (recognitionRef.current && !isStoppedManuallyRef.current) {\n                                            recognitionRef.current.start();\n                                        }\n                                    } catch (err) {\n                                        console.error('Failed to restart recognition:', err);\n                                        setError('Failed to restart speech recognition');\n                                    }\n                                }\n                            }[\"useSpeechRecognition.useEffect\"], RESTART_DELAY);\n                        } else {\n                            setError('Maximum restart attempts reached. Please restart manually.');\n                        }\n                    }\n                }\n            })[\"useSpeechRecognition.useEffect\"];\n            recognition.onerror = ({\n                \"useSpeechRecognition.useEffect\": (event)=>{\n                    console.error('Speech recognition error:', event);\n                    // Handle different error types based on original implementation\n                    switch(event.error){\n                        case 'no-speech':\n                            setError('No speech detected. Still listening...');\n                            break;\n                        case 'audio-capture':\n                            setError('No microphone detected. Please check your microphone.');\n                            setIsListening(false);\n                            break;\n                        case 'not-allowed':\n                            setError('Microphone access denied. Please allow microphone access.');\n                            setIsListening(false);\n                            break;\n                        case 'network':\n                            setError('Network error occurred. Will attempt to reconnect...');\n                            break;\n                        case 'aborted':\n                            console.log('Recognition aborted');\n                            if (!isStoppedManuallyRef.current) {\n                                setError('Recognition was aborted unexpectedly');\n                            }\n                            break;\n                        default:\n                            setError(\"Speech recognition error: \".concat(event.error));\n                            setIsListening(false);\n                    }\n                }\n            })[\"useSpeechRecognition.useEffect\"];\n            recognition.onresult = ({\n                \"useSpeechRecognition.useEffect\": (event)=>{\n                    if (typeof event.results === 'undefined') {\n                        console.log('Undefined results in event:', event);\n                        return;\n                    }\n                    let interim = '';\n                    let final = '';\n                    // Process only new results since last event\n                    for(let i = event.resultIndex; i < event.results.length; i++){\n                        const transcript = event.results[i][0].transcript.trim();\n                        if (event.results[i].isFinal) {\n                            final += (final ? ' ' : '') + transcript;\n                        } else {\n                            interim += (interim ? ' ' : '') + transcript;\n                        }\n                    }\n                    setInterimTranscript(interim);\n                    if (final) {\n                        setFinalTranscript({\n                            \"useSpeechRecognition.useEffect\": (prev)=>prev + (prev ? ' ' : '') + final\n                        }[\"useSpeechRecognition.useEffect\"]);\n                        // Reset restart attempts on successful recognition\n                        restartAttemptsRef.current = 0;\n                    }\n                }\n            })[\"useSpeechRecognition.useEffect\"];\n            return ({\n                \"useSpeechRecognition.useEffect\": ()=>{\n                    if (recognition) {\n                        recognition.stop();\n                    }\n                }\n            })[\"useSpeechRecognition.useEffect\"];\n        }\n    }[\"useSpeechRecognition.useEffect\"], [\n        mergedOptions\n    ]);\n    // Update language when it changes\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)({\n        \"useSpeechRecognition.useEffect\": ()=>{\n            if (recognitionRef.current && mergedOptions.language) {\n                recognitionRef.current.lang = mergedOptions.language;\n            }\n        }\n    }[\"useSpeechRecognition.useEffect\"], [\n        mergedOptions.language\n    ]);\n    const startListening = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useSpeechRecognition.useCallback[startListening]\": ()=>{\n            if (!recognitionRef.current || !isSupported) {\n                setError('Speech recognition is not available');\n                return;\n            }\n            if (isListening) {\n                return;\n            }\n            // Clear any pending restart timeout\n            if (restartTimeoutRef.current) {\n                clearTimeout(restartTimeoutRef.current);\n                restartTimeoutRef.current = null;\n            }\n            isStoppedManuallyRef.current = false;\n            restartAttemptsRef.current = 0;\n            setError(null);\n            try {\n                recognitionRef.current.start();\n                console.log('Speech recognition started');\n            } catch (err) {\n                console.error('Failed to start speech recognition:', err);\n                if (err instanceof Error && err.name === 'InvalidStateError') {\n                    // Recognition is already running, try to stop and restart\n                    try {\n                        recognitionRef.current.stop();\n                        setTimeout({\n                            \"useSpeechRecognition.useCallback[startListening]\": ()=>{\n                                try {\n                                    recognitionRef.current.start();\n                                } catch (retryErr) {\n                                    setError('Failed to start speech recognition after retry');\n                                }\n                            }\n                        }[\"useSpeechRecognition.useCallback[startListening]\"], 100);\n                    } catch (stopErr) {\n                        setError('Failed to restart speech recognition');\n                    }\n                } else {\n                    setError('Failed to start speech recognition');\n                }\n            }\n        }\n    }[\"useSpeechRecognition.useCallback[startListening]\"], [\n        isListening,\n        isSupported\n    ]);\n    const stopListening = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useSpeechRecognition.useCallback[stopListening]\": ()=>{\n            if (!recognitionRef.current) return;\n            isStoppedManuallyRef.current = true;\n            // Clear any pending restart timeout\n            if (restartTimeoutRef.current) {\n                clearTimeout(restartTimeoutRef.current);\n                restartTimeoutRef.current = null;\n            }\n            try {\n                recognitionRef.current.stop();\n                console.log('Speech recognition stopped manually');\n            } catch (err) {\n                console.error('Failed to stop speech recognition:', err);\n                setError('Failed to stop speech recognition');\n            }\n        }\n    }[\"useSpeechRecognition.useCallback[stopListening]\"], []);\n    const resetTranscript = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useSpeechRecognition.useCallback[resetTranscript]\": ()=>{\n            setFinalTranscript('');\n            setInterimTranscript('');\n            setError(null);\n            restartAttemptsRef.current = 0;\n        }\n    }[\"useSpeechRecognition.useCallback[resetTranscript]\"], []);\n    const setLanguage = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useSpeechRecognition.useCallback[setLanguage]\": (language)=>{\n            if (recognitionRef.current) {\n                recognitionRef.current.lang = language;\n                optionsRef.current.language = language;\n            }\n        }\n    }[\"useSpeechRecognition.useCallback[setLanguage]\"], []);\n    // Cleanup on unmount\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)({\n        \"useSpeechRecognition.useEffect\": ()=>{\n            return ({\n                \"useSpeechRecognition.useEffect\": ()=>{\n                    if (restartTimeoutRef.current) {\n                        clearTimeout(restartTimeoutRef.current);\n                    }\n                    if (recognitionRef.current) {\n                        isStoppedManuallyRef.current = true;\n                        try {\n                            recognitionRef.current.stop();\n                        } catch (err) {\n                            console.error('Error stopping recognition on cleanup:', err);\n                        }\n                    }\n                }\n            })[\"useSpeechRecognition.useEffect\"];\n        }\n    }[\"useSpeechRecognition.useEffect\"], []);\n    return {\n        finalTranscript,\n        interimTranscript,\n        isListening,\n        error,\n        isSupported,\n        startListening,\n        stopListening,\n        resetTranscript,\n        setLanguage\n    };\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (useSpeechRecognition);\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/hooks/useSpeechRecognition.ts\n"));

/***/ })

});