/**
 * VideoPlayerWithCaptions Component
 *
 * Main video player component that displays camera stream with caption overlay.
 * Integrates camera access via getUserMedia with real-time caption display.
 */

'use client';

import React, { useRef, useEffect, useState, useCallback } from 'react';
import { useCaption } from '@/contexts/CaptionContext';
import useMediaStream from '@/hooks/useMediaStream';
import CaptionOverlay from './CaptionOverlay';

interface VideoPlayerWithCaptionsProps {
  className?: string;
  showControls?: boolean;
  autoStart?: boolean;
  aspectRatio?: '16:9' | '4:3' | '1:1';
  quality?: 'low' | 'medium' | 'high' | 'ultra';
}

const VideoPlayerWithCaptions: React.FC<VideoPlayerWithCaptionsProps> = ({
  className = '',
  showControls = true,
  autoStart = false,
  aspectRatio = '16:9',
  quality = 'high'
}) => {
  const { state } = useCaption();
  const videoRef = useRef<HTMLVideoElement>(null);
  const [isFullscreen, setIsFullscreen] = useState(false);
  const [showCaptions, setShowCaptions] = useState(true);

  const mediaStream = useMediaStream();

  // Quality settings
  const getQualityConstraints = useCallback(() => {
    const qualitySettings = {
      low: { width: { ideal: 640 }, height: { ideal: 480 }, frameRate: { ideal: 15 } },
      medium: { width: { ideal: 1280 }, height: { ideal: 720 }, frameRate: { ideal: 24 } },
      high: { width: { ideal: 1280 }, height: { ideal: 720 }, frameRate: { ideal: 30 } },
      ultra: { width: { ideal: 1920 }, height: { ideal: 1080 }, frameRate: { ideal: 30 } }
    };
    return qualitySettings[quality];
  }, [quality]);

  // Auto-start camera if requested
  useEffect(() => {
    if (autoStart && mediaStream.isSupported) {
      const startCamera = async () => {
        try {
          await mediaStream.startStream({
            video: getQualityConstraints(),
            audio: true
          });
        } catch (error) {
          console.error('Failed to auto-start camera:', error);
        }
      };
      startCamera();
    }
  }, [autoStart, mediaStream, getQualityConstraints]);

  // Attach stream to video element
  useEffect(() => {
    if (videoRef.current && mediaStream.stream) {
      videoRef.current.srcObject = mediaStream.stream;
    }
  }, [mediaStream.stream]);

  // Handle fullscreen changes
  useEffect(() => {
    const handleFullscreenChange = () => {
      setIsFullscreen(!!document.fullscreenElement);
    };

    document.addEventListener('fullscreenchange', handleFullscreenChange);
    return () => document.removeEventListener('fullscreenchange', handleFullscreenChange);
  }, []);

  // Start camera
  const handleStartCamera = async () => {
    try {
      await mediaStream.startStream({
        video: getQualityConstraints(),
        audio: true
      });
    } catch (error) {
      console.error('Failed to start camera:', error);
    }
  };

  // Stop camera
  const handleStopCamera = () => {
    mediaStream.stopStream();
  };

  // Toggle fullscreen
  const handleToggleFullscreen = async () => {
    if (!videoRef.current) return;

    try {
      if (isFullscreen) {
        await document.exitFullscreen();
      } else {
        await videoRef.current.requestFullscreen();
      }
    } catch (error) {
      console.error('Fullscreen toggle failed:', error);
    }
  };

  // Switch camera device
  const handleDeviceChange = async (deviceId: string) => {
    try {
      await mediaStream.switchVideoDevice(deviceId);
    } catch (error) {
      console.error('Failed to switch camera:', error);
    }
  };

  // Get aspect ratio classes
  const getAspectRatioClass = () => {
    switch (aspectRatio) {
      case '4:3': return 'aspect-[4/3]';
      case '1:1': return 'aspect-square';
      default: return 'aspect-video'; // 16:9
    }
  };

  // Get video devices for selection
  const videoDevices = mediaStream.devices.filter(device => device.kind === 'videoinput');

  if (!mediaStream.isSupported) {
    return (
      <div className={`${getAspectRatioClass()} bg-red-50 border border-red-200 rounded-lg flex items-center justify-center ${className}`}>
        <div className="text-center p-6">
          <div className="text-4xl mb-4">📷</div>
          <h3 className="text-lg font-semibold text-red-800 mb-2">Camera Not Supported</h3>
          <p className="text-red-600 text-sm">
            Your browser doesn&apos;t support camera access. Please use Chrome, Edge, or Firefox.
          </p>
        </div>
      </div>
    );
  }

  return (
    <div className={`relative ${getAspectRatioClass()} bg-black rounded-lg overflow-hidden ${className}`}>
      {/* Video Element */}
      <video
        ref={videoRef}
        autoPlay
        playsInline
        muted
        className="w-full h-full object-cover"
        onLoadedMetadata={() => {
          if (videoRef.current) {
            videoRef.current.play().catch(console.error);
          }
        }}
      />

      {/* Caption Overlay */}
      {showCaptions && (
        <CaptionOverlay
          finalTranscript={state.finalTranscript}
          interimTranscript={state.interimTranscript}
          translatedText={state.translatedText}
          isVisible={showCaptions && (!!state.finalTranscript || !!state.interimTranscript)}
        />
      )}

      {/* Camera not started overlay */}
      {!mediaStream.isStreaming && (
        <div className="absolute inset-0 bg-gray-900 flex items-center justify-center">
          <div className="text-center text-white p-6">
            <div className="text-6xl mb-4">📷</div>
            <h3 className="text-xl font-semibold mb-4">Camera Ready</h3>
            <p className="text-gray-300 mb-6">Click start to begin video streaming with live captions</p>
            <button
              onClick={handleStartCamera}
              className="px-6 py-3 bg-blue-500 hover:bg-blue-600 text-white rounded-lg font-medium transition-colors"
            >
              Start Camera
            </button>
            {mediaStream.error && (
              <div className="mt-4 p-3 bg-red-500 bg-opacity-20 border border-red-500 rounded text-red-200 text-sm">
                {mediaStream.error}
              </div>
            )}
          </div>
        </div>
      )}

      {/* Controls Overlay */}
      {showControls && mediaStream.isStreaming && (
        <div className="absolute bottom-4 left-4 right-4 flex items-center justify-between bg-black bg-opacity-50 rounded-lg p-3">
          {/* Left controls */}
          <div className="flex items-center gap-3">
            <button
              onClick={handleStopCamera}
              className="px-3 py-2 bg-red-500 hover:bg-red-600 text-white rounded text-sm font-medium transition-colors"
            >
              Stop
            </button>

            <button
              onClick={() => setShowCaptions(!showCaptions)}
              className={`px-3 py-2 rounded text-sm font-medium transition-colors ${
                showCaptions
                  ? 'bg-blue-500 hover:bg-blue-600 text-white'
                  : 'bg-gray-600 hover:bg-gray-700 text-gray-200'
              }`}
            >
              Captions
            </button>
          </div>

          {/* Center - Device selection */}
          {videoDevices.length > 1 && (
            <select
              value={mediaStream.selectedVideoDevice}
              onChange={(e) => handleDeviceChange(e.target.value)}
              className="px-2 py-1 bg-gray-800 text-white rounded text-sm border border-gray-600 focus:outline-none focus:ring-2 focus:ring-blue-500"
            >
              {videoDevices.map((device) => (
                <option key={device.deviceId} value={device.deviceId}>
                  {device.label || `Camera ${device.deviceId.slice(0, 8)}`}
                </option>
              ))}
            </select>
          )}

          {/* Right controls */}
          <div className="flex items-center gap-3">
            <button
              onClick={handleToggleFullscreen}
              className="px-3 py-2 bg-gray-600 hover:bg-gray-700 text-white rounded text-sm font-medium transition-colors"
              title={isFullscreen ? 'Exit fullscreen' : 'Enter fullscreen'}
            >
              {isFullscreen ? '⤓' : '⤢'}
            </button>
          </div>
        </div>
      )}

      {/* Status indicators */}
      <div className="absolute top-4 right-4 flex flex-col gap-2">
        {mediaStream.isStreaming && (
          <div className="flex items-center gap-2 bg-green-500 bg-opacity-90 text-white px-3 py-1 rounded-full text-sm">
            <div className="w-2 h-2 bg-white rounded-full animate-pulse"></div>
            Live
          </div>
        )}

        {state.isRecording && (
          <div className="flex items-center gap-2 bg-red-500 bg-opacity-90 text-white px-3 py-1 rounded-full text-sm">
            <div className="w-2 h-2 bg-white rounded-full animate-pulse"></div>
            Recording
          </div>
        )}
      </div>
    </div>
  );
};

export default VideoPlayerWithCaptions;
