"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./src/hooks/useSpeechRecognition.ts":
/*!*******************************************!*\
  !*** ./src/hooks/useSpeechRecognition.ts ***!
  \*******************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n/**\n * useSpeechRecognition Hook\n * \n * React hook for managing speech recognition functionality.\n * Based on the speech recognition implementation from index.html.\n */ \nconst useSpeechRecognition = function() {\n    let options = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : {};\n    // Memoize default options to prevent recreation on every render\n    const defaultOptions = (0,react__WEBPACK_IMPORTED_MODULE_0__.useMemo)({\n        \"useSpeechRecognition.useMemo[defaultOptions]\": ()=>({\n                language: 'en-US',\n                continuous: true,\n                interimResults: true,\n                maxAlternatives: 1\n            })\n    }[\"useSpeechRecognition.useMemo[defaultOptions]\"], []);\n    // Memoize merged options to prevent infinite re-renders\n    const mergedOptions = (0,react__WEBPACK_IMPORTED_MODULE_0__.useMemo)({\n        \"useSpeechRecognition.useMemo[mergedOptions]\": ()=>({\n                ...defaultOptions,\n                ...options\n            })\n    }[\"useSpeechRecognition.useMemo[mergedOptions]\"], [\n        defaultOptions,\n        options.language,\n        options.continuous,\n        options.interimResults,\n        options.maxAlternatives\n    ]);\n    const [finalTranscript, setFinalTranscript] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)('');\n    const [interimTranscript, setInterimTranscript] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)('');\n    const [isListening, setIsListening] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(false);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(null);\n    const [isSupported, setIsSupported] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(false);\n    const recognitionRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(null);\n    const optionsRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(mergedOptions);\n    const restartTimeoutRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(null);\n    const restartAttemptsRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(0);\n    const isStoppedManuallyRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(false);\n    const MAX_RESTART_ATTEMPTS = 3;\n    const RESTART_DELAY = 1000;\n    // Update options ref when merged options change\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)({\n        \"useSpeechRecognition.useEffect\": ()=>{\n            optionsRef.current = mergedOptions;\n        }\n    }[\"useSpeechRecognition.useEffect\"], [\n        mergedOptions\n    ]);\n    // Initialize speech recognition\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)({\n        \"useSpeechRecognition.useEffect\": ()=>{\n            if (false) {}\n            const SpeechRecognition = window.SpeechRecognition || window.webkitSpeechRecognition;\n            if (!SpeechRecognition) {\n                setIsSupported(false);\n                setError('Speech recognition is not supported in this browser');\n                return;\n            }\n            setIsSupported(true);\n            const recognition = new SpeechRecognition();\n            recognitionRef.current = recognition;\n            // Configure recognition with current merged options\n            recognition.continuous = mergedOptions.continuous;\n            recognition.interimResults = mergedOptions.interimResults;\n            recognition.lang = mergedOptions.language;\n            recognition.maxAlternatives = mergedOptions.maxAlternatives;\n            // Event handlers\n            recognition.onstart = ({\n                \"useSpeechRecognition.useEffect\": ()=>{\n                    setIsListening(true);\n                    setError(null);\n                }\n            })[\"useSpeechRecognition.useEffect\"];\n            recognition.onend = ({\n                \"useSpeechRecognition.useEffect\": ()=>{\n                    console.log('Speech recognition ended');\n                    setIsListening(false);\n                    // Auto-restart if not stopped manually and continuous mode is enabled\n                    if (!isStoppedManuallyRef.current && optionsRef.current.continuous) {\n                        if (restartAttemptsRef.current < MAX_RESTART_ATTEMPTS) {\n                            console.log(\"Attempting to restart recognition (attempt \".concat(restartAttemptsRef.current + 1, \")\"));\n                            restartAttemptsRef.current++;\n                            restartTimeoutRef.current = setTimeout({\n                                \"useSpeechRecognition.useEffect\": ()=>{\n                                    try {\n                                        if (recognitionRef.current && !isStoppedManuallyRef.current) {\n                                            recognitionRef.current.start();\n                                        }\n                                    } catch (err) {\n                                        console.error('Failed to restart recognition:', err);\n                                        setError('Failed to restart speech recognition');\n                                    }\n                                }\n                            }[\"useSpeechRecognition.useEffect\"], RESTART_DELAY);\n                        } else {\n                            setError('Maximum restart attempts reached. Please restart manually.');\n                        }\n                    }\n                }\n            })[\"useSpeechRecognition.useEffect\"];\n            recognition.onerror = ({\n                \"useSpeechRecognition.useEffect\": (event)=>{\n                    console.error('Speech recognition error:', event);\n                    // Handle different error types based on original implementation\n                    switch(event.error){\n                        case 'no-speech':\n                            setError('No speech detected. Still listening...');\n                            break;\n                        case 'audio-capture':\n                            setError('No microphone detected. Please check your microphone.');\n                            setIsListening(false);\n                            break;\n                        case 'not-allowed':\n                            setError('Microphone access denied. Please allow microphone access.');\n                            setIsListening(false);\n                            break;\n                        case 'network':\n                            setError('Network error occurred. Will attempt to reconnect...');\n                            break;\n                        case 'aborted':\n                            console.log('Recognition aborted');\n                            if (!isStoppedManuallyRef.current) {\n                                setError('Recognition was aborted unexpectedly');\n                            }\n                            break;\n                        default:\n                            setError(\"Speech recognition error: \".concat(event.error));\n                            setIsListening(false);\n                    }\n                }\n            })[\"useSpeechRecognition.useEffect\"];\n            recognition.onresult = ({\n                \"useSpeechRecognition.useEffect\": (event)=>{\n                    if (typeof event.results === 'undefined') {\n                        console.log('Undefined results in event:', event);\n                        return;\n                    }\n                    let interim = '';\n                    let final = '';\n                    // Process only new results since last event\n                    for(let i = event.resultIndex; i < event.results.length; i++){\n                        const transcript = event.results[i][0].transcript.trim();\n                        if (event.results[i].isFinal) {\n                            final += (final ? ' ' : '') + transcript;\n                        } else {\n                            interim += (interim ? ' ' : '') + transcript;\n                        }\n                    }\n                    setInterimTranscript(interim);\n                    if (final) {\n                        setFinalTranscript({\n                            \"useSpeechRecognition.useEffect\": (prev)=>prev + (prev ? ' ' : '') + final\n                        }[\"useSpeechRecognition.useEffect\"]);\n                        // Reset restart attempts on successful recognition\n                        restartAttemptsRef.current = 0;\n                    }\n                }\n            })[\"useSpeechRecognition.useEffect\"];\n            return ({\n                \"useSpeechRecognition.useEffect\": ()=>{\n                    if (recognition) {\n                        recognition.stop();\n                    }\n                }\n            })[\"useSpeechRecognition.useEffect\"];\n        }\n    }[\"useSpeechRecognition.useEffect\"], [\n        mergedOptions\n    ]);\n    // Update language when it changes\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)({\n        \"useSpeechRecognition.useEffect\": ()=>{\n            if (recognitionRef.current && mergedOptions.language) {\n                recognitionRef.current.lang = mergedOptions.language;\n            }\n        }\n    }[\"useSpeechRecognition.useEffect\"], [\n        mergedOptions.language\n    ]);\n    const startListening = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useSpeechRecognition.useCallback[startListening]\": ()=>{\n            if (!recognitionRef.current || !isSupported) {\n                setError('Speech recognition is not available');\n                return;\n            }\n            if (isListening) {\n                return;\n            }\n            // Clear any pending restart timeout\n            if (restartTimeoutRef.current) {\n                clearTimeout(restartTimeoutRef.current);\n                restartTimeoutRef.current = null;\n            }\n            isStoppedManuallyRef.current = false;\n            restartAttemptsRef.current = 0;\n            setError(null);\n            try {\n                recognitionRef.current.start();\n                console.log('Speech recognition started');\n            } catch (err) {\n                console.error('Failed to start speech recognition:', err);\n                if (err instanceof Error && err.name === 'InvalidStateError') {\n                    // Recognition is already running, try to stop and restart\n                    try {\n                        recognitionRef.current.stop();\n                        setTimeout({\n                            \"useSpeechRecognition.useCallback[startListening]\": ()=>{\n                                try {\n                                    recognitionRef.current.start();\n                                } catch (retryErr) {\n                                    setError('Failed to start speech recognition after retry');\n                                }\n                            }\n                        }[\"useSpeechRecognition.useCallback[startListening]\"], 100);\n                    } catch (stopErr) {\n                        setError('Failed to restart speech recognition');\n                    }\n                } else {\n                    setError('Failed to start speech recognition');\n                }\n            }\n        }\n    }[\"useSpeechRecognition.useCallback[startListening]\"], [\n        isListening,\n        isSupported\n    ]);\n    const stopListening = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useSpeechRecognition.useCallback[stopListening]\": ()=>{\n            if (!recognitionRef.current) return;\n            isStoppedManuallyRef.current = true;\n            // Clear any pending restart timeout\n            if (restartTimeoutRef.current) {\n                clearTimeout(restartTimeoutRef.current);\n                restartTimeoutRef.current = null;\n            }\n            try {\n                recognitionRef.current.stop();\n                console.log('Speech recognition stopped manually');\n            } catch (err) {\n                console.error('Failed to stop speech recognition:', err);\n                setError('Failed to stop speech recognition');\n            }\n        }\n    }[\"useSpeechRecognition.useCallback[stopListening]\"], []);\n    const resetTranscript = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useSpeechRecognition.useCallback[resetTranscript]\": ()=>{\n            setFinalTranscript('');\n            setInterimTranscript('');\n            setError(null);\n            restartAttemptsRef.current = 0;\n        }\n    }[\"useSpeechRecognition.useCallback[resetTranscript]\"], []);\n    const setLanguage = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useSpeechRecognition.useCallback[setLanguage]\": (language)=>{\n            if (recognitionRef.current) {\n                recognitionRef.current.lang = language;\n                optionsRef.current.language = language;\n            }\n        }\n    }[\"useSpeechRecognition.useCallback[setLanguage]\"], []);\n    // Cleanup on unmount\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)({\n        \"useSpeechRecognition.useEffect\": ()=>{\n            return ({\n                \"useSpeechRecognition.useEffect\": ()=>{\n                    if (restartTimeoutRef.current) {\n                        clearTimeout(restartTimeoutRef.current);\n                    }\n                    if (recognitionRef.current) {\n                        isStoppedManuallyRef.current = true;\n                        try {\n                            recognitionRef.current.stop();\n                        } catch (err) {\n                            console.error('Error stopping recognition on cleanup:', err);\n                        }\n                    }\n                }\n            })[\"useSpeechRecognition.useEffect\"];\n        }\n    }[\"useSpeechRecognition.useEffect\"], []);\n    return {\n        finalTranscript,\n        interimTranscript,\n        isListening,\n        error,\n        isSupported,\n        startListening,\n        stopListening,\n        resetTranscript,\n        setLanguage\n    };\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (useSpeechRecognition);\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/hooks/useSpeechRecognition.ts\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/hooks/useTranslation.ts":
/*!*************************************!*\
  !*** ./src/hooks/useTranslation.ts ***!
  \*************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n/**\n * useTranslation Hook\n *\n * React hook for managing translation functionality via Google Cloud Translation API.\n * Based on the translation implementation from translate.html.\n */ \nconst useTranslation = ()=>{\n    const [isTranslating, setIsTranslating] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(false);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(null);\n    const cacheRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)({});\n    const abortControllerRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(null);\n    const requestQueueRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)([]);\n    const isProcessingRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(false);\n    // Load cache from localStorage on mount\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)({\n        \"useTranslation.useEffect\": ()=>{\n            if (true) {\n                try {\n                    const savedCache = localStorage.getItem('caption-ninja-translation-cache');\n                    if (savedCache) {\n                        const cache = JSON.parse(savedCache);\n                        // Filter out expired entries\n                        const now = Date.now();\n                        const validCache = {};\n                        Object.entries(cache).forEach({\n                            \"useTranslation.useEffect\": (param)=>{\n                                let [key, entry] = param;\n                                if (entry.timestamp && now - entry.timestamp < 24 * 60 * 60 * 1000) {\n                                    validCache[key] = entry;\n                                }\n                            }\n                        }[\"useTranslation.useEffect\"]);\n                        cacheRef.current = validCache;\n                    }\n                } catch (error) {\n                    console.warn('Failed to load translation cache:', error);\n                }\n            }\n        }\n    }[\"useTranslation.useEffect\"], []);\n    // Save cache to localStorage when it changes\n    const saveCache = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useTranslation.useCallback[saveCache]\": ()=>{\n            if (true) {\n                try {\n                    localStorage.setItem('caption-ninja-translation-cache', JSON.stringify(cacheRef.current));\n                } catch (error) {\n                    console.warn('Failed to save translation cache:', error);\n                }\n            }\n        }\n    }[\"useTranslation.useCallback[saveCache]\"], []);\n    // Generate cache key\n    const getCacheKey = (text, sourceLanguage, targetLanguage)=>{\n        return \"\".concat(sourceLanguage, \"-\").concat(targetLanguage, \"-\").concat(text);\n    };\n    // Process translation queue to avoid overwhelming the API\n    const processTranslationQueue = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useTranslation.useCallback[processTranslationQueue]\": async ()=>{\n            if (isProcessingRef.current || requestQueueRef.current.length === 0) {\n                return;\n            }\n            isProcessingRef.current = true;\n            setIsTranslating(true);\n            while(requestQueueRef.current.length > 0){\n                const request = requestQueueRef.current.shift();\n                if (!request) continue;\n                try {\n                    const result = await performTranslation(request.text, request.options);\n                    request.resolve(result);\n                } catch (error) {\n                    request.reject(error);\n                }\n                // Small delay between requests to avoid rate limiting\n                if (requestQueueRef.current.length > 0) {\n                    await new Promise({\n                        \"useTranslation.useCallback[processTranslationQueue]\": (resolve)=>setTimeout(resolve, 100)\n                    }[\"useTranslation.useCallback[processTranslationQueue]\"]);\n                }\n            }\n            isProcessingRef.current = false;\n            setIsTranslating(false);\n        }\n    }[\"useTranslation.useCallback[processTranslationQueue]\"], [\n        performTranslation\n    ]);\n    // Actual translation function\n    const performTranslation = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useTranslation.useCallback[performTranslation]\": async (text, options)=>{\n            const { sourceLanguage, targetLanguage, apiKey } = options;\n            // Check cache first\n            const cacheKey = getCacheKey(text, sourceLanguage, targetLanguage);\n            const cachedResult = cacheRef.current[cacheKey];\n            if (cachedResult) {\n                // Check if cache entry is still valid (24 hours)\n                const now = Date.now();\n                if (cachedResult.metadata && now - cachedResult.metadata.timestamp < 24 * 60 * 60 * 1000) {\n                    return {\n                        ...cachedResult,\n                        metadata: {\n                            ...cachedResult.metadata,\n                            cacheHit: true\n                        }\n                    };\n                } else {\n                    // Remove expired cache entry\n                    delete cacheRef.current[cacheKey];\n                }\n            }\n            // Skip translation if source and target are the same\n            if (sourceLanguage === targetLanguage) {\n                const result = {\n                    translatedText: text,\n                    detectedLanguage: sourceLanguage,\n                    confidence: 1.0,\n                    metadata: {\n                        model: 'passthrough',\n                        processingTime: 0,\n                        characterCount: text.length,\n                        wordCount: text.split(/\\s+/).length,\n                        cacheHit: false,\n                        timestamp: Date.now()\n                    }\n                };\n                return result;\n            }\n            setError(null);\n            try {\n                const response = await fetch('/api/translate', {\n                    method: 'POST',\n                    headers: {\n                        'Content-Type': 'application/json'\n                    },\n                    body: JSON.stringify({\n                        text,\n                        sourceLanguage: sourceLanguage === 'auto' ? undefined : sourceLanguage,\n                        targetLanguage,\n                        apiKey\n                    })\n                });\n                if (!response.ok) {\n                    const errorData = await response.json();\n                    throw new Error(errorData.error || \"Translation failed: \".concat(response.status));\n                }\n                const result = await response.json();\n                // Add timestamp to metadata\n                if (result.metadata) {\n                    result.metadata.timestamp = Date.now();\n                }\n                // Cache the result\n                cacheRef.current[cacheKey] = result;\n                // Limit cache size and save to localStorage\n                const cacheKeys = Object.keys(cacheRef.current);\n                if (cacheKeys.length > 100) {\n                    const keysToDelete = cacheKeys.slice(0, cacheKeys.length - 100);\n                    keysToDelete.forEach({\n                        \"useTranslation.useCallback[performTranslation]\": (key)=>{\n                            delete cacheRef.current[key];\n                        }\n                    }[\"useTranslation.useCallback[performTranslation]\"]);\n                }\n                saveCache();\n                return result;\n            } catch (err) {\n                if (err instanceof Error) {\n                    setError(err.message);\n                    throw err;\n                } else {\n                    const error = new Error('Translation failed with unknown error');\n                    setError(error.message);\n                    throw error;\n                }\n            }\n        }\n    }[\"useTranslation.useCallback[performTranslation]\"], [\n        saveCache\n    ]);\n    // Main translation function with queueing\n    const translateText = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useTranslation.useCallback[translateText]\": async (text, options)=>{\n            if (!text.trim()) {\n                return null;\n            }\n            return new Promise({\n                \"useTranslation.useCallback[translateText]\": (resolve, reject)=>{\n                    // Cancel previous requests for the same text\n                    requestQueueRef.current = requestQueueRef.current.filter({\n                        \"useTranslation.useCallback[translateText]\": (req)=>req.text !== text\n                    }[\"useTranslation.useCallback[translateText]\"]);\n                    // Add to queue\n                    requestQueueRef.current.push({\n                        text,\n                        options,\n                        resolve,\n                        reject\n                    });\n                    // Process queue\n                    processTranslationQueue();\n                }\n            }[\"useTranslation.useCallback[translateText]\"]);\n        }\n    }[\"useTranslation.useCallback[translateText]\"], [\n        processTranslationQueue\n    ]);\n    // Clear translation cache\n    const clearCache = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useTranslation.useCallback[clearCache]\": ()=>{\n            cacheRef.current = {};\n            if (true) {\n                localStorage.removeItem('caption-ninja-translation-cache');\n            }\n        }\n    }[\"useTranslation.useCallback[clearCache]\"], []);\n    // Get cache size\n    const getCacheSize = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useTranslation.useCallback[getCacheSize]\": ()=>{\n            return Object.keys(cacheRef.current).length;\n        }\n    }[\"useTranslation.useCallback[getCacheSize]\"], []);\n    // Cancel all pending translations\n    const cancelTranslation = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useTranslation.useCallback[cancelTranslation]\": ()=>{\n            requestQueueRef.current = [];\n            if (abortControllerRef.current) {\n                abortControllerRef.current.abort();\n                abortControllerRef.current = null;\n            }\n            setIsTranslating(false);\n        }\n    }[\"useTranslation.useCallback[cancelTranslation]\"], []);\n    // Get translation statistics\n    const getTranslationStats = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useTranslation.useCallback[getTranslationStats]\": ()=>{\n            const cache = cacheRef.current;\n            const entries = Object.values(cache);\n            const totalTranslations = entries.length;\n            const totalCharacters = entries.reduce({\n                \"useTranslation.useCallback[getTranslationStats].totalCharacters\": (sum, entry)=>{\n                    var _entry_metadata;\n                    return sum + (((_entry_metadata = entry.metadata) === null || _entry_metadata === void 0 ? void 0 : _entry_metadata.characterCount) || 0);\n                }\n            }[\"useTranslation.useCallback[getTranslationStats].totalCharacters\"], 0);\n            const averageProcessingTime = entries.length > 0 ? entries.reduce({\n                \"useTranslation.useCallback[getTranslationStats]\": (sum, entry)=>{\n                    var _entry_metadata;\n                    return sum + (((_entry_metadata = entry.metadata) === null || _entry_metadata === void 0 ? void 0 : _entry_metadata.processingTime) || 0);\n                }\n            }[\"useTranslation.useCallback[getTranslationStats]\"], 0) / entries.length : 0;\n            return {\n                totalTranslations,\n                totalCharacters,\n                averageProcessingTime,\n                cacheSize: totalTranslations,\n                cacheHitRate: 0 // Would need to track this separately\n            };\n        }\n    }[\"useTranslation.useCallback[getTranslationStats]\"], []);\n    return {\n        translateText,\n        isTranslating,\n        error,\n        clearCache,\n        getCacheSize,\n        cancelTranslation,\n        getTranslationStats\n    };\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (useTranslation);\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/hooks/useTranslation.ts\n"));

/***/ })

});