"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./src/hooks/useSpeechRecognition.ts":
/*!*******************************************!*\
  !*** ./src/hooks/useSpeechRecognition.ts ***!
  \*******************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _utils_logger__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/utils/logger */ \"(app-pages-browser)/./src/utils/logger.ts\");\n/**\n * useSpeechRecognition Hook\n * \n * React hook for managing speech recognition functionality.\n * Based on the speech recognition implementation from index.html.\n */ \n\nconst useSpeechRecognition = function() {\n    let options = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : {};\n    // Memoize default options to prevent recreation on every render\n    const defaultOptions = (0,react__WEBPACK_IMPORTED_MODULE_0__.useMemo)({\n        \"useSpeechRecognition.useMemo[defaultOptions]\": ()=>({\n                language: 'en-US',\n                continuous: true,\n                interimResults: true,\n                maxAlternatives: 1\n            })\n    }[\"useSpeechRecognition.useMemo[defaultOptions]\"], []);\n    // Memoize merged options to prevent infinite re-renders\n    const mergedOptions = (0,react__WEBPACK_IMPORTED_MODULE_0__.useMemo)({\n        \"useSpeechRecognition.useMemo[mergedOptions]\": ()=>({\n                ...defaultOptions,\n                ...options\n            })\n    }[\"useSpeechRecognition.useMemo[mergedOptions]\"], [\n        defaultOptions,\n        options.language,\n        options.continuous,\n        options.interimResults,\n        options.maxAlternatives\n    ]);\n    const [finalTranscript, setFinalTranscript] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)('');\n    const [interimTranscript, setInterimTranscript] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)('');\n    const [isListening, setIsListening] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(false);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(null);\n    const [isSupported, setIsSupported] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(false);\n    const [recognitionState, setRecognitionState] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)('idle');\n    const recognitionRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(null);\n    const optionsRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(mergedOptions);\n    const restartTimeoutRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(null);\n    const restartAttemptsRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(0);\n    const isStoppedManuallyRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(false);\n    const MAX_RESTART_ATTEMPTS = 3;\n    const RESTART_DELAY = 1000;\n    // Update options ref when merged options change\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)({\n        \"useSpeechRecognition.useEffect\": ()=>{\n            optionsRef.current = mergedOptions;\n        }\n    }[\"useSpeechRecognition.useEffect\"], [\n        mergedOptions\n    ]);\n    // Initialize speech recognition\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)({\n        \"useSpeechRecognition.useEffect\": ()=>{\n            if (false) {}\n            const SpeechRecognition = window.SpeechRecognition || window.webkitSpeechRecognition;\n            if (!SpeechRecognition) {\n                setIsSupported(false);\n                setError('Speech recognition is not supported in this browser');\n                return;\n            }\n            setIsSupported(true);\n            const recognition = new SpeechRecognition();\n            recognitionRef.current = recognition;\n            // Configure recognition with current merged options\n            recognition.continuous = mergedOptions.continuous;\n            recognition.interimResults = mergedOptions.interimResults;\n            recognition.lang = mergedOptions.language;\n            recognition.maxAlternatives = mergedOptions.maxAlternatives;\n            // Event handlers with state machine\n            recognition.onstart = ({\n                \"useSpeechRecognition.useEffect\": ()=>{\n                    _utils_logger__WEBPACK_IMPORTED_MODULE_1__[\"default\"].speechRecognition.started(mergedOptions.language);\n                    _utils_logger__WEBPACK_IMPORTED_MODULE_1__[\"default\"].speechRecognition.stateChange(recognitionState, 'listening');\n                    setIsListening(true);\n                    setRecognitionState('listening');\n                    setError(null);\n                    // Reset restart attempts on successful start\n                    restartAttemptsRef.current = 0;\n                }\n            })[\"useSpeechRecognition.useEffect\"];\n            recognition.onend = ({\n                \"useSpeechRecognition.useEffect\": ()=>{\n                    console.log('Speech recognition ended');\n                    setIsListening(false);\n                    // Only attempt restart if we're not in stopping state and not stopped manually\n                    if (recognitionState !== 'stopping' && !isStoppedManuallyRef.current && mergedOptions.continuous) {\n                        if (restartAttemptsRef.current < MAX_RESTART_ATTEMPTS) {\n                            console.log(\"Attempting to restart recognition (attempt \".concat(restartAttemptsRef.current + 1, \")\"));\n                            setRecognitionState('restarting');\n                            restartAttemptsRef.current++;\n                            // Use exponential backoff for restart attempts\n                            const backoffDelay = RESTART_DELAY * Math.pow(2, restartAttemptsRef.current - 1);\n                            restartTimeoutRef.current = setTimeout({\n                                \"useSpeechRecognition.useEffect\": ()=>{\n                                    try {\n                                        if (recognitionRef.current && !isStoppedManuallyRef.current && recognitionState !== 'stopping') {\n                                            setRecognitionState('starting');\n                                            recognitionRef.current.start();\n                                        }\n                                    } catch (err) {\n                                        console.error('Failed to restart recognition:', err);\n                                        setError('Failed to restart speech recognition');\n                                        setRecognitionState('error');\n                                    }\n                                }\n                            }[\"useSpeechRecognition.useEffect\"], backoffDelay);\n                        } else {\n                            setError('Maximum restart attempts reached. Please restart manually.');\n                            setRecognitionState('error');\n                        }\n                    } else {\n                        setRecognitionState('idle');\n                    }\n                }\n            })[\"useSpeechRecognition.useEffect\"];\n            recognition.onerror = ({\n                \"useSpeechRecognition.useEffect\": (event)=>{\n                    console.error('Speech recognition error:', event);\n                    // Handle different error types with state machine\n                    switch(event.error){\n                        case 'no-speech':\n                            setError('No speech detected. Still listening...');\n                            break;\n                        case 'audio-capture':\n                            setError('No microphone detected. Please check your microphone.');\n                            setIsListening(false);\n                            setRecognitionState('error');\n                            break;\n                        case 'not-allowed':\n                            setError('Microphone access denied. Please allow microphone access.');\n                            setIsListening(false);\n                            setRecognitionState('error');\n                            break;\n                        case 'network':\n                            setError('Network error occurred. Will attempt to reconnect...');\n                            setRecognitionState('error');\n                            break;\n                        case 'aborted':\n                            console.log('Recognition aborted');\n                            if (!isStoppedManuallyRef.current) {\n                                setError('Recognition was aborted unexpectedly');\n                                setRecognitionState('error');\n                            } else {\n                                setRecognitionState('idle');\n                            }\n                            break;\n                        default:\n                            setError(\"Speech recognition error: \".concat(event.error));\n                            setRecognitionState('error');\n                            setIsListening(false);\n                    }\n                }\n            })[\"useSpeechRecognition.useEffect\"];\n            recognition.onresult = ({\n                \"useSpeechRecognition.useEffect\": (event)=>{\n                    if (typeof event.results === 'undefined') {\n                        console.log('Undefined results in event:', event);\n                        return;\n                    }\n                    let interim = '';\n                    let final = '';\n                    // Process only new results since last event\n                    for(let i = event.resultIndex; i < event.results.length; i++){\n                        const transcript = event.results[i][0].transcript.trim();\n                        if (event.results[i].isFinal) {\n                            final += (final ? ' ' : '') + transcript;\n                        } else {\n                            interim += (interim ? ' ' : '') + transcript;\n                        }\n                    }\n                    setInterimTranscript(interim);\n                    if (final) {\n                        setFinalTranscript({\n                            \"useSpeechRecognition.useEffect\": (prev)=>prev + (prev ? ' ' : '') + final\n                        }[\"useSpeechRecognition.useEffect\"]);\n                        // Reset restart attempts on successful recognition\n                        restartAttemptsRef.current = 0;\n                    }\n                }\n            })[\"useSpeechRecognition.useEffect\"];\n            return ({\n                \"useSpeechRecognition.useEffect\": ()=>{\n                    if (recognition) {\n                        recognition.stop();\n                    }\n                }\n            })[\"useSpeechRecognition.useEffect\"];\n        }\n    }[\"useSpeechRecognition.useEffect\"], [\n        mergedOptions\n    ]);\n    // Update language when it changes\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)({\n        \"useSpeechRecognition.useEffect\": ()=>{\n            if (recognitionRef.current && mergedOptions.language) {\n                recognitionRef.current.lang = mergedOptions.language;\n            }\n        }\n    }[\"useSpeechRecognition.useEffect\"], [\n        mergedOptions.language\n    ]);\n    const startListening = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useSpeechRecognition.useCallback[startListening]\": ()=>{\n            if (!recognitionRef.current || !isSupported) {\n                setError('Speech recognition is not available');\n                setRecognitionState('error');\n                return;\n            }\n            // Prevent starting if already in a transitional state\n            if (recognitionState === 'starting' || recognitionState === 'restarting' || isListening) {\n                return;\n            }\n            // Clear any pending restart timeout\n            if (restartTimeoutRef.current) {\n                clearTimeout(restartTimeoutRef.current);\n                restartTimeoutRef.current = null;\n            }\n            isStoppedManuallyRef.current = false;\n            restartAttemptsRef.current = 0;\n            setError(null);\n            setRecognitionState('starting');\n            try {\n                recognitionRef.current.start();\n                console.log('Speech recognition start requested');\n            } catch (err) {\n                console.error('Failed to start speech recognition:', err);\n                if (err instanceof Error && err.name === 'InvalidStateError') {\n                    // Recognition is already running, try to stop and restart\n                    try {\n                        setRecognitionState('stopping');\n                        recognitionRef.current.stop();\n                        setTimeout({\n                            \"useSpeechRecognition.useCallback[startListening]\": ()=>{\n                                try {\n                                    if (!isStoppedManuallyRef.current) {\n                                        setRecognitionState('starting');\n                                        recognitionRef.current.start();\n                                    }\n                                } catch (retryErr) {\n                                    setError('Failed to start speech recognition after retry');\n                                    setRecognitionState('error');\n                                }\n                            }\n                        }[\"useSpeechRecognition.useCallback[startListening]\"], 100);\n                    } catch (stopErr) {\n                        setError('Failed to restart speech recognition');\n                        setRecognitionState('error');\n                    }\n                } else {\n                    setError('Failed to start speech recognition');\n                    setRecognitionState('error');\n                }\n            }\n        }\n    }[\"useSpeechRecognition.useCallback[startListening]\"], [\n        recognitionState,\n        isListening,\n        isSupported\n    ]);\n    const stopListening = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useSpeechRecognition.useCallback[stopListening]\": ()=>{\n            if (!recognitionRef.current) return;\n            isStoppedManuallyRef.current = true;\n            setRecognitionState('stopping');\n            // Clear any pending restart timeout\n            if (restartTimeoutRef.current) {\n                clearTimeout(restartTimeoutRef.current);\n                restartTimeoutRef.current = null;\n            }\n            try {\n                recognitionRef.current.stop();\n                console.log('Speech recognition stopped manually');\n            } catch (err) {\n                console.error('Failed to stop speech recognition:', err);\n                setError('Failed to stop speech recognition');\n                setRecognitionState('error');\n            }\n        }\n    }[\"useSpeechRecognition.useCallback[stopListening]\"], []);\n    const resetTranscript = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useSpeechRecognition.useCallback[resetTranscript]\": ()=>{\n            setFinalTranscript('');\n            setInterimTranscript('');\n            setError(null);\n            restartAttemptsRef.current = 0;\n        }\n    }[\"useSpeechRecognition.useCallback[resetTranscript]\"], []);\n    const setLanguage = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useSpeechRecognition.useCallback[setLanguage]\": (language)=>{\n            if (recognitionRef.current) {\n                recognitionRef.current.lang = language;\n                optionsRef.current.language = language;\n            }\n        }\n    }[\"useSpeechRecognition.useCallback[setLanguage]\"], []);\n    // Cleanup on unmount\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)({\n        \"useSpeechRecognition.useEffect\": ()=>{\n            return ({\n                \"useSpeechRecognition.useEffect\": ()=>{\n                    if (restartTimeoutRef.current) {\n                        clearTimeout(restartTimeoutRef.current);\n                    }\n                    if (recognitionRef.current) {\n                        isStoppedManuallyRef.current = true;\n                        try {\n                            recognitionRef.current.stop();\n                        } catch (err) {\n                            console.error('Error stopping recognition on cleanup:', err);\n                        }\n                    }\n                }\n            })[\"useSpeechRecognition.useEffect\"];\n        }\n    }[\"useSpeechRecognition.useEffect\"], []);\n    return {\n        finalTranscript,\n        interimTranscript,\n        isListening,\n        error,\n        isSupported,\n        recognitionState,\n        startListening,\n        stopListening,\n        resetTranscript,\n        setLanguage\n    };\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (useSpeechRecognition);\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/hooks/useSpeechRecognition.ts\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/utils/logger.ts":
/*!*****************************!*\
  !*** ./src/utils/logger.ts ***!
  \*****************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__),\n/* harmony export */   performanceLogger: () => (/* binding */ performanceLogger),\n/* harmony export */   useLogger: () => (/* binding */ useLogger)\n/* harmony export */ });\n/**\n * Logger Utility\n * \n * Structured logging system for tracking state changes and identifying issues.\n * Provides different log levels and context-aware logging.\n */ class Logger {\n    formatTimestamp() {\n        return new Date().toISOString();\n    }\n    shouldLog(level) {\n        if (this.isDevelopment) return true;\n        // In production, only log warnings and errors\n        return level === 'warn' || level === 'error';\n    }\n    addLog(entry) {\n        this.logs.push(entry);\n        // Keep only the last maxLogs entries\n        if (this.logs.length > this.maxLogs) {\n            this.logs = this.logs.slice(-this.maxLogs);\n        }\n        // Save to localStorage for persistence\n        if (true) {\n            try {\n                const recentLogs = this.logs.slice(-100); // Keep last 100 in localStorage\n                localStorage.setItem('caption-ninja-logs', JSON.stringify(recentLogs));\n            } catch (error) {\n                console.warn('Failed to save logs to localStorage:', error);\n            }\n        }\n    }\n    log(level, category, message, data) {\n        if (!this.shouldLog(level)) return;\n        const entry = {\n            timestamp: this.formatTimestamp(),\n            level,\n            category,\n            message,\n            data,\n            sessionId: this.getSessionId()\n        };\n        this.addLog(entry);\n        // Console output with appropriate method\n        const consoleMethod = level === 'debug' ? 'log' : level;\n        const prefix = \"[\".concat(entry.timestamp, \"] [\").concat(level.toUpperCase(), \"] [\").concat(category, \"]\");\n        if (data) {\n            console[consoleMethod](prefix, message, data);\n        } else {\n            console[consoleMethod](prefix, message);\n        }\n    }\n    getSessionId() {\n        if (true) {\n            let sessionId = sessionStorage.getItem('caption-ninja-session-id');\n            if (!sessionId) {\n                sessionId = \"session-\".concat(Date.now(), \"-\").concat(Math.random().toString(36).substr(2, 9));\n                sessionStorage.setItem('caption-ninja-session-id', sessionId);\n            }\n            return sessionId;\n        }\n        return 'server-session';\n    }\n    // Public logging methods\n    debug(category, message, data) {\n        this.log('debug', category, message, data);\n    }\n    info(category, message, data) {\n        this.log('info', category, message, data);\n    }\n    warn(category, message, data) {\n        this.log('warn', category, message, data);\n    }\n    error(category, message, data) {\n        this.log('error', category, message, data);\n    }\n    // Utility methods\n    getLogs(level, category) {\n        let filteredLogs = this.logs;\n        if (level) {\n            filteredLogs = filteredLogs.filter((log)=>log.level === level);\n        }\n        if (category) {\n            filteredLogs = filteredLogs.filter((log)=>log.category === category);\n        }\n        return filteredLogs;\n    }\n    getRecentLogs() {\n        let count = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : 50;\n        return this.logs.slice(-count);\n    }\n    clearLogs() {\n        this.logs = [];\n        if (true) {\n            localStorage.removeItem('caption-ninja-logs');\n        }\n    }\n    exportLogs() {\n        return JSON.stringify(this.logs, null, 2);\n    }\n    // Load logs from localStorage on initialization\n    loadPersistedLogs() {\n        if (true) {\n            try {\n                const savedLogs = localStorage.getItem('caption-ninja-logs');\n                if (savedLogs) {\n                    const logs = JSON.parse(savedLogs);\n                    if (Array.isArray(logs)) {\n                        this.logs = logs;\n                    }\n                }\n            } catch (error) {\n                console.warn('Failed to load persisted logs:', error);\n            }\n        }\n    }\n    constructor(){\n        this.logs = [];\n        this.maxLogs = 1000; // Keep last 1000 logs in memory\n        this.isDevelopment = \"development\" === 'development';\n        // Specific logging methods for different components\n        this.speechRecognition = {\n            started: (language)=>this.info('SpeechRecognition', \"Started recognition for language: \".concat(language)),\n            stopped: ()=>this.info('SpeechRecognition', 'Stopped recognition'),\n            result: (transcript, isFinal)=>this.debug('SpeechRecognition', \"Result: \".concat(isFinal ? 'Final' : 'Interim'), {\n                    transcript\n                }),\n            error: (error)=>this.error('SpeechRecognition', \"Recognition error: \".concat(error)),\n            restart: (attempt)=>this.warn('SpeechRecognition', \"Restart attempt \".concat(attempt)),\n            stateChange: (from, to)=>this.debug('SpeechRecognition', \"State change: \".concat(from, \" -> \").concat(to))\n        };\n        this.translation = {\n            started: (text, from, to)=>this.info('Translation', \"Started translation: \".concat(from, \" -> \").concat(to), {\n                    text: text.substring(0, 100)\n                }),\n            completed: (originalText, translatedText)=>this.info('Translation', 'Translation completed', {\n                    originalText: originalText.substring(0, 50),\n                    translatedText: translatedText.substring(0, 50)\n                }),\n            error: (error, text)=>this.error('Translation', \"Translation error: \".concat(error), {\n                    text\n                }),\n            cached: (text)=>this.debug('Translation', 'Used cached translation', {\n                    text: text.substring(0, 50)\n                }),\n            apiCall: (endpoint, duration)=>this.debug('Translation', \"API call to \".concat(endpoint, \" took \").concat(duration, \"ms\"))\n        };\n        this.mediaStream = {\n            started: (constraints)=>this.info('MediaStream', 'Media stream started', {\n                    constraints\n                }),\n            stopped: ()=>this.info('MediaStream', 'Media stream stopped'),\n            error: (error)=>this.error('MediaStream', \"Media stream error: \".concat(error)),\n            deviceChange: (type, deviceId)=>this.info('MediaStream', \"Device changed: \".concat(type), {\n                    deviceId\n                }),\n            permissionDenied: ()=>this.warn('MediaStream', 'Media permission denied'),\n            fallback: (attempt)=>this.warn('MediaStream', \"Using fallback constraints (attempt \".concat(attempt, \")\"))\n        };\n        this.context = {\n            stateUpdate: (action, payload)=>this.debug('Context', \"State update: \".concat(action), payload),\n            rerender: (component, reason)=>this.debug('Context', \"Component re-render: \".concat(component), {\n                    reason\n                }),\n            error: (component, error)=>this.error('Context', \"Context error in \".concat(component, \": \").concat(error))\n        };\n    }\n}\n// Create singleton instance\nconst logger = new Logger();\n// Load persisted logs on initialization\nif (true) {\n    logger.loadPersistedLogs();\n}\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (logger);\n// React hook for using logger in components\nconst useLogger = ()=>{\n    return logger;\n};\n// Performance logging utility\nconst performanceLogger = {\n    start: (operation)=>{\n        const startTime = performance.now();\n        logger.debug('Performance', \"Started: \".concat(operation));\n        return ()=>{\n            const duration = performance.now() - startTime;\n            logger.info('Performance', \"Completed: \".concat(operation), {\n                duration: \"\".concat(duration.toFixed(2), \"ms\")\n            });\n        };\n    },\n    measure: async (operation, fn)=>{\n        const endTimer = performanceLogger.start(operation);\n        try {\n            const result = await fn();\n            endTimer();\n            return result;\n        } catch (error) {\n            endTimer();\n            logger.error('Performance', \"Failed: \".concat(operation), {\n                error\n            });\n            throw error;\n        }\n    }\n};\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/utils/logger.ts\n"));

/***/ })

});