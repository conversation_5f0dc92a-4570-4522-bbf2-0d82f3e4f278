"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./src/hooks/useTranslation.ts":
/*!*************************************!*\
  !*** ./src/hooks/useTranslation.ts ***!
  \*************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n/**\n * useTranslation Hook\n *\n * React hook for managing translation functionality via Google Cloud Translation API.\n * Based on the translation implementation from translate.html.\n */ \nconst useTranslation = ()=>{\n    const [isTranslating, setIsTranslating] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(false);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(null);\n    const cacheRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)({});\n    const abortControllerRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(null);\n    const requestQueueRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)([]);\n    const isProcessingRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(false);\n    // Load cache from localStorage on mount\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)({\n        \"useTranslation.useEffect\": ()=>{\n            if (true) {\n                try {\n                    const savedCache = localStorage.getItem('caption-ninja-translation-cache');\n                    if (savedCache) {\n                        const cache = JSON.parse(savedCache);\n                        // Filter out expired entries\n                        const now = Date.now();\n                        const validCache = {};\n                        Object.entries(cache).forEach({\n                            \"useTranslation.useEffect\": (param)=>{\n                                let [key, entry] = param;\n                                if (entry.timestamp && now - entry.timestamp < 24 * 60 * 60 * 1000) {\n                                    validCache[key] = entry;\n                                }\n                            }\n                        }[\"useTranslation.useEffect\"]);\n                        cacheRef.current = validCache;\n                    }\n                } catch (error) {\n                    console.warn('Failed to load translation cache:', error);\n                }\n            }\n        }\n    }[\"useTranslation.useEffect\"], []);\n    // Save cache to localStorage when it changes\n    const saveCache = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useTranslation.useCallback[saveCache]\": ()=>{\n            if (true) {\n                try {\n                    localStorage.setItem('caption-ninja-translation-cache', JSON.stringify(cacheRef.current));\n                } catch (error) {\n                    console.warn('Failed to save translation cache:', error);\n                }\n            }\n        }\n    }[\"useTranslation.useCallback[saveCache]\"], []);\n    // Generate cache key\n    const getCacheKey = (text, sourceLanguage, targetLanguage)=>{\n        return \"\".concat(sourceLanguage, \"-\").concat(targetLanguage, \"-\").concat(text);\n    };\n    // Actual translation function - moved before processTranslationQueue to fix hoisting issue\n    const performTranslation = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useTranslation.useCallback[performTranslation]\": async (text, options)=>{\n            const { sourceLanguage, targetLanguage, apiKey } = options;\n            // Check cache first\n            const cacheKey = getCacheKey(text, sourceLanguage, targetLanguage);\n            const cachedResult = cacheRef.current[cacheKey];\n            if (cachedResult) {\n                // Check if cache entry is still valid (24 hours)\n                const now = Date.now();\n                if (cachedResult.metadata && now - cachedResult.metadata.timestamp < 24 * 60 * 60 * 1000) {\n                    return {\n                        ...cachedResult,\n                        metadata: {\n                            ...cachedResult.metadata,\n                            cacheHit: true\n                        }\n                    };\n                } else {\n                    // Remove expired cache entry\n                    delete cacheRef.current[cacheKey];\n                }\n            }\n            // Skip translation if source and target are the same\n            if (sourceLanguage === targetLanguage) {\n                const result = {\n                    translatedText: text,\n                    detectedLanguage: sourceLanguage,\n                    confidence: 1.0,\n                    metadata: {\n                        model: 'passthrough',\n                        processingTime: 0,\n                        characterCount: text.length,\n                        wordCount: text.split(/\\s+/).length,\n                        cacheHit: false,\n                        timestamp: Date.now()\n                    }\n                };\n                return result;\n            }\n            setError(null);\n            try {\n                const response = await fetch('/api/translate', {\n                    method: 'POST',\n                    headers: {\n                        'Content-Type': 'application/json'\n                    },\n                    body: JSON.stringify({\n                        text,\n                        sourceLanguage: sourceLanguage === 'auto' ? undefined : sourceLanguage,\n                        targetLanguage,\n                        apiKey\n                    })\n                });\n                if (!response.ok) {\n                    const errorData = await response.json();\n                    throw new Error(errorData.error || \"Translation failed: \".concat(response.status));\n                }\n                const result = await response.json();\n                // Add timestamp to metadata\n                if (result.metadata) {\n                    result.metadata.timestamp = Date.now();\n                }\n                // Cache the result\n                cacheRef.current[cacheKey] = result;\n                // Limit cache size and save to localStorage\n                const cacheKeys = Object.keys(cacheRef.current);\n                if (cacheKeys.length > 100) {\n                    const keysToDelete = cacheKeys.slice(0, cacheKeys.length - 100);\n                    keysToDelete.forEach({\n                        \"useTranslation.useCallback[performTranslation]\": (key)=>{\n                            delete cacheRef.current[key];\n                        }\n                    }[\"useTranslation.useCallback[performTranslation]\"]);\n                }\n                saveCache();\n                return result;\n            } catch (err) {\n                if (err instanceof Error) {\n                    setError(err.message);\n                    throw err;\n                } else {\n                    const error = new Error('Translation failed with unknown error');\n                    setError(error.message);\n                    throw error;\n                }\n            }\n        }\n    }[\"useTranslation.useCallback[performTranslation]\"], [\n        saveCache\n    ]);\n    // Process translation queue to avoid overwhelming the API\n    const processTranslationQueue = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useTranslation.useCallback[processTranslationQueue]\": async ()=>{\n            if (isProcessingRef.current || requestQueueRef.current.length === 0) {\n                return;\n            }\n            isProcessingRef.current = true;\n            setIsTranslating(true);\n            while(requestQueueRef.current.length > 0){\n                const request = requestQueueRef.current.shift();\n                if (!request) continue;\n                try {\n                    const result = await performTranslation(request.text, request.options);\n                    request.resolve(result);\n                } catch (error) {\n                    request.reject(error);\n                }\n                // Small delay between requests to avoid rate limiting\n                if (requestQueueRef.current.length > 0) {\n                    await new Promise({\n                        \"useTranslation.useCallback[processTranslationQueue]\": (resolve)=>setTimeout(resolve, 100)\n                    }[\"useTranslation.useCallback[processTranslationQueue]\"]);\n                }\n            }\n            isProcessingRef.current = false;\n            setIsTranslating(false);\n        }\n    }[\"useTranslation.useCallback[processTranslationQueue]\"], [\n        performTranslation\n    ]);\n    // Main translation function with queueing\n    const translateText = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useTranslation.useCallback[translateText]\": async (text, options)=>{\n            if (!text.trim()) {\n                return null;\n            }\n            return new Promise({\n                \"useTranslation.useCallback[translateText]\": (resolve, reject)=>{\n                    // Cancel previous requests for the same text\n                    requestQueueRef.current = requestQueueRef.current.filter({\n                        \"useTranslation.useCallback[translateText]\": (req)=>req.text !== text\n                    }[\"useTranslation.useCallback[translateText]\"]);\n                    // Add to queue\n                    requestQueueRef.current.push({\n                        text,\n                        options,\n                        resolve,\n                        reject\n                    });\n                    // Process queue\n                    processTranslationQueue();\n                }\n            }[\"useTranslation.useCallback[translateText]\"]);\n        }\n    }[\"useTranslation.useCallback[translateText]\"], [\n        processTranslationQueue\n    ]);\n    // Clear translation cache\n    const clearCache = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useTranslation.useCallback[clearCache]\": ()=>{\n            cacheRef.current = {};\n            if (true) {\n                localStorage.removeItem('caption-ninja-translation-cache');\n            }\n        }\n    }[\"useTranslation.useCallback[clearCache]\"], []);\n    // Get cache size\n    const getCacheSize = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useTranslation.useCallback[getCacheSize]\": ()=>{\n            return Object.keys(cacheRef.current).length;\n        }\n    }[\"useTranslation.useCallback[getCacheSize]\"], []);\n    // Cancel all pending translations\n    const cancelTranslation = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useTranslation.useCallback[cancelTranslation]\": ()=>{\n            requestQueueRef.current = [];\n            if (abortControllerRef.current) {\n                abortControllerRef.current.abort();\n                abortControllerRef.current = null;\n            }\n            setIsTranslating(false);\n        }\n    }[\"useTranslation.useCallback[cancelTranslation]\"], []);\n    // Get translation statistics\n    const getTranslationStats = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useTranslation.useCallback[getTranslationStats]\": ()=>{\n            const cache = cacheRef.current;\n            const entries = Object.values(cache);\n            const totalTranslations = entries.length;\n            const totalCharacters = entries.reduce({\n                \"useTranslation.useCallback[getTranslationStats].totalCharacters\": (sum, entry)=>{\n                    var _entry_metadata;\n                    return sum + (((_entry_metadata = entry.metadata) === null || _entry_metadata === void 0 ? void 0 : _entry_metadata.characterCount) || 0);\n                }\n            }[\"useTranslation.useCallback[getTranslationStats].totalCharacters\"], 0);\n            const averageProcessingTime = entries.length > 0 ? entries.reduce({\n                \"useTranslation.useCallback[getTranslationStats]\": (sum, entry)=>{\n                    var _entry_metadata;\n                    return sum + (((_entry_metadata = entry.metadata) === null || _entry_metadata === void 0 ? void 0 : _entry_metadata.processingTime) || 0);\n                }\n            }[\"useTranslation.useCallback[getTranslationStats]\"], 0) / entries.length : 0;\n            return {\n                totalTranslations,\n                totalCharacters,\n                averageProcessingTime,\n                cacheSize: totalTranslations,\n                cacheHitRate: 0 // Would need to track this separately\n            };\n        }\n    }[\"useTranslation.useCallback[getTranslationStats]\"], []);\n    return {\n        translateText,\n        isTranslating,\n        error,\n        clearCache,\n        getCacheSize,\n        cancelTranslation,\n        getTranslationStats\n    };\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (useTranslation);\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/hooks/useTranslation.ts\n"));

/***/ })

});