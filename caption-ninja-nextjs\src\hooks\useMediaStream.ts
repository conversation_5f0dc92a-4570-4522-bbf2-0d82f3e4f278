/**
 * useMediaStream Hook
 * 
 * React hook for managing camera and microphone access via getUserMedia.
 * Provides camera device selection and stream management.
 */

import { useState, useRef, useCallback, useEffect } from 'react';

interface MediaStreamOptions {
  video: boolean | MediaTrackConstraints;
  audio: boolean | MediaTrackConstraints;
}

interface MediaDevice {
  deviceId: string;
  label: string;
  kind: MediaDeviceKind;
}

interface MediaStreamHook {
  stream: MediaStream | null;
  isStreaming: boolean;
  error: string | null;
  devices: MediaDevice[];
  selectedVideoDevice: string;
  selectedAudioDevice: string;
  startStream: (options?: Partial<MediaStreamOptions>) => Promise<void>;
  stopStream: () => void;
  switchVideoDevice: (deviceId: string) => Promise<void>;
  switchAudioDevice: (deviceId: string) => Promise<void>;
  refreshDevices: () => Promise<void>;
  isSupported: boolean;
}

const useMediaStream = (): MediaStreamHook => {
  const [stream, setStream] = useState<MediaStream | null>(null);
  const [isStreaming, setIsStreaming] = useState<boolean>(false);
  const [error, setError] = useState<string | null>(null);
  const [devices, setDevices] = useState<MediaDevice[]>([]);
  const [selectedVideoDevice, setSelectedVideoDevice] = useState<string>('');
  const [selectedAudioDevice, setSelectedAudioDevice] = useState<string>('');
  const [isSupported, setIsSupported] = useState<boolean>(false);

  const streamRef = useRef<MediaStream | null>(null);

  // Refresh available media devices
  const refreshDevices = useCallback(async () => {
    if (!isSupported) return;

    try {
      const deviceList = await navigator.mediaDevices.enumerateDevices();
      const mediaDevices: MediaDevice[] = deviceList.map(device => ({
        deviceId: device.deviceId,
        label: device.label || `${device.kind} ${device.deviceId.slice(0, 8)}`,
        kind: device.kind
      }));

      setDevices(mediaDevices);

      // Set default devices if none selected (only on first load)
      if (!selectedVideoDevice && mediaDevices.length > 0) {
        const videoDevice = mediaDevices.find(d => d.kind === 'videoinput');
        if (videoDevice) {
          setSelectedVideoDevice(videoDevice.deviceId);
        }
      }

      if (!selectedAudioDevice && mediaDevices.length > 0) {
        const audioDevice = mediaDevices.find(d => d.kind === 'audioinput');
        if (audioDevice) {
          setSelectedAudioDevice(audioDevice.deviceId);
        }
      }
    } catch (err) {
      console.error('Failed to enumerate media devices:', err);
      setError('Failed to enumerate media devices');
    }
  }, [isSupported]); // Remove selectedVideoDevice and selectedAudioDevice from dependencies

  // Check for getUserMedia support
  useEffect(() => {
    if (typeof window !== 'undefined' && navigator.mediaDevices && typeof navigator.mediaDevices.getUserMedia === 'function') {
      setIsSupported(true);
    } else {
      setIsSupported(false);
      setError('Media devices are not supported in this browser');
    }
  }, []);

  // Refresh devices when supported status changes
  useEffect(() => {
    if (isSupported) {
      refreshDevices();
    }
  }, [isSupported, refreshDevices]);



  // Start media stream
  const startStream = useCallback(async (options: Partial<MediaStreamOptions> = {}) => {
    if (!isSupported) {
      setError('Media devices are not supported');
      return;
    }

    if (isStreaming) {
      return;
    }

    const defaultOptions: MediaStreamOptions = {
      video: {
        deviceId: selectedVideoDevice ? { exact: selectedVideoDevice } : undefined,
        width: { ideal: 1280 },
        height: { ideal: 720 },
        frameRate: { ideal: 30 }
      },
      audio: {
        deviceId: selectedAudioDevice ? { exact: selectedAudioDevice } : undefined,
        echoCancellation: true,
        noiseSuppression: true,
        autoGainControl: true
      }
    };

    const streamOptions = { ...defaultOptions, ...options };

    try {
      setError(null);
      const mediaStream = await navigator.mediaDevices.getUserMedia(streamOptions);
      
      streamRef.current = mediaStream;
      setStream(mediaStream);
      setIsStreaming(true);

      // Refresh devices after getting permission (labels will be available)
      await refreshDevices();
    } catch (err) {
      const error = err as Error;
      console.error('Failed to access media devices:', err);

      // Provide more specific error messages
      let errorMessage = 'Failed to access media devices';
      if (error.name === 'NotAllowedError') {
        errorMessage = 'Camera/microphone access denied. Please allow permissions and try again.';
      } else if (error.name === 'NotFoundError') {
        errorMessage = 'No camera or microphone found. Please connect a device and try again.';
      } else if (error.name === 'NotReadableError') {
        errorMessage = 'Camera/microphone is already in use by another application.';
      } else if (error.name === 'OverconstrainedError') {
        errorMessage = 'Camera/microphone constraints cannot be satisfied. Try different settings.';
      } else if (error.message) {
        errorMessage = `Media access error: ${error.message}`;
      }

      setError(errorMessage);
      setIsStreaming(false);
    }
  }, [isSupported, isStreaming, selectedVideoDevice, selectedAudioDevice, refreshDevices]);

  // Stop media stream
  const stopStream = useCallback(() => {
    if (streamRef.current) {
      streamRef.current.getTracks().forEach(track => {
        track.stop();
      });
      streamRef.current = null;
      setStream(null);
      setIsStreaming(false);
    }
  }, []);

  // Switch video device
  const switchVideoDevice = useCallback(async (deviceId: string) => {
    setSelectedVideoDevice(deviceId);
    
    if (isStreaming) {
      // Stop current stream and start with new device
      stopStream();
      await startStream({
        video: {
          deviceId: { exact: deviceId },
          width: { ideal: 1280 },
          height: { ideal: 720 },
          frameRate: { ideal: 30 }
        },
        audio: {
          deviceId: selectedAudioDevice ? { exact: selectedAudioDevice } : undefined,
          echoCancellation: true,
          noiseSuppression: true,
          autoGainControl: true
        }
      });
    }
  }, [isStreaming, selectedAudioDevice, stopStream, startStream]);

  // Switch audio device
  const switchAudioDevice = useCallback(async (deviceId: string) => {
    setSelectedAudioDevice(deviceId);
    
    if (isStreaming) {
      // Stop current stream and start with new device
      stopStream();
      await startStream({
        video: {
          deviceId: selectedVideoDevice ? { exact: selectedVideoDevice } : undefined,
          width: { ideal: 1280 },
          height: { ideal: 720 },
          frameRate: { ideal: 30 }
        },
        audio: {
          deviceId: { exact: deviceId },
          echoCancellation: true,
          noiseSuppression: true,
          autoGainControl: true
        }
      });
    }
  }, [isStreaming, selectedVideoDevice, stopStream, startStream]);

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      stopStream();
    };
  }, [stopStream]);

  return {
    stream,
    isStreaming,
    error,
    devices,
    selectedVideoDevice,
    selectedAudioDevice,
    startStream,
    stopStream,
    switchVideoDevice,
    switchAudioDevice,
    refreshDevices,
    isSupported
  };
};

export default useMediaStream;
