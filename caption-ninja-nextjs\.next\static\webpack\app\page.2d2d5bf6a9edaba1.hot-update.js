"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./src/hooks/useTranslation.ts":
/*!*************************************!*\
  !*** ./src/hooks/useTranslation.ts ***!
  \*************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n/**\n * useTranslation Hook\n *\n * React hook for managing translation functionality via Google Cloud Translation API.\n * Based on the translation implementation from translate.html.\n */ \nconst useTranslation = ()=>{\n    const [isTranslating, setIsTranslating] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(false);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(null);\n    const cacheRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)({});\n    const abortControllerRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(null);\n    const requestQueueRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)([]);\n    const isProcessingRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(false);\n    // Load cache from localStorage on mount\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)({\n        \"useTranslation.useEffect\": ()=>{\n            if (true) {\n                try {\n                    const savedCache = localStorage.getItem('caption-ninja-translation-cache');\n                    if (savedCache) {\n                        const cache = JSON.parse(savedCache);\n                        // Filter out expired entries\n                        const now = Date.now();\n                        const validCache = {};\n                        Object.entries(cache).forEach({\n                            \"useTranslation.useEffect\": (param)=>{\n                                let [key, entry] = param;\n                                if (entry.timestamp && now - entry.timestamp < 24 * 60 * 60 * 1000) {\n                                    validCache[key] = entry;\n                                }\n                            }\n                        }[\"useTranslation.useEffect\"]);\n                        cacheRef.current = validCache;\n                    }\n                } catch (error) {\n                    console.warn('Failed to load translation cache:', error);\n                }\n            }\n        }\n    }[\"useTranslation.useEffect\"], []);\n    // Save cache to localStorage when it changes\n    const saveCache = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useTranslation.useCallback[saveCache]\": ()=>{\n            if (true) {\n                try {\n                    localStorage.setItem('caption-ninja-translation-cache', JSON.stringify(cacheRef.current));\n                } catch (error) {\n                    console.warn('Failed to save translation cache:', error);\n                }\n            }\n        }\n    }[\"useTranslation.useCallback[saveCache]\"], []);\n    // Generate cache key\n    const getCacheKey = (text, sourceLanguage, targetLanguage)=>{\n        return \"\".concat(sourceLanguage, \"-\").concat(targetLanguage, \"-\").concat(text);\n    };\n    // Actual translation function - moved before processTranslationQueue to fix hoisting issue\n    const performTranslation = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useTranslation.useCallback[performTranslation]\": async (text, options)=>{\n            const { sourceLanguage, targetLanguage, apiKey } = options;\n            // Check cache first\n            const cacheKey = getCacheKey(text, sourceLanguage, targetLanguage);\n            const cachedResult = cacheRef.current[cacheKey];\n            if (cachedResult) {\n                // Check if cache entry is still valid (24 hours)\n                const now = Date.now();\n                if (cachedResult.metadata && now - cachedResult.metadata.timestamp < 24 * 60 * 60 * 1000) {\n                    return {\n                        ...cachedResult,\n                        metadata: {\n                            ...cachedResult.metadata,\n                            cacheHit: true\n                        }\n                    };\n                } else {\n                    // Remove expired cache entry\n                    delete cacheRef.current[cacheKey];\n                }\n            }\n            // Skip translation if source and target are the same\n            if (sourceLanguage === targetLanguage) {\n                const result = {\n                    translatedText: text,\n                    detectedLanguage: sourceLanguage,\n                    confidence: 1.0,\n                    metadata: {\n                        model: 'passthrough',\n                        processingTime: 0,\n                        characterCount: text.length,\n                        wordCount: text.split(/\\s+/).length,\n                        cacheHit: false,\n                        timestamp: Date.now()\n                    }\n                };\n                return result;\n            }\n            setError(null);\n            try {\n                const response = await fetch('/api/translate', {\n                    method: 'POST',\n                    headers: {\n                        'Content-Type': 'application/json'\n                    },\n                    body: JSON.stringify({\n                        text,\n                        sourceLanguage: sourceLanguage === 'auto' ? undefined : sourceLanguage,\n                        targetLanguage,\n                        apiKey\n                    })\n                });\n                if (!response.ok) {\n                    const errorData = await response.json();\n                    throw new Error(errorData.error || \"Translation failed: \".concat(response.status));\n                }\n                const result = await response.json();\n                // Add timestamp to metadata\n                if (result.metadata) {\n                    result.metadata.timestamp = Date.now();\n                }\n                // Cache the result\n                cacheRef.current[cacheKey] = result;\n                // Limit cache size and save to localStorage\n                const cacheKeys = Object.keys(cacheRef.current);\n                if (cacheKeys.length > 100) {\n                    const keysToDelete = cacheKeys.slice(0, cacheKeys.length - 100);\n                    keysToDelete.forEach({\n                        \"useTranslation.useCallback[performTranslation]\": (key)=>{\n                            delete cacheRef.current[key];\n                        }\n                    }[\"useTranslation.useCallback[performTranslation]\"]);\n                }\n                saveCache();\n                return result;\n            } catch (err) {\n                if (err instanceof Error) {\n                    setError(err.message);\n                    throw err;\n                } else {\n                    const error = new Error('Translation failed with unknown error');\n                    setError(error.message);\n                    throw error;\n                }\n            }\n        }\n    }[\"useTranslation.useCallback[performTranslation]\"], [\n        saveCache\n    ]);\n    // Main translation function with queueing\n    const translateText = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useTranslation.useCallback[translateText]\": async (text, options)=>{\n            if (!text.trim()) {\n                return null;\n            }\n            return new Promise({\n                \"useTranslation.useCallback[translateText]\": (resolve, reject)=>{\n                    // Cancel previous requests for the same text\n                    requestQueueRef.current = requestQueueRef.current.filter({\n                        \"useTranslation.useCallback[translateText]\": (req)=>req.text !== text\n                    }[\"useTranslation.useCallback[translateText]\"]);\n                    // Add to queue\n                    requestQueueRef.current.push({\n                        text,\n                        options,\n                        resolve,\n                        reject\n                    });\n                    // Process queue\n                    processTranslationQueue();\n                }\n            }[\"useTranslation.useCallback[translateText]\"]);\n        }\n    }[\"useTranslation.useCallback[translateText]\"], [\n        processTranslationQueue\n    ]);\n    // Clear translation cache\n    const clearCache = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useTranslation.useCallback[clearCache]\": ()=>{\n            cacheRef.current = {};\n            if (true) {\n                localStorage.removeItem('caption-ninja-translation-cache');\n            }\n        }\n    }[\"useTranslation.useCallback[clearCache]\"], []);\n    // Get cache size\n    const getCacheSize = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useTranslation.useCallback[getCacheSize]\": ()=>{\n            return Object.keys(cacheRef.current).length;\n        }\n    }[\"useTranslation.useCallback[getCacheSize]\"], []);\n    // Cancel all pending translations\n    const cancelTranslation = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useTranslation.useCallback[cancelTranslation]\": ()=>{\n            requestQueueRef.current = [];\n            if (abortControllerRef.current) {\n                abortControllerRef.current.abort();\n                abortControllerRef.current = null;\n            }\n            setIsTranslating(false);\n        }\n    }[\"useTranslation.useCallback[cancelTranslation]\"], []);\n    // Get translation statistics\n    const getTranslationStats = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useTranslation.useCallback[getTranslationStats]\": ()=>{\n            const cache = cacheRef.current;\n            const entries = Object.values(cache);\n            const totalTranslations = entries.length;\n            const totalCharacters = entries.reduce({\n                \"useTranslation.useCallback[getTranslationStats].totalCharacters\": (sum, entry)=>{\n                    var _entry_metadata;\n                    return sum + (((_entry_metadata = entry.metadata) === null || _entry_metadata === void 0 ? void 0 : _entry_metadata.characterCount) || 0);\n                }\n            }[\"useTranslation.useCallback[getTranslationStats].totalCharacters\"], 0);\n            const averageProcessingTime = entries.length > 0 ? entries.reduce({\n                \"useTranslation.useCallback[getTranslationStats]\": (sum, entry)=>{\n                    var _entry_metadata;\n                    return sum + (((_entry_metadata = entry.metadata) === null || _entry_metadata === void 0 ? void 0 : _entry_metadata.processingTime) || 0);\n                }\n            }[\"useTranslation.useCallback[getTranslationStats]\"], 0) / entries.length : 0;\n            return {\n                totalTranslations,\n                totalCharacters,\n                averageProcessingTime,\n                cacheSize: totalTranslations,\n                cacheHitRate: 0 // Would need to track this separately\n            };\n        }\n    }[\"useTranslation.useCallback[getTranslationStats]\"], []);\n    return {\n        translateText,\n        isTranslating,\n        error,\n        clearCache,\n        getCacheSize,\n        cancelTranslation,\n        getTranslationStats\n    };\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (useTranslation);\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/hooks/useTranslation.ts\n"));

/***/ })

});