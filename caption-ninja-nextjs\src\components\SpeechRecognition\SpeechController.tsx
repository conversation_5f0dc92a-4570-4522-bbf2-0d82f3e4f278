/**
 * SpeechController Component
 *
 * UI component for controlling speech recognition functionality.
 * Provides start/stop buttons, language selection, and visual feedback.
 * Integrates with the CaptionContext and useSpeechRecognition hook.
 */

'use client';

import React, { useEffect, useCallback } from 'react';
import { useCaption, useCaptionActions } from '@/contexts/CaptionContext';
import useSpeechRecognition from '@/hooks/useSpeechRecognition';
import useAutoTranslation from '@/hooks/useAutoTranslation';
import { SUPPORTED_LANGUAGES } from '@/utils/speechRecognition';
import { generateTranscriptId } from '@/utils/speechRecognition';

interface SpeechControllerProps {
  className?: string;
}

const SpeechController: React.FC<SpeechControllerProps> = ({ className = '' }) => {
  const { state } = useCaption();
  const actions = useCaptionActions();

  // Auto-translation hook - automatically translates speech results
  const autoTranslation = useAutoTranslation({
    debounceDelay: 500,
    translateInterim: false, // Only translate final results for better performance
    maxRetries: 2
  });

  // Initialize speech recognition with current settings
  const speechRecognition = useSpeechRecognition({
    language: state.settings.speechLanguage,
    continuous: true,
    interimResults: true,
    maxAlternatives: 1,
  });

  // Update context when speech recognition state changes
  useEffect(() => {
    actions.setRecordingStatus(speechRecognition.isListening);
  }, [speechRecognition.isListening, actions]);

  useEffect(() => {
    actions.setSpeechError(speechRecognition.error);
  }, [speechRecognition.error, actions]);

  // Update transcripts in context
  useEffect(() => {
    actions.setFinalTranscript(speechRecognition.finalTranscript);
  }, [speechRecognition.finalTranscript, actions]);

  useEffect(() => {
    actions.setInterimTranscript(speechRecognition.interimTranscript);
  }, [speechRecognition.interimTranscript, actions]);

  // Add final transcript to history when it changes
  useEffect(() => {
    if (speechRecognition.finalTranscript && speechRecognition.finalTranscript.trim()) {
      const entry = {
        id: generateTranscriptId(),
        text: speechRecognition.finalTranscript.trim(),
        timestamp: Date.now(),
        isInterim: false,
        sessionId: state.sessionId,
        languageCode: state.settings.speechLanguage,
      };
      actions.addTranscriptEntry(entry);

      // Reset the final transcript in the hook to prepare for next entry
      speechRecognition.resetTranscript();
    }
  }, [speechRecognition.finalTranscript, actions, state.sessionId, state.settings.speechLanguage, speechRecognition]);

  // Handle language change
  const handleLanguageChange = useCallback((language: string) => {
    actions.updateSettings({ speechLanguage: language });
    speechRecognition.setLanguage(language);
  }, [actions, speechRecognition]);

  // Handle start/stop
  const handleToggleRecording = useCallback(() => {
    if (speechRecognition.isListening) {
      speechRecognition.stopListening();
    } else {
      speechRecognition.startListening();
    }
  }, [speechRecognition]);

  // Keyboard shortcuts
  useEffect(() => {
    const handleKeyPress = (event: KeyboardEvent) => {
      // Space bar to toggle recording (when not in input fields)
      if (event.code === 'Space' && !['INPUT', 'TEXTAREA', 'SELECT'].includes((event.target as HTMLElement)?.tagName)) {
        event.preventDefault();
        handleToggleRecording();
      }
    };

    document.addEventListener('keydown', handleKeyPress);
    return () => document.removeEventListener('keydown', handleKeyPress);
  }, [handleToggleRecording]);

  if (!speechRecognition.isSupported) {
    return (
      <div className={`p-4 bg-red-50 border border-red-200 rounded-lg ${className}`}>
        <div className="flex items-center gap-2 text-red-700">
          <span className="text-lg">⚠️</span>
          <div>
            <h3 className="font-medium">Speech Recognition Not Supported</h3>
            <p className="text-sm mt-1">
              Please use a supported browser like Chrome or Edge for speech recognition.
            </p>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className={`flex flex-col gap-4 p-4 bg-white rounded-lg shadow-md ${className}`}>
      {/* Header */}
      <div className="flex items-center justify-between">
        <h3 className="text-lg font-semibold text-gray-800">Speech Recognition</h3>
        <div className="flex items-center gap-2 text-sm text-gray-600">
          <kbd className="px-2 py-1 bg-gray-100 rounded text-xs">Space</kbd>
          <span>to toggle</span>
        </div>
      </div>

      {/* Control buttons */}
      <div className="flex gap-3 items-center">
        <button
          onClick={handleToggleRecording}
          disabled={!speechRecognition.isSupported}
          className={`px-6 py-3 rounded-lg font-medium transition-all duration-200 flex items-center gap-2 ${
            speechRecognition.isListening
              ? 'bg-red-500 hover:bg-red-600 text-white shadow-lg'
              : 'bg-green-500 hover:bg-green-600 text-white shadow-md hover:shadow-lg'
          } disabled:opacity-50 disabled:cursor-not-allowed`}
        >
          {speechRecognition.isListening ? (
            <>
              <div className="w-2 h-2 bg-white rounded-full animate-pulse"></div>
              Stop Recording
            </>
          ) : (
            <>
              <div className="w-2 h-2 bg-white rounded-full"></div>
              Start Recording
            </>
          )}
        </button>

        {/* Recording indicator */}
        {speechRecognition.isListening && (
          <div className="flex items-center gap-2 text-red-500 animate-pulse">
            <div className="w-3 h-3 bg-red-500 rounded-full"></div>
            <span className="text-sm font-medium">Live Recording</span>
          </div>
        )}

        {/* Reset button */}
        <button
          onClick={speechRecognition.resetTranscript}
          className="px-4 py-2 text-gray-600 hover:text-gray-800 hover:bg-gray-100 rounded-lg transition-colors text-sm"
          title="Clear current transcript"
        >
          Clear
        </button>
      </div>

      {/* Language selection */}
      <div className="flex items-center gap-3">
        <label htmlFor="language-select" className="text-sm font-medium text-gray-700 min-w-fit">
          Language:
        </label>
        <select
          id="language-select"
          value={state.settings.speechLanguage}
          onChange={(e) => handleLanguageChange(e.target.value)}
          disabled={speechRecognition.isListening}
          className="flex-1 px-3 py-2 border border-gray-300 rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent disabled:bg-gray-100 disabled:cursor-not-allowed"
        >
          {SUPPORTED_LANGUAGES.map((lang) => (
            <option key={lang.code} value={lang.code}>
              {lang.flag} {lang.name}
            </option>
          ))}
        </select>
      </div>

      {/* Current transcript preview */}
      {(speechRecognition.finalTranscript || speechRecognition.interimTranscript) && (
        <div className="p-3 bg-gray-50 rounded-lg border">
          <h4 className="text-sm font-medium text-gray-700 mb-2">Current Transcript:</h4>
          <div className="text-sm space-y-1">
            {speechRecognition.finalTranscript && (
              <div className="text-gray-800">
                <span className="font-medium text-green-600">Final:</span> {speechRecognition.finalTranscript}
              </div>
            )}
            {speechRecognition.interimTranscript && (
              <div className="text-gray-600 italic">
                <span className="font-medium text-yellow-600">Interim:</span> {speechRecognition.interimTranscript}
              </div>
            )}
          </div>
        </div>
      )}

      {/* Error display */}
      {speechRecognition.error && (
        <div className="p-3 bg-red-50 border border-red-200 rounded-lg">
          <div className="flex items-start gap-2">
            <span className="text-red-500 text-lg">⚠️</span>
            <div>
              <h4 className="text-sm font-medium text-red-800">Recognition Error</h4>
              <p className="text-sm text-red-700 mt-1">{speechRecognition.error}</p>
              {speechRecognition.error.includes('denied') && (
                <p className="text-xs text-red-600 mt-2">
                  Please allow microphone access in your browser settings and refresh the page.
                </p>
              )}
            </div>
          </div>
        </div>
      )}

      {/* Status info */}
      <div className="text-xs text-gray-500 bg-gray-50 p-2 rounded">
        <div className="grid grid-cols-2 gap-2">
          <div>
            <strong>Status:</strong> {speechRecognition.isListening ? 'Listening' : 'Stopped'}
          </div>
          <div>
            <strong>Language:</strong> {SUPPORTED_LANGUAGES.find(l => l.code === state.settings.speechLanguage)?.name || state.settings.speechLanguage}
          </div>
          <div>
            <strong>History:</strong> {state.transcriptHistory.length} entries
          </div>
          <div>
            <strong>Session:</strong> {state.sessionId.split('-').pop()}
          </div>
        </div>
      </div>
    </div>
  );
};

export default SpeechController;
