"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./src/hooks/useMediaStream.ts":
/*!*************************************!*\
  !*** ./src/hooks/useMediaStream.ts ***!
  \*************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n/**\n * useMediaStream Hook\n * \n * React hook for managing camera and microphone access via getUserMedia.\n * Provides camera device selection and stream management.\n */ \nconst useMediaStream = ()=>{\n    const [stream, setStream] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(null);\n    const [isStreaming, setIsStreaming] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(false);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(null);\n    const [devices, setDevices] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)([]);\n    const [selectedVideoDevice, setSelectedVideoDevice] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)('');\n    const [selectedAudioDevice, setSelectedAudioDevice] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)('');\n    const [isSupported, setIsSupported] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(false);\n    const streamRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(null);\n    // Timeout wrapper for getUserMedia calls\n    const getUserMediaWithTimeout = function(constraints) {\n        let timeoutMs = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : 10000;\n        return new Promise((resolve, reject)=>{\n            const timeoutId = setTimeout(()=>{\n                reject(new Error(\"getUserMedia timeout after \".concat(timeoutMs, \"ms\")));\n            }, timeoutMs);\n            navigator.mediaDevices.getUserMedia(constraints).then((stream)=>{\n                clearTimeout(timeoutId);\n                resolve(stream);\n            }).catch((error)=>{\n                clearTimeout(timeoutId);\n                reject(error);\n            });\n        });\n    };\n    // Progressive constraint fallback to prevent timeout errors\n    const tryGetUserMedia = async (options)=>{\n        const constraints = [\n            // First attempt: Full constraints with exact device IDs\n            options,\n            // Second attempt: Ideal constraints without exact device IDs\n            {\n                video: options.video ? {\n                    width: {\n                        ideal: 1280\n                    },\n                    height: {\n                        ideal: 720\n                    },\n                    frameRate: {\n                        ideal: 30\n                    }\n                } : false,\n                audio: options.audio ? {\n                    echoCancellation: true,\n                    noiseSuppression: true,\n                    autoGainControl: true\n                } : false\n            },\n            // Third attempt: Basic constraints\n            {\n                video: options.video ? {\n                    width: {\n                        ideal: 640\n                    },\n                    height: {\n                        ideal: 480\n                    }\n                } : false,\n                audio: options.audio ? true : false\n            },\n            // Final attempt: Minimal constraints\n            {\n                video: options.video ? true : false,\n                audio: options.audio ? true : false\n            }\n        ];\n        let lastError = null;\n        for(let i = 0; i < constraints.length; i++){\n            try {\n                console.log(\"Attempting getUserMedia with constraint set \".concat(i + 1, \"/\").concat(constraints.length));\n                // Use shorter timeout for fallback attempts\n                const timeout = i === 0 ? 10000 : 5000;\n                const stream = await getUserMediaWithTimeout(constraints[i], timeout);\n                if (i > 0) {\n                    console.log(\"Successfully obtained media stream using fallback constraint set \".concat(i + 1));\n                }\n                return stream;\n            } catch (error) {\n                lastError = error;\n                console.warn(\"Constraint set \".concat(i + 1, \" failed:\"), error);\n                // If it's a permission error, don't try fallbacks\n                if (lastError.name === 'NotAllowedError') {\n                    throw lastError;\n                }\n            }\n        }\n        // If all attempts failed, throw the last error\n        throw lastError || new Error('Failed to access media devices');\n    };\n    // Refresh available media devices\n    const refreshDevices = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useMediaStream.useCallback[refreshDevices]\": async ()=>{\n            if (!isSupported) return;\n            try {\n                const deviceList = await navigator.mediaDevices.enumerateDevices();\n                const mediaDevices = deviceList.map({\n                    \"useMediaStream.useCallback[refreshDevices].mediaDevices\": (device)=>({\n                            deviceId: device.deviceId,\n                            label: device.label || \"\".concat(device.kind, \" \").concat(device.deviceId.slice(0, 8)),\n                            kind: device.kind\n                        })\n                }[\"useMediaStream.useCallback[refreshDevices].mediaDevices\"]);\n                setDevices(mediaDevices);\n                // Set default devices if none selected (only on first load)\n                if (!selectedVideoDevice && mediaDevices.length > 0) {\n                    const videoDevice = mediaDevices.find({\n                        \"useMediaStream.useCallback[refreshDevices].videoDevice\": (d)=>d.kind === 'videoinput'\n                    }[\"useMediaStream.useCallback[refreshDevices].videoDevice\"]);\n                    if (videoDevice) {\n                        setSelectedVideoDevice(videoDevice.deviceId);\n                    }\n                }\n                if (!selectedAudioDevice && mediaDevices.length > 0) {\n                    const audioDevice = mediaDevices.find({\n                        \"useMediaStream.useCallback[refreshDevices].audioDevice\": (d)=>d.kind === 'audioinput'\n                    }[\"useMediaStream.useCallback[refreshDevices].audioDevice\"]);\n                    if (audioDevice) {\n                        setSelectedAudioDevice(audioDevice.deviceId);\n                    }\n                }\n            } catch (err) {\n                console.error('Failed to enumerate media devices:', err);\n                setError('Failed to enumerate media devices');\n            }\n        }\n    }[\"useMediaStream.useCallback[refreshDevices]\"], [\n        isSupported\n    ]); // Remove selectedVideoDevice and selectedAudioDevice from dependencies\n    // Check for getUserMedia support\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)({\n        \"useMediaStream.useEffect\": ()=>{\n            if ( true && navigator.mediaDevices && typeof navigator.mediaDevices.getUserMedia === 'function') {\n                setIsSupported(true);\n            } else {\n                setIsSupported(false);\n                setError('Media devices are not supported in this browser');\n            }\n        }\n    }[\"useMediaStream.useEffect\"], []);\n    // Refresh devices when supported status changes\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)({\n        \"useMediaStream.useEffect\": ()=>{\n            if (isSupported) {\n                refreshDevices();\n            }\n        }\n    }[\"useMediaStream.useEffect\"], [\n        isSupported,\n        refreshDevices\n    ]);\n    // Start media stream\n    const startStream = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useMediaStream.useCallback[startStream]\": async function() {\n            let options = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : {};\n            if (!isSupported) {\n                setError('Media devices are not supported');\n                return;\n            }\n            if (isStreaming) {\n                return;\n            }\n            const defaultOptions = {\n                video: {\n                    deviceId: selectedVideoDevice ? {\n                        exact: selectedVideoDevice\n                    } : undefined,\n                    width: {\n                        ideal: 1280\n                    },\n                    height: {\n                        ideal: 720\n                    },\n                    frameRate: {\n                        ideal: 30\n                    }\n                },\n                audio: {\n                    deviceId: selectedAudioDevice ? {\n                        exact: selectedAudioDevice\n                    } : undefined,\n                    echoCancellation: true,\n                    noiseSuppression: true,\n                    autoGainControl: true\n                }\n            };\n            const streamOptions = {\n                ...defaultOptions,\n                ...options\n            };\n            try {\n                setError(null);\n                const mediaStream = await tryGetUserMedia(streamOptions);\n                streamRef.current = mediaStream;\n                setStream(mediaStream);\n                setIsStreaming(true);\n                // Refresh devices after getting permission (labels will be available)\n                await refreshDevices();\n            } catch (err) {\n                const error = err;\n                console.error('Failed to access media devices:', err);\n                // Provide more specific error messages\n                let errorMessage = 'Failed to access media devices';\n                if (error.name === 'NotAllowedError') {\n                    errorMessage = 'Camera/microphone access denied. Please allow permissions and try again.';\n                } else if (error.name === 'NotFoundError') {\n                    errorMessage = 'No camera or microphone found. Please connect a device and try again.';\n                } else if (error.name === 'NotReadableError') {\n                    errorMessage = 'Camera/microphone is already in use by another application.';\n                } else if (error.name === 'OverconstrainedError') {\n                    errorMessage = 'Camera/microphone constraints cannot be satisfied. Try different settings.';\n                } else if (error.message) {\n                    errorMessage = \"Media access error: \".concat(error.message);\n                }\n                setError(errorMessage);\n                setIsStreaming(false);\n            }\n        }\n    }[\"useMediaStream.useCallback[startStream]\"], [\n        isSupported,\n        isStreaming,\n        selectedVideoDevice,\n        selectedAudioDevice,\n        refreshDevices\n    ]);\n    // Stop media stream\n    const stopStream = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useMediaStream.useCallback[stopStream]\": ()=>{\n            if (streamRef.current) {\n                streamRef.current.getTracks().forEach({\n                    \"useMediaStream.useCallback[stopStream]\": (track)=>{\n                        track.stop();\n                    }\n                }[\"useMediaStream.useCallback[stopStream]\"]);\n                streamRef.current = null;\n                setStream(null);\n                setIsStreaming(false);\n            }\n        }\n    }[\"useMediaStream.useCallback[stopStream]\"], []);\n    // Switch video device\n    const switchVideoDevice = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useMediaStream.useCallback[switchVideoDevice]\": async (deviceId)=>{\n            setSelectedVideoDevice(deviceId);\n            if (isStreaming) {\n                // Stop current stream and start with new device\n                stopStream();\n                await startStream({\n                    video: {\n                        deviceId: {\n                            exact: deviceId\n                        },\n                        width: {\n                            ideal: 1280\n                        },\n                        height: {\n                            ideal: 720\n                        },\n                        frameRate: {\n                            ideal: 30\n                        }\n                    },\n                    audio: {\n                        deviceId: selectedAudioDevice ? {\n                            exact: selectedAudioDevice\n                        } : undefined,\n                        echoCancellation: true,\n                        noiseSuppression: true,\n                        autoGainControl: true\n                    }\n                });\n            }\n        }\n    }[\"useMediaStream.useCallback[switchVideoDevice]\"], [\n        isStreaming,\n        selectedAudioDevice,\n        stopStream,\n        startStream\n    ]);\n    // Switch audio device\n    const switchAudioDevice = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useMediaStream.useCallback[switchAudioDevice]\": async (deviceId)=>{\n            setSelectedAudioDevice(deviceId);\n            if (isStreaming) {\n                // Stop current stream and start with new device\n                stopStream();\n                await startStream({\n                    video: {\n                        deviceId: selectedVideoDevice ? {\n                            exact: selectedVideoDevice\n                        } : undefined,\n                        width: {\n                            ideal: 1280\n                        },\n                        height: {\n                            ideal: 720\n                        },\n                        frameRate: {\n                            ideal: 30\n                        }\n                    },\n                    audio: {\n                        deviceId: {\n                            exact: deviceId\n                        },\n                        echoCancellation: true,\n                        noiseSuppression: true,\n                        autoGainControl: true\n                    }\n                });\n            }\n        }\n    }[\"useMediaStream.useCallback[switchAudioDevice]\"], [\n        isStreaming,\n        selectedVideoDevice,\n        stopStream,\n        startStream\n    ]);\n    // Cleanup on unmount\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)({\n        \"useMediaStream.useEffect\": ()=>{\n            return ({\n                \"useMediaStream.useEffect\": ()=>{\n                    stopStream();\n                }\n            })[\"useMediaStream.useEffect\"];\n        }\n    }[\"useMediaStream.useEffect\"], [\n        stopStream\n    ]);\n    return {\n        stream,\n        isStreaming,\n        error,\n        devices,\n        selectedVideoDevice,\n        selectedAudioDevice,\n        startStream,\n        stopStream,\n        switchVideoDevice,\n        switchAudioDevice,\n        refreshDevices,\n        isSupported\n    };\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (useMediaStream);\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/hooks/useMediaStream.ts\n"));

/***/ })

});