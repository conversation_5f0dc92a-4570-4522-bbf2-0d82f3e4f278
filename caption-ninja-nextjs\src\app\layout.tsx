import type { Metadata } from "next";
import { <PERSON>ei<PERSON>, <PERSON><PERSON>st_Mono } from "next/font/google";
import "./globals.css";
import { CaptionProvider } from "@/contexts/CaptionContext";
import ErrorBoundary from "@/components/ErrorBoundary";

const geistSans = Geist({
  variable: "--font-geist-sans",
  subsets: ["latin"],
});

const geistMono = Geist_Mono({
  variable: "--font-geist-mono",
  subsets: ["latin"],
});

export const metadata: Metadata = {
  title: "CAPTION.Ninja - Real-time Speech-to-Text with Translation",
  description: "Professional speech-to-text application with real-time translation, video streaming, and export capabilities",
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="en">
      <body
        className={`${geistSans.variable} ${geistMono.variable} antialiased`}
      >
        <ErrorBoundary>
          <CaptionProvider>
            {children}
          </CaptionProvider>
        </ErrorBoundary>
      </body>
    </html>
  );
}
