"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/layout",{

/***/ "(app-pages-browser)/./src/app/globals.css":
/*!*****************************!*\
  !*** ./src/app/globals.css ***!
  \*****************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"f138f9607c4b\");\nif (true) { module.hot.accept() }\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL3NyYy9hcHAvZ2xvYmFscy5jc3MiLCJtYXBwaW5ncyI6Ijs7OztBQUFBLGlFQUFlLGNBQWM7QUFDN0IsSUFBSSxJQUFVLElBQUksaUJBQWlCIiwic291cmNlcyI6WyJDOlxcVXNlcnNcXEFsaXl1U3VudXNpXFxPbmVEcml2ZSAtIFVHU00tTW9uYXJjaCBCdXNpbmVzcyBTY2hvb2wgR21iSFxcRG9jdW1lbnRzXFxjYXB0aW9ubmluamFcXGNhcHRpb24tbmluamEtbmV4dGpzXFxzcmNcXGFwcFxcZ2xvYmFscy5jc3MiXSwic291cmNlc0NvbnRlbnQiOlsiZXhwb3J0IGRlZmF1bHQgXCJmMTM4Zjk2MDdjNGJcIlxuaWYgKG1vZHVsZS5ob3QpIHsgbW9kdWxlLmhvdC5hY2NlcHQoKSB9XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/globals.css\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/contexts/CaptionContext.tsx":
/*!*****************************************!*\
  !*** ./src/contexts/CaptionContext.tsx ***!
  \*****************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   CaptionProvider: () => (/* binding */ CaptionProvider),\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__),\n/* harmony export */   useCaption: () => (/* binding */ useCaption),\n/* harmony export */   useCaptionActions: () => (/* binding */ useCaptionActions),\n/* harmony export */   useCaptionSelectors: () => (/* binding */ useCaptionSelectors)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/**\n * CaptionContext\n *\n * Global context for managing caption-related state across the application.\n * Provides centralized state management for transcripts, translations, and settings.\n */ /* __next_internal_client_entry_do_not_use__ CaptionProvider,useCaption,useCaptionActions,useCaptionSelectors,default auto */ \nvar _s = $RefreshSig$(), _s1 = $RefreshSig$(), _s2 = $RefreshSig$(), _s3 = $RefreshSig$();\n\n// Initial state\nconst initialSettings = {\n    speechLanguage: 'en-US',\n    translationEnabled: false,\n    sourceLanguage: 'auto',\n    targetLanguage: 'en',\n    ttsEnabled: false,\n    ttsAutoPlay: false,\n    selectedVoice: '',\n    ttsRate: 1,\n    ttsPitch: 1,\n    ttsVolume: 1\n};\nconst initialState = {\n    finalTranscript: '',\n    interimTranscript: '',\n    translatedText: '',\n    transcriptHistory: [],\n    isRecording: false,\n    isTranslating: false,\n    isTTSPlaying: false,\n    settings: initialSettings,\n    speechError: null,\n    translationError: null,\n    ttsError: null,\n    sessionId: 'session-initial',\n    lastUpdate: 0\n};\n// Reducer\nconst captionReducer = (state, action)=>{\n    switch(action.type){\n        case 'SET_FINAL_TRANSCRIPT':\n            return {\n                ...state,\n                finalTranscript: action.payload,\n                lastUpdate: Date.now()\n            };\n        case 'SET_INTERIM_TRANSCRIPT':\n            return {\n                ...state,\n                interimTranscript: action.payload,\n                lastUpdate: Date.now()\n            };\n        case 'SET_TRANSLATED_TEXT':\n            return {\n                ...state,\n                translatedText: action.payload,\n                lastUpdate: Date.now()\n            };\n        case 'ADD_TRANSCRIPT_ENTRY':\n            return {\n                ...state,\n                transcriptHistory: [\n                    ...state.transcriptHistory,\n                    action.payload\n                ],\n                lastUpdate: Date.now()\n            };\n        case 'UPDATE_TRANSCRIPT_ENTRY':\n            return {\n                ...state,\n                transcriptHistory: state.transcriptHistory.map((entry)=>entry.id === action.payload.id ? {\n                        ...entry,\n                        ...action.payload.updates\n                    } : entry),\n                lastUpdate: Date.now()\n            };\n        case 'DELETE_TRANSCRIPT_ENTRY':\n            return {\n                ...state,\n                transcriptHistory: state.transcriptHistory.filter((entry)=>entry.id !== action.payload),\n                lastUpdate: Date.now()\n            };\n        case 'CLEAR_TRANSCRIPT_HISTORY':\n            return {\n                ...state,\n                transcriptHistory: [],\n                lastUpdate: Date.now()\n            };\n        case 'SET_RECORDING_STATUS':\n            return {\n                ...state,\n                isRecording: action.payload,\n                lastUpdate: Date.now()\n            };\n        case 'SET_TRANSLATING_STATUS':\n            return {\n                ...state,\n                isTranslating: action.payload,\n                lastUpdate: Date.now()\n            };\n        case 'SET_TTS_PLAYING_STATUS':\n            return {\n                ...state,\n                isTTSPlaying: action.payload,\n                lastUpdate: Date.now()\n            };\n        case 'UPDATE_SETTINGS':\n            return {\n                ...state,\n                settings: {\n                    ...state.settings,\n                    ...action.payload\n                },\n                lastUpdate: Date.now()\n            };\n        case 'SET_SPEECH_ERROR':\n            return {\n                ...state,\n                speechError: action.payload,\n                lastUpdate: Date.now()\n            };\n        case 'SET_TRANSLATION_ERROR':\n            return {\n                ...state,\n                translationError: action.payload,\n                lastUpdate: Date.now()\n            };\n        case 'SET_TTS_ERROR':\n            return {\n                ...state,\n                ttsError: action.payload,\n                lastUpdate: Date.now()\n            };\n        case 'RESET_TRANSCRIPT':\n            return {\n                ...state,\n                finalTranscript: '',\n                interimTranscript: '',\n                translatedText: '',\n                speechError: null,\n                translationError: null,\n                lastUpdate: Date.now()\n            };\n        case 'LOAD_SESSION':\n            return {\n                ...state,\n                ...action.payload,\n                lastUpdate: Date.now()\n            };\n        case 'SET_SESSION_ID':\n            return {\n                ...state,\n                sessionId: action.payload,\n                lastUpdate: Date.now()\n            };\n        default:\n            return state;\n    }\n};\n// Context\nconst CaptionContext = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.createContext)(null);\nconst CaptionProvider = (param)=>{\n    let { children } = param;\n    _s();\n    const [state, dispatch] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useReducer)(captionReducer, initialState);\n    // Initialize client-side data and load saved settings\n    react__WEBPACK_IMPORTED_MODULE_1___default().useEffect({\n        \"CaptionProvider.useEffect\": ()=>{\n            if (true) {\n                // Set unique session ID on client side to avoid hydration mismatch\n                dispatch({\n                    type: 'SET_SESSION_ID',\n                    payload: \"session-\".concat(Date.now())\n                });\n                try {\n                    const savedSettings = localStorage.getItem('caption-ninja-settings');\n                    if (savedSettings) {\n                        const settings = JSON.parse(savedSettings);\n                        dispatch({\n                            type: 'UPDATE_SETTINGS',\n                            payload: settings\n                        });\n                    }\n                } catch (error) {\n                    console.warn('Failed to load saved settings:', error);\n                }\n            }\n        }\n    }[\"CaptionProvider.useEffect\"], []);\n    // Save settings to localStorage when they change\n    react__WEBPACK_IMPORTED_MODULE_1___default().useEffect({\n        \"CaptionProvider.useEffect\": ()=>{\n            if (true) {\n                try {\n                    localStorage.setItem('caption-ninja-settings', JSON.stringify(state.settings));\n                } catch (error) {\n                    console.warn('Failed to save caption settings:', error);\n                }\n            }\n        }\n    }[\"CaptionProvider.useEffect\"], [\n        state.settings\n    ]);\n    // Auto-save session data periodically\n    react__WEBPACK_IMPORTED_MODULE_1___default().useEffect({\n        \"CaptionProvider.useEffect\": ()=>{\n            if ( true && state.transcriptHistory.length > 0) {\n                const saveSession = {\n                    \"CaptionProvider.useEffect.saveSession\": ()=>{\n                        try {\n                            const sessionData = {\n                                transcriptHistory: state.transcriptHistory,\n                                sessionId: state.sessionId,\n                                lastUpdate: state.lastUpdate\n                            };\n                            localStorage.setItem('caption-ninja-last-session', JSON.stringify(sessionData));\n                        } catch (error) {\n                            console.warn('Failed to save session data:', error);\n                        }\n                    }\n                }[\"CaptionProvider.useEffect.saveSession\"];\n                const interval = setInterval(saveSession, 30000); // Save every 30 seconds\n                return ({\n                    \"CaptionProvider.useEffect\": ()=>clearInterval(interval)\n                })[\"CaptionProvider.useEffect\"];\n            }\n        }\n    }[\"CaptionProvider.useEffect\"], [\n        state.transcriptHistory,\n        state.sessionId,\n        state.lastUpdate\n    ]);\n    // Memoize context value to prevent unnecessary re-renders\n    const contextValue = react__WEBPACK_IMPORTED_MODULE_1___default().useMemo({\n        \"CaptionProvider.useMemo[contextValue]\": ()=>({\n                state,\n                dispatch\n            })\n    }[\"CaptionProvider.useMemo[contextValue]\"], [\n        state,\n        dispatch\n    ]);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(CaptionContext.Provider, {\n        value: contextValue,\n        children: children\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive - UGSM-Monarch Business School GmbH\\\\Documents\\\\captionninja\\\\caption-ninja-nextjs\\\\src\\\\contexts\\\\CaptionContext.tsx\",\n        lineNumber: 323,\n        columnNumber: 5\n    }, undefined);\n};\n_s(CaptionProvider, \"0jzhUUPXp4QnlH12/b0rtPEkclA=\");\n_c = CaptionProvider;\n// Custom hook to use the caption context\nconst useCaption = ()=>{\n    _s1();\n    const context = (0,react__WEBPACK_IMPORTED_MODULE_1__.useContext)(CaptionContext);\n    if (!context) {\n        throw new Error('useCaption must be used within a CaptionProvider');\n    }\n    return context;\n};\n_s1(useCaption, \"b9L3QQ+jgeyIrH0NfHrJ8nn7VMU=\");\n// Helper hooks for specific functionality\nconst useCaptionActions = ()=>{\n    _s2();\n    const { dispatch } = useCaption();\n    const addTranscriptEntry = react__WEBPACK_IMPORTED_MODULE_1___default().useCallback({\n        \"useCaptionActions.useCallback[addTranscriptEntry]\": (entry)=>{\n            dispatch({\n                type: 'ADD_TRANSCRIPT_ENTRY',\n                payload: entry\n            });\n        }\n    }[\"useCaptionActions.useCallback[addTranscriptEntry]\"], [\n        dispatch\n    ]);\n    const updateTranscriptEntry = react__WEBPACK_IMPORTED_MODULE_1___default().useCallback({\n        \"useCaptionActions.useCallback[updateTranscriptEntry]\": (id, updates)=>{\n            dispatch({\n                type: 'UPDATE_TRANSCRIPT_ENTRY',\n                payload: {\n                    id,\n                    updates\n                }\n            });\n        }\n    }[\"useCaptionActions.useCallback[updateTranscriptEntry]\"], [\n        dispatch\n    ]);\n    const deleteTranscriptEntry = react__WEBPACK_IMPORTED_MODULE_1___default().useCallback({\n        \"useCaptionActions.useCallback[deleteTranscriptEntry]\": (id)=>{\n            dispatch({\n                type: 'DELETE_TRANSCRIPT_ENTRY',\n                payload: id\n            });\n        }\n    }[\"useCaptionActions.useCallback[deleteTranscriptEntry]\"], [\n        dispatch\n    ]);\n    const clearTranscriptHistory = react__WEBPACK_IMPORTED_MODULE_1___default().useCallback({\n        \"useCaptionActions.useCallback[clearTranscriptHistory]\": ()=>{\n            dispatch({\n                type: 'CLEAR_TRANSCRIPT_HISTORY'\n            });\n        }\n    }[\"useCaptionActions.useCallback[clearTranscriptHistory]\"], [\n        dispatch\n    ]);\n    const setFinalTranscript = react__WEBPACK_IMPORTED_MODULE_1___default().useCallback({\n        \"useCaptionActions.useCallback[setFinalTranscript]\": (transcript)=>{\n            dispatch({\n                type: 'SET_FINAL_TRANSCRIPT',\n                payload: transcript\n            });\n        }\n    }[\"useCaptionActions.useCallback[setFinalTranscript]\"], [\n        dispatch\n    ]);\n    const setInterimTranscript = react__WEBPACK_IMPORTED_MODULE_1___default().useCallback({\n        \"useCaptionActions.useCallback[setInterimTranscript]\": (transcript)=>{\n            dispatch({\n                type: 'SET_INTERIM_TRANSCRIPT',\n                payload: transcript\n            });\n        }\n    }[\"useCaptionActions.useCallback[setInterimTranscript]\"], [\n        dispatch\n    ]);\n    const setTranslatedText = react__WEBPACK_IMPORTED_MODULE_1___default().useCallback({\n        \"useCaptionActions.useCallback[setTranslatedText]\": (text)=>{\n            dispatch({\n                type: 'SET_TRANSLATED_TEXT',\n                payload: text\n            });\n        }\n    }[\"useCaptionActions.useCallback[setTranslatedText]\"], [\n        dispatch\n    ]);\n    const updateSettings = react__WEBPACK_IMPORTED_MODULE_1___default().useCallback({\n        \"useCaptionActions.useCallback[updateSettings]\": (settings)=>{\n            dispatch({\n                type: 'UPDATE_SETTINGS',\n                payload: settings\n            });\n        }\n    }[\"useCaptionActions.useCallback[updateSettings]\"], [\n        dispatch\n    ]);\n    const setRecordingStatus = react__WEBPACK_IMPORTED_MODULE_1___default().useCallback({\n        \"useCaptionActions.useCallback[setRecordingStatus]\": (isRecording)=>{\n            dispatch({\n                type: 'SET_RECORDING_STATUS',\n                payload: isRecording\n            });\n        }\n    }[\"useCaptionActions.useCallback[setRecordingStatus]\"], [\n        dispatch\n    ]);\n    const setTranslatingStatus = react__WEBPACK_IMPORTED_MODULE_1___default().useCallback({\n        \"useCaptionActions.useCallback[setTranslatingStatus]\": (isTranslating)=>{\n            dispatch({\n                type: 'SET_TRANSLATING_STATUS',\n                payload: isTranslating\n            });\n        }\n    }[\"useCaptionActions.useCallback[setTranslatingStatus]\"], [\n        dispatch\n    ]);\n    const setTTSPlayingStatus = react__WEBPACK_IMPORTED_MODULE_1___default().useCallback({\n        \"useCaptionActions.useCallback[setTTSPlayingStatus]\": (isPlaying)=>{\n            dispatch({\n                type: 'SET_TTS_PLAYING_STATUS',\n                payload: isPlaying\n            });\n        }\n    }[\"useCaptionActions.useCallback[setTTSPlayingStatus]\"], [\n        dispatch\n    ]);\n    const setSpeechError = react__WEBPACK_IMPORTED_MODULE_1___default().useCallback({\n        \"useCaptionActions.useCallback[setSpeechError]\": (error)=>{\n            dispatch({\n                type: 'SET_SPEECH_ERROR',\n                payload: error\n            });\n        }\n    }[\"useCaptionActions.useCallback[setSpeechError]\"], [\n        dispatch\n    ]);\n    const setTranslationError = react__WEBPACK_IMPORTED_MODULE_1___default().useCallback({\n        \"useCaptionActions.useCallback[setTranslationError]\": (error)=>{\n            dispatch({\n                type: 'SET_TRANSLATION_ERROR',\n                payload: error\n            });\n        }\n    }[\"useCaptionActions.useCallback[setTranslationError]\"], [\n        dispatch\n    ]);\n    const setTTSError = react__WEBPACK_IMPORTED_MODULE_1___default().useCallback({\n        \"useCaptionActions.useCallback[setTTSError]\": (error)=>{\n            dispatch({\n                type: 'SET_TTS_ERROR',\n                payload: error\n            });\n        }\n    }[\"useCaptionActions.useCallback[setTTSError]\"], [\n        dispatch\n    ]);\n    const resetTranscript = react__WEBPACK_IMPORTED_MODULE_1___default().useCallback({\n        \"useCaptionActions.useCallback[resetTranscript]\": ()=>{\n            dispatch({\n                type: 'RESET_TRANSCRIPT'\n            });\n        }\n    }[\"useCaptionActions.useCallback[resetTranscript]\"], [\n        dispatch\n    ]);\n    // Memoize the actions object to prevent unnecessary re-renders\n    return react__WEBPACK_IMPORTED_MODULE_1___default().useMemo({\n        \"useCaptionActions.useMemo\": ()=>({\n                addTranscriptEntry,\n                updateTranscriptEntry,\n                deleteTranscriptEntry,\n                clearTranscriptHistory,\n                setFinalTranscript,\n                setInterimTranscript,\n                setTranslatedText,\n                updateSettings,\n                setRecordingStatus,\n                setTranslatingStatus,\n                setTTSPlayingStatus,\n                setSpeechError,\n                setTranslationError,\n                setTTSError,\n                resetTranscript\n            })\n    }[\"useCaptionActions.useMemo\"], [\n        addTranscriptEntry,\n        updateTranscriptEntry,\n        deleteTranscriptEntry,\n        clearTranscriptHistory,\n        setFinalTranscript,\n        setInterimTranscript,\n        setTranslatedText,\n        updateSettings,\n        setRecordingStatus,\n        setTranslatingStatus,\n        setTTSPlayingStatus,\n        setSpeechError,\n        setTranslationError,\n        setTTSError,\n        resetTranscript\n    ]);\n};\n_s2(useCaptionActions, \"y4RDCwLc9JlQDBhw9zHHuxdSAmg=\", false, function() {\n    return [\n        useCaption\n    ];\n});\n// Hook for accessing caption state selectors\nconst useCaptionSelectors = ()=>{\n    _s3();\n    const { state } = useCaption();\n    const getCurrentTranscript = react__WEBPACK_IMPORTED_MODULE_1___default().useCallback({\n        \"useCaptionSelectors.useCallback[getCurrentTranscript]\": ()=>{\n            return state.finalTranscript + (state.interimTranscript ? ' ' + state.interimTranscript : '');\n        }\n    }[\"useCaptionSelectors.useCallback[getCurrentTranscript]\"], [\n        state.finalTranscript,\n        state.interimTranscript\n    ]);\n    const getTranscriptWordCount = react__WEBPACK_IMPORTED_MODULE_1___default().useCallback({\n        \"useCaptionSelectors.useCallback[getTranscriptWordCount]\": ()=>{\n            const fullTranscript = getCurrentTranscript();\n            return fullTranscript.trim().split(/\\s+/).filter({\n                \"useCaptionSelectors.useCallback[getTranscriptWordCount]\": (word)=>word.length > 0\n            }[\"useCaptionSelectors.useCallback[getTranscriptWordCount]\"]).length;\n        }\n    }[\"useCaptionSelectors.useCallback[getTranscriptWordCount]\"], [\n        getCurrentTranscript\n    ]);\n    const getSessionDuration = react__WEBPACK_IMPORTED_MODULE_1___default().useCallback({\n        \"useCaptionSelectors.useCallback[getSessionDuration]\": ()=>{\n            if (state.transcriptHistory.length === 0) return 0;\n            const firstEntry = state.transcriptHistory[0];\n            const lastEntry = state.transcriptHistory[state.transcriptHistory.length - 1];\n            return lastEntry.timestamp - firstEntry.timestamp;\n        }\n    }[\"useCaptionSelectors.useCallback[getSessionDuration]\"], [\n        state.transcriptHistory\n    ]);\n    const hasActiveTranscript = react__WEBPACK_IMPORTED_MODULE_1___default().useCallback({\n        \"useCaptionSelectors.useCallback[hasActiveTranscript]\": ()=>{\n            return state.finalTranscript.length > 0 || state.interimTranscript.length > 0;\n        }\n    }[\"useCaptionSelectors.useCallback[hasActiveTranscript]\"], [\n        state.finalTranscript,\n        state.interimTranscript\n    ]);\n    const getLanguageStats = react__WEBPACK_IMPORTED_MODULE_1___default().useCallback({\n        \"useCaptionSelectors.useCallback[getLanguageStats]\": ()=>{\n            const stats = {};\n            state.transcriptHistory.forEach({\n                \"useCaptionSelectors.useCallback[getLanguageStats]\": (entry)=>{\n                    const lang = entry.languageCode || 'unknown';\n                    stats[lang] = (stats[lang] || 0) + 1;\n                }\n            }[\"useCaptionSelectors.useCallback[getLanguageStats]\"]);\n            return stats;\n        }\n    }[\"useCaptionSelectors.useCallback[getLanguageStats]\"], [\n        state.transcriptHistory\n    ]);\n    return {\n        getCurrentTranscript,\n        getTranscriptWordCount,\n        getSessionDuration,\n        hasActiveTranscript,\n        getLanguageStats\n    };\n};\n_s3(useCaptionSelectors, \"wR7VlyPqzmpFy4JNrGDJjW/fGlY=\", false, function() {\n    return [\n        useCaption\n    ];\n});\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (CaptionContext);\nvar _c;\n$RefreshReg$(_c, \"CaptionProvider\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL3NyYy9jb250ZXh0cy9DYXB0aW9uQ29udGV4dC50c3giLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7QUFBQTs7Ozs7Q0FLQzs7QUFJK0U7QUEyRWhGLGdCQUFnQjtBQUNoQixNQUFNSSxrQkFBbUM7SUFDdkNDLGdCQUFnQjtJQUNoQkMsb0JBQW9CO0lBQ3BCQyxnQkFBZ0I7SUFDaEJDLGdCQUFnQjtJQUNoQkMsWUFBWTtJQUNaQyxhQUFhO0lBQ2JDLGVBQWU7SUFDZkMsU0FBUztJQUNUQyxVQUFVO0lBQ1ZDLFdBQVc7QUFDYjtBQUVBLE1BQU1DLGVBQTZCO0lBQ2pDQyxpQkFBaUI7SUFDakJDLG1CQUFtQjtJQUNuQkMsZ0JBQWdCO0lBQ2hCQyxtQkFBbUIsRUFBRTtJQUNyQkMsYUFBYTtJQUNiQyxlQUFlO0lBQ2ZDLGNBQWM7SUFDZEMsVUFBVW5CO0lBQ1ZvQixhQUFhO0lBQ2JDLGtCQUFrQjtJQUNsQkMsVUFBVTtJQUNWQyxXQUFXO0lBQ1hDLFlBQVk7QUFDZDtBQUVBLFVBQVU7QUFDVixNQUFNQyxpQkFBaUIsQ0FBQ0MsT0FBcUJDO0lBQzNDLE9BQVFBLE9BQU9DLElBQUk7UUFDakIsS0FBSztZQUNILE9BQU87Z0JBQ0wsR0FBR0YsS0FBSztnQkFDUmQsaUJBQWlCZSxPQUFPRSxPQUFPO2dCQUMvQkwsWUFBWU0sS0FBS0MsR0FBRztZQUN0QjtRQUVGLEtBQUs7WUFDSCxPQUFPO2dCQUNMLEdBQUdMLEtBQUs7Z0JBQ1JiLG1CQUFtQmMsT0FBT0UsT0FBTztnQkFDakNMLFlBQVlNLEtBQUtDLEdBQUc7WUFDdEI7UUFFRixLQUFLO1lBQ0gsT0FBTztnQkFDTCxHQUFHTCxLQUFLO2dCQUNSWixnQkFBZ0JhLE9BQU9FLE9BQU87Z0JBQzlCTCxZQUFZTSxLQUFLQyxHQUFHO1lBQ3RCO1FBRUYsS0FBSztZQUNILE9BQU87Z0JBQ0wsR0FBR0wsS0FBSztnQkFDUlgsbUJBQW1CO3VCQUFJVyxNQUFNWCxpQkFBaUI7b0JBQUVZLE9BQU9FLE9BQU87aUJBQUM7Z0JBQy9ETCxZQUFZTSxLQUFLQyxHQUFHO1lBQ3RCO1FBRUYsS0FBSztZQUNILE9BQU87Z0JBQ0wsR0FBR0wsS0FBSztnQkFDUlgsbUJBQW1CVyxNQUFNWCxpQkFBaUIsQ0FBQ2lCLEdBQUcsQ0FBQ0MsQ0FBQUEsUUFDN0NBLE1BQU1DLEVBQUUsS0FBS1AsT0FBT0UsT0FBTyxDQUFDSyxFQUFFLEdBQzFCO3dCQUFFLEdBQUdELEtBQUs7d0JBQUUsR0FBR04sT0FBT0UsT0FBTyxDQUFDTSxPQUFPO29CQUFDLElBQ3RDRjtnQkFFTlQsWUFBWU0sS0FBS0MsR0FBRztZQUN0QjtRQUVGLEtBQUs7WUFDSCxPQUFPO2dCQUNMLEdBQUdMLEtBQUs7Z0JBQ1JYLG1CQUFtQlcsTUFBTVgsaUJBQWlCLENBQUNxQixNQUFNLENBQy9DSCxDQUFBQSxRQUFTQSxNQUFNQyxFQUFFLEtBQUtQLE9BQU9FLE9BQU87Z0JBRXRDTCxZQUFZTSxLQUFLQyxHQUFHO1lBQ3RCO1FBRUYsS0FBSztZQUNILE9BQU87Z0JBQ0wsR0FBR0wsS0FBSztnQkFDUlgsbUJBQW1CLEVBQUU7Z0JBQ3JCUyxZQUFZTSxLQUFLQyxHQUFHO1lBQ3RCO1FBRUYsS0FBSztZQUNILE9BQU87Z0JBQ0wsR0FBR0wsS0FBSztnQkFDUlYsYUFBYVcsT0FBT0UsT0FBTztnQkFDM0JMLFlBQVlNLEtBQUtDLEdBQUc7WUFDdEI7UUFFRixLQUFLO1lBQ0gsT0FBTztnQkFDTCxHQUFHTCxLQUFLO2dCQUNSVCxlQUFlVSxPQUFPRSxPQUFPO2dCQUM3QkwsWUFBWU0sS0FBS0MsR0FBRztZQUN0QjtRQUVGLEtBQUs7WUFDSCxPQUFPO2dCQUNMLEdBQUdMLEtBQUs7Z0JBQ1JSLGNBQWNTLE9BQU9FLE9BQU87Z0JBQzVCTCxZQUFZTSxLQUFLQyxHQUFHO1lBQ3RCO1FBRUYsS0FBSztZQUNILE9BQU87Z0JBQ0wsR0FBR0wsS0FBSztnQkFDUlAsVUFBVTtvQkFBRSxHQUFHTyxNQUFNUCxRQUFRO29CQUFFLEdBQUdRLE9BQU9FLE9BQU87Z0JBQUM7Z0JBQ2pETCxZQUFZTSxLQUFLQyxHQUFHO1lBQ3RCO1FBRUYsS0FBSztZQUNILE9BQU87Z0JBQ0wsR0FBR0wsS0FBSztnQkFDUk4sYUFBYU8sT0FBT0UsT0FBTztnQkFDM0JMLFlBQVlNLEtBQUtDLEdBQUc7WUFDdEI7UUFFRixLQUFLO1lBQ0gsT0FBTztnQkFDTCxHQUFHTCxLQUFLO2dCQUNSTCxrQkFBa0JNLE9BQU9FLE9BQU87Z0JBQ2hDTCxZQUFZTSxLQUFLQyxHQUFHO1lBQ3RCO1FBRUYsS0FBSztZQUNILE9BQU87Z0JBQ0wsR0FBR0wsS0FBSztnQkFDUkosVUFBVUssT0FBT0UsT0FBTztnQkFDeEJMLFlBQVlNLEtBQUtDLEdBQUc7WUFDdEI7UUFFRixLQUFLO1lBQ0gsT0FBTztnQkFDTCxHQUFHTCxLQUFLO2dCQUNSZCxpQkFBaUI7Z0JBQ2pCQyxtQkFBbUI7Z0JBQ25CQyxnQkFBZ0I7Z0JBQ2hCTSxhQUFhO2dCQUNiQyxrQkFBa0I7Z0JBQ2xCRyxZQUFZTSxLQUFLQyxHQUFHO1lBQ3RCO1FBRUYsS0FBSztZQUNILE9BQU87Z0JBQ0wsR0FBR0wsS0FBSztnQkFDUixHQUFHQyxPQUFPRSxPQUFPO2dCQUNqQkwsWUFBWU0sS0FBS0MsR0FBRztZQUN0QjtRQUVGLEtBQUs7WUFDSCxPQUFPO2dCQUNMLEdBQUdMLEtBQUs7Z0JBQ1JILFdBQVdJLE9BQU9FLE9BQU87Z0JBQ3pCTCxZQUFZTSxLQUFLQyxHQUFHO1lBQ3RCO1FBRUY7WUFDRSxPQUFPTDtJQUNYO0FBQ0Y7QUFFQSxVQUFVO0FBQ1YsTUFBTVcsK0JBQWlCeEMsb0RBQWFBLENBRzFCO0FBT0gsTUFBTXlDLGtCQUFrRDtRQUFDLEVBQUVDLFFBQVEsRUFBRTs7SUFDMUUsTUFBTSxDQUFDYixPQUFPYyxTQUFTLEdBQUd6QyxpREFBVUEsQ0FBQzBCLGdCQUFnQmQ7SUFFckQsc0RBQXNEO0lBQ3REZixzREFBZTtxQ0FBQztZQUNkLElBQUksSUFBNkIsRUFBRTtnQkFDakMsbUVBQW1FO2dCQUNuRTRDLFNBQVM7b0JBQUVaLE1BQU07b0JBQWtCQyxTQUFTLFdBQXNCLE9BQVhDLEtBQUtDLEdBQUc7Z0JBQUs7Z0JBRXBFLElBQUk7b0JBQ0YsTUFBTVcsZ0JBQWdCQyxhQUFhQyxPQUFPLENBQUM7b0JBQzNDLElBQUlGLGVBQWU7d0JBQ2pCLE1BQU12QixXQUFXMEIsS0FBS0MsS0FBSyxDQUFDSjt3QkFDNUJGLFNBQVM7NEJBQUVaLE1BQU07NEJBQW1CQyxTQUFTVjt3QkFBUztvQkFDeEQ7Z0JBQ0YsRUFBRSxPQUFPNEIsT0FBTztvQkFDZEMsUUFBUUMsSUFBSSxDQUFDLGtDQUFrQ0Y7Z0JBQ2pEO1lBQ0Y7UUFDRjtvQ0FBRyxFQUFFO0lBRUwsaURBQWlEO0lBQ2pEbkQsc0RBQWU7cUNBQUM7WUFDZCxJQUFJLElBQTZCLEVBQUU7Z0JBQ2pDLElBQUk7b0JBQ0YrQyxhQUFhTyxPQUFPLENBQUMsMEJBQTBCTCxLQUFLTSxTQUFTLENBQUN6QixNQUFNUCxRQUFRO2dCQUM5RSxFQUFFLE9BQU80QixPQUFPO29CQUNkQyxRQUFRQyxJQUFJLENBQUMsb0NBQW9DRjtnQkFDbkQ7WUFDRjtRQUNGO29DQUFHO1FBQUNyQixNQUFNUCxRQUFRO0tBQUM7SUFFbkIsc0NBQXNDO0lBQ3RDdkIsc0RBQWU7cUNBQUM7WUFDZCxJQUFJLEtBQTZCLElBQUk4QixNQUFNWCxpQkFBaUIsQ0FBQ3FDLE1BQU0sR0FBRyxHQUFHO2dCQUN2RSxNQUFNQzs2REFBYzt3QkFDbEIsSUFBSTs0QkFDRixNQUFNQyxjQUFjO2dDQUNsQnZDLG1CQUFtQlcsTUFBTVgsaUJBQWlCO2dDQUMxQ1EsV0FBV0csTUFBTUgsU0FBUztnQ0FDMUJDLFlBQVlFLE1BQU1GLFVBQVU7NEJBQzlCOzRCQUNBbUIsYUFBYU8sT0FBTyxDQUFDLDhCQUE4QkwsS0FBS00sU0FBUyxDQUFDRzt3QkFDcEUsRUFBRSxPQUFPUCxPQUFPOzRCQUNkQyxRQUFRQyxJQUFJLENBQUMsZ0NBQWdDRjt3QkFDL0M7b0JBQ0Y7O2dCQUVBLE1BQU1RLFdBQVdDLFlBQVlILGFBQWEsUUFBUSx3QkFBd0I7Z0JBQzFFO2lEQUFPLElBQU1JLGNBQWNGOztZQUM3QjtRQUNGO29DQUFHO1FBQUM3QixNQUFNWCxpQkFBaUI7UUFBRVcsTUFBTUgsU0FBUztRQUFFRyxNQUFNRixVQUFVO0tBQUM7SUFFL0QsMERBQTBEO0lBQzFELE1BQU1rQyxlQUFlOUQsb0RBQWE7aURBQUMsSUFBTztnQkFDeEM4QjtnQkFDQWM7WUFDRjtnREFBSTtRQUFDZDtRQUFPYztLQUFTO0lBRXJCLHFCQUNFLDhEQUFDSCxlQUFldUIsUUFBUTtRQUFDQyxPQUFPSDtrQkFDN0JuQjs7Ozs7O0FBR1AsRUFBRTtHQWhFV0Q7S0FBQUE7QUFrRWIseUNBQXlDO0FBQ2xDLE1BQU13QixhQUFhOztJQUN4QixNQUFNQyxVQUFVakUsaURBQVVBLENBQUN1QztJQUMzQixJQUFJLENBQUMwQixTQUFTO1FBQ1osTUFBTSxJQUFJQyxNQUFNO0lBQ2xCO0lBQ0EsT0FBT0Q7QUFDVCxFQUFFO0lBTldEO0FBUWIsMENBQTBDO0FBQ25DLE1BQU1HLG9CQUFvQjs7SUFDL0IsTUFBTSxFQUFFekIsUUFBUSxFQUFFLEdBQUdzQjtJQUVyQixNQUFNSSxxQkFBcUJ0RSx3REFBaUI7NkRBQUMsQ0FBQ3FDO1lBQzVDTyxTQUFTO2dCQUFFWixNQUFNO2dCQUF3QkMsU0FBU0k7WUFBTTtRQUMxRDs0REFBRztRQUFDTztLQUFTO0lBRWIsTUFBTTRCLHdCQUF3QnhFLHdEQUFpQjtnRUFBQyxDQUFDc0MsSUFBWUM7WUFDM0RLLFNBQVM7Z0JBQUVaLE1BQU07Z0JBQTJCQyxTQUFTO29CQUFFSztvQkFBSUM7Z0JBQVE7WUFBRTtRQUN2RTsrREFBRztRQUFDSztLQUFTO0lBRWIsTUFBTTZCLHdCQUF3QnpFLHdEQUFpQjtnRUFBQyxDQUFDc0M7WUFDL0NNLFNBQVM7Z0JBQUVaLE1BQU07Z0JBQTJCQyxTQUFTSztZQUFHO1FBQzFEOytEQUFHO1FBQUNNO0tBQVM7SUFFYixNQUFNOEIseUJBQXlCMUUsd0RBQWlCO2lFQUFDO1lBQy9DNEMsU0FBUztnQkFBRVosTUFBTTtZQUEyQjtRQUM5QztnRUFBRztRQUFDWTtLQUFTO0lBRWIsTUFBTStCLHFCQUFxQjNFLHdEQUFpQjs2REFBQyxDQUFDNEU7WUFDNUNoQyxTQUFTO2dCQUFFWixNQUFNO2dCQUF3QkMsU0FBUzJDO1lBQVc7UUFDL0Q7NERBQUc7UUFBQ2hDO0tBQVM7SUFFYixNQUFNaUMsdUJBQXVCN0Usd0RBQWlCOytEQUFDLENBQUM0RTtZQUM5Q2hDLFNBQVM7Z0JBQUVaLE1BQU07Z0JBQTBCQyxTQUFTMkM7WUFBVztRQUNqRTs4REFBRztRQUFDaEM7S0FBUztJQUViLE1BQU1rQyxvQkFBb0I5RSx3REFBaUI7NERBQUMsQ0FBQytFO1lBQzNDbkMsU0FBUztnQkFBRVosTUFBTTtnQkFBdUJDLFNBQVM4QztZQUFLO1FBQ3hEOzJEQUFHO1FBQUNuQztLQUFTO0lBRWIsTUFBTW9DLGlCQUFpQmhGLHdEQUFpQjt5REFBQyxDQUFDdUI7WUFDeENxQixTQUFTO2dCQUFFWixNQUFNO2dCQUFtQkMsU0FBU1Y7WUFBUztRQUN4RDt3REFBRztRQUFDcUI7S0FBUztJQUViLE1BQU1xQyxxQkFBcUJqRix3REFBaUI7NkRBQUMsQ0FBQ29CO1lBQzVDd0IsU0FBUztnQkFBRVosTUFBTTtnQkFBd0JDLFNBQVNiO1lBQVk7UUFDaEU7NERBQUc7UUFBQ3dCO0tBQVM7SUFFYixNQUFNc0MsdUJBQXVCbEYsd0RBQWlCOytEQUFDLENBQUNxQjtZQUM5Q3VCLFNBQVM7Z0JBQUVaLE1BQU07Z0JBQTBCQyxTQUFTWjtZQUFjO1FBQ3BFOzhEQUFHO1FBQUN1QjtLQUFTO0lBRWIsTUFBTXVDLHNCQUFzQm5GLHdEQUFpQjs4REFBQyxDQUFDb0Y7WUFDN0N4QyxTQUFTO2dCQUFFWixNQUFNO2dCQUEwQkMsU0FBU21EO1lBQVU7UUFDaEU7NkRBQUc7UUFBQ3hDO0tBQVM7SUFFYixNQUFNeUMsaUJBQWlCckYsd0RBQWlCO3lEQUFDLENBQUNtRDtZQUN4Q1AsU0FBUztnQkFBRVosTUFBTTtnQkFBb0JDLFNBQVNrQjtZQUFNO1FBQ3REO3dEQUFHO1FBQUNQO0tBQVM7SUFFYixNQUFNMEMsc0JBQXNCdEYsd0RBQWlCOzhEQUFDLENBQUNtRDtZQUM3Q1AsU0FBUztnQkFBRVosTUFBTTtnQkFBeUJDLFNBQVNrQjtZQUFNO1FBQzNEOzZEQUFHO1FBQUNQO0tBQVM7SUFFYixNQUFNMkMsY0FBY3ZGLHdEQUFpQjtzREFBQyxDQUFDbUQ7WUFDckNQLFNBQVM7Z0JBQUVaLE1BQU07Z0JBQWlCQyxTQUFTa0I7WUFBTTtRQUNuRDtxREFBRztRQUFDUDtLQUFTO0lBRWIsTUFBTTRDLGtCQUFrQnhGLHdEQUFpQjswREFBQztZQUN4QzRDLFNBQVM7Z0JBQUVaLE1BQU07WUFBbUI7UUFDdEM7eURBQUc7UUFBQ1k7S0FBUztJQUViLCtEQUErRDtJQUMvRCxPQUFPNUMsb0RBQWE7cUNBQUMsSUFBTztnQkFDMUJzRTtnQkFDQUU7Z0JBQ0FDO2dCQUNBQztnQkFDQUM7Z0JBQ0FFO2dCQUNBQztnQkFDQUU7Z0JBQ0FDO2dCQUNBQztnQkFDQUM7Z0JBQ0FFO2dCQUNBQztnQkFDQUM7Z0JBQ0FDO1lBQ0Y7b0NBQUk7UUFDRmxCO1FBQ0FFO1FBQ0FDO1FBQ0FDO1FBQ0FDO1FBQ0FFO1FBQ0FDO1FBQ0FFO1FBQ0FDO1FBQ0FDO1FBQ0FDO1FBQ0FFO1FBQ0FDO1FBQ0FDO1FBQ0FDO0tBQ0Q7QUFDSCxFQUFFO0lBakdXbkI7O1FBQ1VIOzs7QUFrR3ZCLDZDQUE2QztBQUN0QyxNQUFNdUIsc0JBQXNCOztJQUNqQyxNQUFNLEVBQUUzRCxLQUFLLEVBQUUsR0FBR29DO0lBRWxCLE1BQU13Qix1QkFBdUIxRix3REFBaUI7aUVBQUM7WUFDN0MsT0FBTzhCLE1BQU1kLGVBQWUsR0FBSWMsQ0FBQUEsTUFBTWIsaUJBQWlCLEdBQUcsTUFBTWEsTUFBTWIsaUJBQWlCLEdBQUcsRUFBQztRQUM3RjtnRUFBRztRQUFDYSxNQUFNZCxlQUFlO1FBQUVjLE1BQU1iLGlCQUFpQjtLQUFDO0lBRW5ELE1BQU0wRSx5QkFBeUIzRix3REFBaUI7bUVBQUM7WUFDL0MsTUFBTTRGLGlCQUFpQkY7WUFDdkIsT0FBT0UsZUFBZUMsSUFBSSxHQUFHQyxLQUFLLENBQUMsT0FBT3RELE1BQU07MkVBQUN1RCxDQUFBQSxPQUFRQSxLQUFLdkMsTUFBTSxHQUFHOzBFQUFHQSxNQUFNO1FBQ2xGO2tFQUFHO1FBQUNrQztLQUFxQjtJQUV6QixNQUFNTSxxQkFBcUJoRyx3REFBaUI7K0RBQUM7WUFDM0MsSUFBSThCLE1BQU1YLGlCQUFpQixDQUFDcUMsTUFBTSxLQUFLLEdBQUcsT0FBTztZQUNqRCxNQUFNeUMsYUFBYW5FLE1BQU1YLGlCQUFpQixDQUFDLEVBQUU7WUFDN0MsTUFBTStFLFlBQVlwRSxNQUFNWCxpQkFBaUIsQ0FBQ1csTUFBTVgsaUJBQWlCLENBQUNxQyxNQUFNLEdBQUcsRUFBRTtZQUM3RSxPQUFPMEMsVUFBVUMsU0FBUyxHQUFHRixXQUFXRSxTQUFTO1FBQ25EOzhEQUFHO1FBQUNyRSxNQUFNWCxpQkFBaUI7S0FBQztJQUU1QixNQUFNaUYsc0JBQXNCcEcsd0RBQWlCO2dFQUFDO1lBQzVDLE9BQU84QixNQUFNZCxlQUFlLENBQUN3QyxNQUFNLEdBQUcsS0FBSzFCLE1BQU1iLGlCQUFpQixDQUFDdUMsTUFBTSxHQUFHO1FBQzlFOytEQUFHO1FBQUMxQixNQUFNZCxlQUFlO1FBQUVjLE1BQU1iLGlCQUFpQjtLQUFDO0lBRW5ELE1BQU1vRixtQkFBbUJyRyx3REFBaUI7NkRBQUM7WUFDekMsTUFBTXNHLFFBQXdDLENBQUM7WUFDL0N4RSxNQUFNWCxpQkFBaUIsQ0FBQ29GLE9BQU87cUVBQUNsRSxDQUFBQTtvQkFDOUIsTUFBTW1FLE9BQU9uRSxNQUFNb0UsWUFBWSxJQUFJO29CQUNuQ0gsS0FBSyxDQUFDRSxLQUFLLEdBQUcsQ0FBQ0YsS0FBSyxDQUFDRSxLQUFLLElBQUksS0FBSztnQkFDckM7O1lBQ0EsT0FBT0Y7UUFDVDs0REFBRztRQUFDeEUsTUFBTVgsaUJBQWlCO0tBQUM7SUFFNUIsT0FBTztRQUNMdUU7UUFDQUM7UUFDQUs7UUFDQUk7UUFDQUM7SUFDRjtBQUNGLEVBQUU7SUF2Q1daOztRQUNPdkI7OztBQXdDcEIsaUVBQWV6QixjQUFjQSxFQUFDIiwic291cmNlcyI6WyJDOlxcVXNlcnNcXEFsaXl1U3VudXNpXFxPbmVEcml2ZSAtIFVHU00tTW9uYXJjaCBCdXNpbmVzcyBTY2hvb2wgR21iSFxcRG9jdW1lbnRzXFxjYXB0aW9ubmluamFcXGNhcHRpb24tbmluamEtbmV4dGpzXFxzcmNcXGNvbnRleHRzXFxDYXB0aW9uQ29udGV4dC50c3giXSwic291cmNlc0NvbnRlbnQiOlsiLyoqXG4gKiBDYXB0aW9uQ29udGV4dFxuICpcbiAqIEdsb2JhbCBjb250ZXh0IGZvciBtYW5hZ2luZyBjYXB0aW9uLXJlbGF0ZWQgc3RhdGUgYWNyb3NzIHRoZSBhcHBsaWNhdGlvbi5cbiAqIFByb3ZpZGVzIGNlbnRyYWxpemVkIHN0YXRlIG1hbmFnZW1lbnQgZm9yIHRyYW5zY3JpcHRzLCB0cmFuc2xhdGlvbnMsIGFuZCBzZXR0aW5ncy5cbiAqL1xuXG4ndXNlIGNsaWVudCc7XG5cbmltcG9ydCBSZWFjdCwgeyBjcmVhdGVDb250ZXh0LCB1c2VDb250ZXh0LCB1c2VSZWR1Y2VyLCBSZWFjdE5vZGUgfSBmcm9tICdyZWFjdCc7XG5cbi8vIFR5cGVzXG5leHBvcnQgaW50ZXJmYWNlIFRyYW5zY3JpcHRFbnRyeSB7XG4gIGlkOiBzdHJpbmc7XG4gIHRleHQ6IHN0cmluZztcbiAgdGltZXN0YW1wOiBudW1iZXI7XG4gIGR1cmF0aW9uPzogbnVtYmVyO1xuICB0cmFuc2xhdGVkVGV4dD86IHN0cmluZztcbiAgaXNJbnRlcmltOiBib29sZWFuO1xuICBzZXNzaW9uSWQ/OiBzdHJpbmc7XG4gIGxhbmd1YWdlQ29kZT86IHN0cmluZztcbiAgY29uZmlkZW5jZT86IG51bWJlcjtcbn1cblxuZXhwb3J0IGludGVyZmFjZSBDYXB0aW9uU2V0dGluZ3Mge1xuICBzcGVlY2hMYW5ndWFnZTogc3RyaW5nO1xuICB0cmFuc2xhdGlvbkVuYWJsZWQ6IGJvb2xlYW47XG4gIHNvdXJjZUxhbmd1YWdlOiBzdHJpbmc7XG4gIHRhcmdldExhbmd1YWdlOiBzdHJpbmc7XG4gIHR0c0VuYWJsZWQ6IGJvb2xlYW47XG4gIHR0c0F1dG9QbGF5OiBib29sZWFuO1xuICBzZWxlY3RlZFZvaWNlOiBzdHJpbmc7XG4gIHR0c1JhdGU6IG51bWJlcjtcbiAgdHRzUGl0Y2g6IG51bWJlcjtcbiAgdHRzVm9sdW1lOiBudW1iZXI7XG59XG5cbmV4cG9ydCBpbnRlcmZhY2UgQ2FwdGlvblN0YXRlIHtcbiAgLy8gQ3VycmVudCB0cmFuc2NyaXB0IGRhdGFcbiAgZmluYWxUcmFuc2NyaXB0OiBzdHJpbmc7XG4gIGludGVyaW1UcmFuc2NyaXB0OiBzdHJpbmc7XG4gIHRyYW5zbGF0ZWRUZXh0OiBzdHJpbmc7XG4gIFxuICAvLyBIaXN0b3JpY2FsIGRhdGFcbiAgdHJhbnNjcmlwdEhpc3Rvcnk6IFRyYW5zY3JpcHRFbnRyeVtdO1xuICBcbiAgLy8gU3RhdHVzIGZsYWdzXG4gIGlzUmVjb3JkaW5nOiBib29sZWFuO1xuICBpc1RyYW5zbGF0aW5nOiBib29sZWFuO1xuICBpc1RUU1BsYXlpbmc6IGJvb2xlYW47XG4gIFxuICAvLyBTZXR0aW5nc1xuICBzZXR0aW5nczogQ2FwdGlvblNldHRpbmdzO1xuICBcbiAgLy8gRXJyb3Igc3RhdGVzXG4gIHNwZWVjaEVycm9yOiBzdHJpbmcgfCBudWxsO1xuICB0cmFuc2xhdGlvbkVycm9yOiBzdHJpbmcgfCBudWxsO1xuICB0dHNFcnJvcjogc3RyaW5nIHwgbnVsbDtcbiAgXG4gIC8vIE1ldGFkYXRhXG4gIHNlc3Npb25JZDogc3RyaW5nO1xuICBsYXN0VXBkYXRlOiBudW1iZXI7XG59XG5cbi8vIEFjdGlvbiB0eXBlc1xuZXhwb3J0IHR5cGUgQ2FwdGlvbkFjdGlvbiA9XG4gIHwgeyB0eXBlOiAnU0VUX0ZJTkFMX1RSQU5TQ1JJUFQnOyBwYXlsb2FkOiBzdHJpbmcgfVxuICB8IHsgdHlwZTogJ1NFVF9JTlRFUklNX1RSQU5TQ1JJUFQnOyBwYXlsb2FkOiBzdHJpbmcgfVxuICB8IHsgdHlwZTogJ1NFVF9UUkFOU0xBVEVEX1RFWFQnOyBwYXlsb2FkOiBzdHJpbmcgfVxuICB8IHsgdHlwZTogJ0FERF9UUkFOU0NSSVBUX0VOVFJZJzsgcGF5bG9hZDogVHJhbnNjcmlwdEVudHJ5IH1cbiAgfCB7IHR5cGU6ICdVUERBVEVfVFJBTlNDUklQVF9FTlRSWSc7IHBheWxvYWQ6IHsgaWQ6IHN0cmluZzsgdXBkYXRlczogUGFydGlhbDxUcmFuc2NyaXB0RW50cnk+IH0gfVxuICB8IHsgdHlwZTogJ0RFTEVURV9UUkFOU0NSSVBUX0VOVFJZJzsgcGF5bG9hZDogc3RyaW5nIH1cbiAgfCB7IHR5cGU6ICdDTEVBUl9UUkFOU0NSSVBUX0hJU1RPUlknIH1cbiAgfCB7IHR5cGU6ICdTRVRfUkVDT1JESU5HX1NUQVRVUyc7IHBheWxvYWQ6IGJvb2xlYW4gfVxuICB8IHsgdHlwZTogJ1NFVF9UUkFOU0xBVElOR19TVEFUVVMnOyBwYXlsb2FkOiBib29sZWFuIH1cbiAgfCB7IHR5cGU6ICdTRVRfVFRTX1BMQVlJTkdfU1RBVFVTJzsgcGF5bG9hZDogYm9vbGVhbiB9XG4gIHwgeyB0eXBlOiAnVVBEQVRFX1NFVFRJTkdTJzsgcGF5bG9hZDogUGFydGlhbDxDYXB0aW9uU2V0dGluZ3M+IH1cbiAgfCB7IHR5cGU6ICdTRVRfU1BFRUNIX0VSUk9SJzsgcGF5bG9hZDogc3RyaW5nIHwgbnVsbCB9XG4gIHwgeyB0eXBlOiAnU0VUX1RSQU5TTEFUSU9OX0VSUk9SJzsgcGF5bG9hZDogc3RyaW5nIHwgbnVsbCB9XG4gIHwgeyB0eXBlOiAnU0VUX1RUU19FUlJPUic7IHBheWxvYWQ6IHN0cmluZyB8IG51bGwgfVxuICB8IHsgdHlwZTogJ1JFU0VUX1RSQU5TQ1JJUFQnIH1cbiAgfCB7IHR5cGU6ICdMT0FEX1NFU1NJT04nOyBwYXlsb2FkOiBQYXJ0aWFsPENhcHRpb25TdGF0ZT4gfVxuICB8IHsgdHlwZTogJ1NFVF9TRVNTSU9OX0lEJzsgcGF5bG9hZDogc3RyaW5nIH07XG5cbi8vIEluaXRpYWwgc3RhdGVcbmNvbnN0IGluaXRpYWxTZXR0aW5nczogQ2FwdGlvblNldHRpbmdzID0ge1xuICBzcGVlY2hMYW5ndWFnZTogJ2VuLVVTJyxcbiAgdHJhbnNsYXRpb25FbmFibGVkOiBmYWxzZSxcbiAgc291cmNlTGFuZ3VhZ2U6ICdhdXRvJyxcbiAgdGFyZ2V0TGFuZ3VhZ2U6ICdlbicsXG4gIHR0c0VuYWJsZWQ6IGZhbHNlLFxuICB0dHNBdXRvUGxheTogZmFsc2UsXG4gIHNlbGVjdGVkVm9pY2U6ICcnLFxuICB0dHNSYXRlOiAxLFxuICB0dHNQaXRjaDogMSxcbiAgdHRzVm9sdW1lOiAxLFxufTtcblxuY29uc3QgaW5pdGlhbFN0YXRlOiBDYXB0aW9uU3RhdGUgPSB7XG4gIGZpbmFsVHJhbnNjcmlwdDogJycsXG4gIGludGVyaW1UcmFuc2NyaXB0OiAnJyxcbiAgdHJhbnNsYXRlZFRleHQ6ICcnLFxuICB0cmFuc2NyaXB0SGlzdG9yeTogW10sXG4gIGlzUmVjb3JkaW5nOiBmYWxzZSxcbiAgaXNUcmFuc2xhdGluZzogZmFsc2UsXG4gIGlzVFRTUGxheWluZzogZmFsc2UsXG4gIHNldHRpbmdzOiBpbml0aWFsU2V0dGluZ3MsXG4gIHNwZWVjaEVycm9yOiBudWxsLFxuICB0cmFuc2xhdGlvbkVycm9yOiBudWxsLFxuICB0dHNFcnJvcjogbnVsbCxcbiAgc2Vzc2lvbklkOiAnc2Vzc2lvbi1pbml0aWFsJyxcbiAgbGFzdFVwZGF0ZTogMCxcbn07XG5cbi8vIFJlZHVjZXJcbmNvbnN0IGNhcHRpb25SZWR1Y2VyID0gKHN0YXRlOiBDYXB0aW9uU3RhdGUsIGFjdGlvbjogQ2FwdGlvbkFjdGlvbik6IENhcHRpb25TdGF0ZSA9PiB7XG4gIHN3aXRjaCAoYWN0aW9uLnR5cGUpIHtcbiAgICBjYXNlICdTRVRfRklOQUxfVFJBTlNDUklQVCc6XG4gICAgICByZXR1cm4ge1xuICAgICAgICAuLi5zdGF0ZSxcbiAgICAgICAgZmluYWxUcmFuc2NyaXB0OiBhY3Rpb24ucGF5bG9hZCxcbiAgICAgICAgbGFzdFVwZGF0ZTogRGF0ZS5ub3coKSxcbiAgICAgIH07XG5cbiAgICBjYXNlICdTRVRfSU5URVJJTV9UUkFOU0NSSVBUJzpcbiAgICAgIHJldHVybiB7XG4gICAgICAgIC4uLnN0YXRlLFxuICAgICAgICBpbnRlcmltVHJhbnNjcmlwdDogYWN0aW9uLnBheWxvYWQsXG4gICAgICAgIGxhc3RVcGRhdGU6IERhdGUubm93KCksXG4gICAgICB9O1xuXG4gICAgY2FzZSAnU0VUX1RSQU5TTEFURURfVEVYVCc6XG4gICAgICByZXR1cm4ge1xuICAgICAgICAuLi5zdGF0ZSxcbiAgICAgICAgdHJhbnNsYXRlZFRleHQ6IGFjdGlvbi5wYXlsb2FkLFxuICAgICAgICBsYXN0VXBkYXRlOiBEYXRlLm5vdygpLFxuICAgICAgfTtcblxuICAgIGNhc2UgJ0FERF9UUkFOU0NSSVBUX0VOVFJZJzpcbiAgICAgIHJldHVybiB7XG4gICAgICAgIC4uLnN0YXRlLFxuICAgICAgICB0cmFuc2NyaXB0SGlzdG9yeTogWy4uLnN0YXRlLnRyYW5zY3JpcHRIaXN0b3J5LCBhY3Rpb24ucGF5bG9hZF0sXG4gICAgICAgIGxhc3RVcGRhdGU6IERhdGUubm93KCksXG4gICAgICB9O1xuXG4gICAgY2FzZSAnVVBEQVRFX1RSQU5TQ1JJUFRfRU5UUlknOlxuICAgICAgcmV0dXJuIHtcbiAgICAgICAgLi4uc3RhdGUsXG4gICAgICAgIHRyYW5zY3JpcHRIaXN0b3J5OiBzdGF0ZS50cmFuc2NyaXB0SGlzdG9yeS5tYXAoZW50cnkgPT5cbiAgICAgICAgICBlbnRyeS5pZCA9PT0gYWN0aW9uLnBheWxvYWQuaWRcbiAgICAgICAgICAgID8geyAuLi5lbnRyeSwgLi4uYWN0aW9uLnBheWxvYWQudXBkYXRlcyB9XG4gICAgICAgICAgICA6IGVudHJ5XG4gICAgICAgICksXG4gICAgICAgIGxhc3RVcGRhdGU6IERhdGUubm93KCksXG4gICAgICB9O1xuXG4gICAgY2FzZSAnREVMRVRFX1RSQU5TQ1JJUFRfRU5UUlknOlxuICAgICAgcmV0dXJuIHtcbiAgICAgICAgLi4uc3RhdGUsXG4gICAgICAgIHRyYW5zY3JpcHRIaXN0b3J5OiBzdGF0ZS50cmFuc2NyaXB0SGlzdG9yeS5maWx0ZXIoXG4gICAgICAgICAgZW50cnkgPT4gZW50cnkuaWQgIT09IGFjdGlvbi5wYXlsb2FkXG4gICAgICAgICksXG4gICAgICAgIGxhc3RVcGRhdGU6IERhdGUubm93KCksXG4gICAgICB9O1xuXG4gICAgY2FzZSAnQ0xFQVJfVFJBTlNDUklQVF9ISVNUT1JZJzpcbiAgICAgIHJldHVybiB7XG4gICAgICAgIC4uLnN0YXRlLFxuICAgICAgICB0cmFuc2NyaXB0SGlzdG9yeTogW10sXG4gICAgICAgIGxhc3RVcGRhdGU6IERhdGUubm93KCksXG4gICAgICB9O1xuXG4gICAgY2FzZSAnU0VUX1JFQ09SRElOR19TVEFUVVMnOlxuICAgICAgcmV0dXJuIHtcbiAgICAgICAgLi4uc3RhdGUsXG4gICAgICAgIGlzUmVjb3JkaW5nOiBhY3Rpb24ucGF5bG9hZCxcbiAgICAgICAgbGFzdFVwZGF0ZTogRGF0ZS5ub3coKSxcbiAgICAgIH07XG5cbiAgICBjYXNlICdTRVRfVFJBTlNMQVRJTkdfU1RBVFVTJzpcbiAgICAgIHJldHVybiB7XG4gICAgICAgIC4uLnN0YXRlLFxuICAgICAgICBpc1RyYW5zbGF0aW5nOiBhY3Rpb24ucGF5bG9hZCxcbiAgICAgICAgbGFzdFVwZGF0ZTogRGF0ZS5ub3coKSxcbiAgICAgIH07XG5cbiAgICBjYXNlICdTRVRfVFRTX1BMQVlJTkdfU1RBVFVTJzpcbiAgICAgIHJldHVybiB7XG4gICAgICAgIC4uLnN0YXRlLFxuICAgICAgICBpc1RUU1BsYXlpbmc6IGFjdGlvbi5wYXlsb2FkLFxuICAgICAgICBsYXN0VXBkYXRlOiBEYXRlLm5vdygpLFxuICAgICAgfTtcblxuICAgIGNhc2UgJ1VQREFURV9TRVRUSU5HUyc6XG4gICAgICByZXR1cm4ge1xuICAgICAgICAuLi5zdGF0ZSxcbiAgICAgICAgc2V0dGluZ3M6IHsgLi4uc3RhdGUuc2V0dGluZ3MsIC4uLmFjdGlvbi5wYXlsb2FkIH0sXG4gICAgICAgIGxhc3RVcGRhdGU6IERhdGUubm93KCksXG4gICAgICB9O1xuXG4gICAgY2FzZSAnU0VUX1NQRUVDSF9FUlJPUic6XG4gICAgICByZXR1cm4ge1xuICAgICAgICAuLi5zdGF0ZSxcbiAgICAgICAgc3BlZWNoRXJyb3I6IGFjdGlvbi5wYXlsb2FkLFxuICAgICAgICBsYXN0VXBkYXRlOiBEYXRlLm5vdygpLFxuICAgICAgfTtcblxuICAgIGNhc2UgJ1NFVF9UUkFOU0xBVElPTl9FUlJPUic6XG4gICAgICByZXR1cm4ge1xuICAgICAgICAuLi5zdGF0ZSxcbiAgICAgICAgdHJhbnNsYXRpb25FcnJvcjogYWN0aW9uLnBheWxvYWQsXG4gICAgICAgIGxhc3RVcGRhdGU6IERhdGUubm93KCksXG4gICAgICB9O1xuXG4gICAgY2FzZSAnU0VUX1RUU19FUlJPUic6XG4gICAgICByZXR1cm4ge1xuICAgICAgICAuLi5zdGF0ZSxcbiAgICAgICAgdHRzRXJyb3I6IGFjdGlvbi5wYXlsb2FkLFxuICAgICAgICBsYXN0VXBkYXRlOiBEYXRlLm5vdygpLFxuICAgICAgfTtcblxuICAgIGNhc2UgJ1JFU0VUX1RSQU5TQ1JJUFQnOlxuICAgICAgcmV0dXJuIHtcbiAgICAgICAgLi4uc3RhdGUsXG4gICAgICAgIGZpbmFsVHJhbnNjcmlwdDogJycsXG4gICAgICAgIGludGVyaW1UcmFuc2NyaXB0OiAnJyxcbiAgICAgICAgdHJhbnNsYXRlZFRleHQ6ICcnLFxuICAgICAgICBzcGVlY2hFcnJvcjogbnVsbCxcbiAgICAgICAgdHJhbnNsYXRpb25FcnJvcjogbnVsbCxcbiAgICAgICAgbGFzdFVwZGF0ZTogRGF0ZS5ub3coKSxcbiAgICAgIH07XG5cbiAgICBjYXNlICdMT0FEX1NFU1NJT04nOlxuICAgICAgcmV0dXJuIHtcbiAgICAgICAgLi4uc3RhdGUsXG4gICAgICAgIC4uLmFjdGlvbi5wYXlsb2FkLFxuICAgICAgICBsYXN0VXBkYXRlOiBEYXRlLm5vdygpLFxuICAgICAgfTtcblxuICAgIGNhc2UgJ1NFVF9TRVNTSU9OX0lEJzpcbiAgICAgIHJldHVybiB7XG4gICAgICAgIC4uLnN0YXRlLFxuICAgICAgICBzZXNzaW9uSWQ6IGFjdGlvbi5wYXlsb2FkLFxuICAgICAgICBsYXN0VXBkYXRlOiBEYXRlLm5vdygpLFxuICAgICAgfTtcblxuICAgIGRlZmF1bHQ6XG4gICAgICByZXR1cm4gc3RhdGU7XG4gIH1cbn07XG5cbi8vIENvbnRleHRcbmNvbnN0IENhcHRpb25Db250ZXh0ID0gY3JlYXRlQ29udGV4dDx7XG4gIHN0YXRlOiBDYXB0aW9uU3RhdGU7XG4gIGRpc3BhdGNoOiBSZWFjdC5EaXNwYXRjaDxDYXB0aW9uQWN0aW9uPjtcbn0gfCBudWxsPihudWxsKTtcblxuLy8gUHJvdmlkZXIgY29tcG9uZW50XG5pbnRlcmZhY2UgQ2FwdGlvblByb3ZpZGVyUHJvcHMge1xuICBjaGlsZHJlbjogUmVhY3ROb2RlO1xufVxuXG5leHBvcnQgY29uc3QgQ2FwdGlvblByb3ZpZGVyOiBSZWFjdC5GQzxDYXB0aW9uUHJvdmlkZXJQcm9wcz4gPSAoeyBjaGlsZHJlbiB9KSA9PiB7XG4gIGNvbnN0IFtzdGF0ZSwgZGlzcGF0Y2hdID0gdXNlUmVkdWNlcihjYXB0aW9uUmVkdWNlciwgaW5pdGlhbFN0YXRlKTtcblxuICAvLyBJbml0aWFsaXplIGNsaWVudC1zaWRlIGRhdGEgYW5kIGxvYWQgc2F2ZWQgc2V0dGluZ3NcbiAgUmVhY3QudXNlRWZmZWN0KCgpID0+IHtcbiAgICBpZiAodHlwZW9mIHdpbmRvdyAhPT0gJ3VuZGVmaW5lZCcpIHtcbiAgICAgIC8vIFNldCB1bmlxdWUgc2Vzc2lvbiBJRCBvbiBjbGllbnQgc2lkZSB0byBhdm9pZCBoeWRyYXRpb24gbWlzbWF0Y2hcbiAgICAgIGRpc3BhdGNoKHsgdHlwZTogJ1NFVF9TRVNTSU9OX0lEJywgcGF5bG9hZDogYHNlc3Npb24tJHtEYXRlLm5vdygpfWAgfSk7XG5cbiAgICAgIHRyeSB7XG4gICAgICAgIGNvbnN0IHNhdmVkU2V0dGluZ3MgPSBsb2NhbFN0b3JhZ2UuZ2V0SXRlbSgnY2FwdGlvbi1uaW5qYS1zZXR0aW5ncycpO1xuICAgICAgICBpZiAoc2F2ZWRTZXR0aW5ncykge1xuICAgICAgICAgIGNvbnN0IHNldHRpbmdzID0gSlNPTi5wYXJzZShzYXZlZFNldHRpbmdzKTtcbiAgICAgICAgICBkaXNwYXRjaCh7IHR5cGU6ICdVUERBVEVfU0VUVElOR1MnLCBwYXlsb2FkOiBzZXR0aW5ncyB9KTtcbiAgICAgICAgfVxuICAgICAgfSBjYXRjaCAoZXJyb3IpIHtcbiAgICAgICAgY29uc29sZS53YXJuKCdGYWlsZWQgdG8gbG9hZCBzYXZlZCBzZXR0aW5nczonLCBlcnJvcik7XG4gICAgICB9XG4gICAgfVxuICB9LCBbXSk7XG5cbiAgLy8gU2F2ZSBzZXR0aW5ncyB0byBsb2NhbFN0b3JhZ2Ugd2hlbiB0aGV5IGNoYW5nZVxuICBSZWFjdC51c2VFZmZlY3QoKCkgPT4ge1xuICAgIGlmICh0eXBlb2Ygd2luZG93ICE9PSAndW5kZWZpbmVkJykge1xuICAgICAgdHJ5IHtcbiAgICAgICAgbG9jYWxTdG9yYWdlLnNldEl0ZW0oJ2NhcHRpb24tbmluamEtc2V0dGluZ3MnLCBKU09OLnN0cmluZ2lmeShzdGF0ZS5zZXR0aW5ncykpO1xuICAgICAgfSBjYXRjaCAoZXJyb3IpIHtcbiAgICAgICAgY29uc29sZS53YXJuKCdGYWlsZWQgdG8gc2F2ZSBjYXB0aW9uIHNldHRpbmdzOicsIGVycm9yKTtcbiAgICAgIH1cbiAgICB9XG4gIH0sIFtzdGF0ZS5zZXR0aW5nc10pO1xuXG4gIC8vIEF1dG8tc2F2ZSBzZXNzaW9uIGRhdGEgcGVyaW9kaWNhbGx5XG4gIFJlYWN0LnVzZUVmZmVjdCgoKSA9PiB7XG4gICAgaWYgKHR5cGVvZiB3aW5kb3cgIT09ICd1bmRlZmluZWQnICYmIHN0YXRlLnRyYW5zY3JpcHRIaXN0b3J5Lmxlbmd0aCA+IDApIHtcbiAgICAgIGNvbnN0IHNhdmVTZXNzaW9uID0gKCkgPT4ge1xuICAgICAgICB0cnkge1xuICAgICAgICAgIGNvbnN0IHNlc3Npb25EYXRhID0ge1xuICAgICAgICAgICAgdHJhbnNjcmlwdEhpc3Rvcnk6IHN0YXRlLnRyYW5zY3JpcHRIaXN0b3J5LFxuICAgICAgICAgICAgc2Vzc2lvbklkOiBzdGF0ZS5zZXNzaW9uSWQsXG4gICAgICAgICAgICBsYXN0VXBkYXRlOiBzdGF0ZS5sYXN0VXBkYXRlLFxuICAgICAgICAgIH07XG4gICAgICAgICAgbG9jYWxTdG9yYWdlLnNldEl0ZW0oJ2NhcHRpb24tbmluamEtbGFzdC1zZXNzaW9uJywgSlNPTi5zdHJpbmdpZnkoc2Vzc2lvbkRhdGEpKTtcbiAgICAgICAgfSBjYXRjaCAoZXJyb3IpIHtcbiAgICAgICAgICBjb25zb2xlLndhcm4oJ0ZhaWxlZCB0byBzYXZlIHNlc3Npb24gZGF0YTonLCBlcnJvcik7XG4gICAgICAgIH1cbiAgICAgIH07XG5cbiAgICAgIGNvbnN0IGludGVydmFsID0gc2V0SW50ZXJ2YWwoc2F2ZVNlc3Npb24sIDMwMDAwKTsgLy8gU2F2ZSBldmVyeSAzMCBzZWNvbmRzXG4gICAgICByZXR1cm4gKCkgPT4gY2xlYXJJbnRlcnZhbChpbnRlcnZhbCk7XG4gICAgfVxuICB9LCBbc3RhdGUudHJhbnNjcmlwdEhpc3RvcnksIHN0YXRlLnNlc3Npb25JZCwgc3RhdGUubGFzdFVwZGF0ZV0pO1xuXG4gIC8vIE1lbW9pemUgY29udGV4dCB2YWx1ZSB0byBwcmV2ZW50IHVubmVjZXNzYXJ5IHJlLXJlbmRlcnNcbiAgY29uc3QgY29udGV4dFZhbHVlID0gUmVhY3QudXNlTWVtbygoKSA9PiAoe1xuICAgIHN0YXRlLFxuICAgIGRpc3BhdGNoXG4gIH0pLCBbc3RhdGUsIGRpc3BhdGNoXSk7XG5cbiAgcmV0dXJuIChcbiAgICA8Q2FwdGlvbkNvbnRleHQuUHJvdmlkZXIgdmFsdWU9e2NvbnRleHRWYWx1ZX0+XG4gICAgICB7Y2hpbGRyZW59XG4gICAgPC9DYXB0aW9uQ29udGV4dC5Qcm92aWRlcj5cbiAgKTtcbn07XG5cbi8vIEN1c3RvbSBob29rIHRvIHVzZSB0aGUgY2FwdGlvbiBjb250ZXh0XG5leHBvcnQgY29uc3QgdXNlQ2FwdGlvbiA9ICgpID0+IHtcbiAgY29uc3QgY29udGV4dCA9IHVzZUNvbnRleHQoQ2FwdGlvbkNvbnRleHQpO1xuICBpZiAoIWNvbnRleHQpIHtcbiAgICB0aHJvdyBuZXcgRXJyb3IoJ3VzZUNhcHRpb24gbXVzdCBiZSB1c2VkIHdpdGhpbiBhIENhcHRpb25Qcm92aWRlcicpO1xuICB9XG4gIHJldHVybiBjb250ZXh0O1xufTtcblxuLy8gSGVscGVyIGhvb2tzIGZvciBzcGVjaWZpYyBmdW5jdGlvbmFsaXR5XG5leHBvcnQgY29uc3QgdXNlQ2FwdGlvbkFjdGlvbnMgPSAoKSA9PiB7XG4gIGNvbnN0IHsgZGlzcGF0Y2ggfSA9IHVzZUNhcHRpb24oKTtcblxuICBjb25zdCBhZGRUcmFuc2NyaXB0RW50cnkgPSBSZWFjdC51c2VDYWxsYmFjaygoZW50cnk6IFRyYW5zY3JpcHRFbnRyeSkgPT4ge1xuICAgIGRpc3BhdGNoKHsgdHlwZTogJ0FERF9UUkFOU0NSSVBUX0VOVFJZJywgcGF5bG9hZDogZW50cnkgfSk7XG4gIH0sIFtkaXNwYXRjaF0pO1xuXG4gIGNvbnN0IHVwZGF0ZVRyYW5zY3JpcHRFbnRyeSA9IFJlYWN0LnVzZUNhbGxiYWNrKChpZDogc3RyaW5nLCB1cGRhdGVzOiBQYXJ0aWFsPFRyYW5zY3JpcHRFbnRyeT4pID0+IHtcbiAgICBkaXNwYXRjaCh7IHR5cGU6ICdVUERBVEVfVFJBTlNDUklQVF9FTlRSWScsIHBheWxvYWQ6IHsgaWQsIHVwZGF0ZXMgfSB9KTtcbiAgfSwgW2Rpc3BhdGNoXSk7XG5cbiAgY29uc3QgZGVsZXRlVHJhbnNjcmlwdEVudHJ5ID0gUmVhY3QudXNlQ2FsbGJhY2soKGlkOiBzdHJpbmcpID0+IHtcbiAgICBkaXNwYXRjaCh7IHR5cGU6ICdERUxFVEVfVFJBTlNDUklQVF9FTlRSWScsIHBheWxvYWQ6IGlkIH0pO1xuICB9LCBbZGlzcGF0Y2hdKTtcblxuICBjb25zdCBjbGVhclRyYW5zY3JpcHRIaXN0b3J5ID0gUmVhY3QudXNlQ2FsbGJhY2soKCkgPT4ge1xuICAgIGRpc3BhdGNoKHsgdHlwZTogJ0NMRUFSX1RSQU5TQ1JJUFRfSElTVE9SWScgfSk7XG4gIH0sIFtkaXNwYXRjaF0pO1xuXG4gIGNvbnN0IHNldEZpbmFsVHJhbnNjcmlwdCA9IFJlYWN0LnVzZUNhbGxiYWNrKCh0cmFuc2NyaXB0OiBzdHJpbmcpID0+IHtcbiAgICBkaXNwYXRjaCh7IHR5cGU6ICdTRVRfRklOQUxfVFJBTlNDUklQVCcsIHBheWxvYWQ6IHRyYW5zY3JpcHQgfSk7XG4gIH0sIFtkaXNwYXRjaF0pO1xuXG4gIGNvbnN0IHNldEludGVyaW1UcmFuc2NyaXB0ID0gUmVhY3QudXNlQ2FsbGJhY2soKHRyYW5zY3JpcHQ6IHN0cmluZykgPT4ge1xuICAgIGRpc3BhdGNoKHsgdHlwZTogJ1NFVF9JTlRFUklNX1RSQU5TQ1JJUFQnLCBwYXlsb2FkOiB0cmFuc2NyaXB0IH0pO1xuICB9LCBbZGlzcGF0Y2hdKTtcblxuICBjb25zdCBzZXRUcmFuc2xhdGVkVGV4dCA9IFJlYWN0LnVzZUNhbGxiYWNrKCh0ZXh0OiBzdHJpbmcpID0+IHtcbiAgICBkaXNwYXRjaCh7IHR5cGU6ICdTRVRfVFJBTlNMQVRFRF9URVhUJywgcGF5bG9hZDogdGV4dCB9KTtcbiAgfSwgW2Rpc3BhdGNoXSk7XG5cbiAgY29uc3QgdXBkYXRlU2V0dGluZ3MgPSBSZWFjdC51c2VDYWxsYmFjaygoc2V0dGluZ3M6IFBhcnRpYWw8Q2FwdGlvblNldHRpbmdzPikgPT4ge1xuICAgIGRpc3BhdGNoKHsgdHlwZTogJ1VQREFURV9TRVRUSU5HUycsIHBheWxvYWQ6IHNldHRpbmdzIH0pO1xuICB9LCBbZGlzcGF0Y2hdKTtcblxuICBjb25zdCBzZXRSZWNvcmRpbmdTdGF0dXMgPSBSZWFjdC51c2VDYWxsYmFjaygoaXNSZWNvcmRpbmc6IGJvb2xlYW4pID0+IHtcbiAgICBkaXNwYXRjaCh7IHR5cGU6ICdTRVRfUkVDT1JESU5HX1NUQVRVUycsIHBheWxvYWQ6IGlzUmVjb3JkaW5nIH0pO1xuICB9LCBbZGlzcGF0Y2hdKTtcblxuICBjb25zdCBzZXRUcmFuc2xhdGluZ1N0YXR1cyA9IFJlYWN0LnVzZUNhbGxiYWNrKChpc1RyYW5zbGF0aW5nOiBib29sZWFuKSA9PiB7XG4gICAgZGlzcGF0Y2goeyB0eXBlOiAnU0VUX1RSQU5TTEFUSU5HX1NUQVRVUycsIHBheWxvYWQ6IGlzVHJhbnNsYXRpbmcgfSk7XG4gIH0sIFtkaXNwYXRjaF0pO1xuXG4gIGNvbnN0IHNldFRUU1BsYXlpbmdTdGF0dXMgPSBSZWFjdC51c2VDYWxsYmFjaygoaXNQbGF5aW5nOiBib29sZWFuKSA9PiB7XG4gICAgZGlzcGF0Y2goeyB0eXBlOiAnU0VUX1RUU19QTEFZSU5HX1NUQVRVUycsIHBheWxvYWQ6IGlzUGxheWluZyB9KTtcbiAgfSwgW2Rpc3BhdGNoXSk7XG5cbiAgY29uc3Qgc2V0U3BlZWNoRXJyb3IgPSBSZWFjdC51c2VDYWxsYmFjaygoZXJyb3I6IHN0cmluZyB8IG51bGwpID0+IHtcbiAgICBkaXNwYXRjaCh7IHR5cGU6ICdTRVRfU1BFRUNIX0VSUk9SJywgcGF5bG9hZDogZXJyb3IgfSk7XG4gIH0sIFtkaXNwYXRjaF0pO1xuXG4gIGNvbnN0IHNldFRyYW5zbGF0aW9uRXJyb3IgPSBSZWFjdC51c2VDYWxsYmFjaygoZXJyb3I6IHN0cmluZyB8IG51bGwpID0+IHtcbiAgICBkaXNwYXRjaCh7IHR5cGU6ICdTRVRfVFJBTlNMQVRJT05fRVJST1InLCBwYXlsb2FkOiBlcnJvciB9KTtcbiAgfSwgW2Rpc3BhdGNoXSk7XG5cbiAgY29uc3Qgc2V0VFRTRXJyb3IgPSBSZWFjdC51c2VDYWxsYmFjaygoZXJyb3I6IHN0cmluZyB8IG51bGwpID0+IHtcbiAgICBkaXNwYXRjaCh7IHR5cGU6ICdTRVRfVFRTX0VSUk9SJywgcGF5bG9hZDogZXJyb3IgfSk7XG4gIH0sIFtkaXNwYXRjaF0pO1xuXG4gIGNvbnN0IHJlc2V0VHJhbnNjcmlwdCA9IFJlYWN0LnVzZUNhbGxiYWNrKCgpID0+IHtcbiAgICBkaXNwYXRjaCh7IHR5cGU6ICdSRVNFVF9UUkFOU0NSSVBUJyB9KTtcbiAgfSwgW2Rpc3BhdGNoXSk7XG5cbiAgLy8gTWVtb2l6ZSB0aGUgYWN0aW9ucyBvYmplY3QgdG8gcHJldmVudCB1bm5lY2Vzc2FyeSByZS1yZW5kZXJzXG4gIHJldHVybiBSZWFjdC51c2VNZW1vKCgpID0+ICh7XG4gICAgYWRkVHJhbnNjcmlwdEVudHJ5LFxuICAgIHVwZGF0ZVRyYW5zY3JpcHRFbnRyeSxcbiAgICBkZWxldGVUcmFuc2NyaXB0RW50cnksXG4gICAgY2xlYXJUcmFuc2NyaXB0SGlzdG9yeSxcbiAgICBzZXRGaW5hbFRyYW5zY3JpcHQsXG4gICAgc2V0SW50ZXJpbVRyYW5zY3JpcHQsXG4gICAgc2V0VHJhbnNsYXRlZFRleHQsXG4gICAgdXBkYXRlU2V0dGluZ3MsXG4gICAgc2V0UmVjb3JkaW5nU3RhdHVzLFxuICAgIHNldFRyYW5zbGF0aW5nU3RhdHVzLFxuICAgIHNldFRUU1BsYXlpbmdTdGF0dXMsXG4gICAgc2V0U3BlZWNoRXJyb3IsXG4gICAgc2V0VHJhbnNsYXRpb25FcnJvcixcbiAgICBzZXRUVFNFcnJvcixcbiAgICByZXNldFRyYW5zY3JpcHQsXG4gIH0pLCBbXG4gICAgYWRkVHJhbnNjcmlwdEVudHJ5LFxuICAgIHVwZGF0ZVRyYW5zY3JpcHRFbnRyeSxcbiAgICBkZWxldGVUcmFuc2NyaXB0RW50cnksXG4gICAgY2xlYXJUcmFuc2NyaXB0SGlzdG9yeSxcbiAgICBzZXRGaW5hbFRyYW5zY3JpcHQsXG4gICAgc2V0SW50ZXJpbVRyYW5zY3JpcHQsXG4gICAgc2V0VHJhbnNsYXRlZFRleHQsXG4gICAgdXBkYXRlU2V0dGluZ3MsXG4gICAgc2V0UmVjb3JkaW5nU3RhdHVzLFxuICAgIHNldFRyYW5zbGF0aW5nU3RhdHVzLFxuICAgIHNldFRUU1BsYXlpbmdTdGF0dXMsXG4gICAgc2V0U3BlZWNoRXJyb3IsXG4gICAgc2V0VHJhbnNsYXRpb25FcnJvcixcbiAgICBzZXRUVFNFcnJvcixcbiAgICByZXNldFRyYW5zY3JpcHQsXG4gIF0pO1xufTtcblxuLy8gSG9vayBmb3IgYWNjZXNzaW5nIGNhcHRpb24gc3RhdGUgc2VsZWN0b3JzXG5leHBvcnQgY29uc3QgdXNlQ2FwdGlvblNlbGVjdG9ycyA9ICgpID0+IHtcbiAgY29uc3QgeyBzdGF0ZSB9ID0gdXNlQ2FwdGlvbigpO1xuXG4gIGNvbnN0IGdldEN1cnJlbnRUcmFuc2NyaXB0ID0gUmVhY3QudXNlQ2FsbGJhY2soKCkgPT4ge1xuICAgIHJldHVybiBzdGF0ZS5maW5hbFRyYW5zY3JpcHQgKyAoc3RhdGUuaW50ZXJpbVRyYW5zY3JpcHQgPyAnICcgKyBzdGF0ZS5pbnRlcmltVHJhbnNjcmlwdCA6ICcnKTtcbiAgfSwgW3N0YXRlLmZpbmFsVHJhbnNjcmlwdCwgc3RhdGUuaW50ZXJpbVRyYW5zY3JpcHRdKTtcblxuICBjb25zdCBnZXRUcmFuc2NyaXB0V29yZENvdW50ID0gUmVhY3QudXNlQ2FsbGJhY2soKCkgPT4ge1xuICAgIGNvbnN0IGZ1bGxUcmFuc2NyaXB0ID0gZ2V0Q3VycmVudFRyYW5zY3JpcHQoKTtcbiAgICByZXR1cm4gZnVsbFRyYW5zY3JpcHQudHJpbSgpLnNwbGl0KC9cXHMrLykuZmlsdGVyKHdvcmQgPT4gd29yZC5sZW5ndGggPiAwKS5sZW5ndGg7XG4gIH0sIFtnZXRDdXJyZW50VHJhbnNjcmlwdF0pO1xuXG4gIGNvbnN0IGdldFNlc3Npb25EdXJhdGlvbiA9IFJlYWN0LnVzZUNhbGxiYWNrKCgpID0+IHtcbiAgICBpZiAoc3RhdGUudHJhbnNjcmlwdEhpc3RvcnkubGVuZ3RoID09PSAwKSByZXR1cm4gMDtcbiAgICBjb25zdCBmaXJzdEVudHJ5ID0gc3RhdGUudHJhbnNjcmlwdEhpc3RvcnlbMF07XG4gICAgY29uc3QgbGFzdEVudHJ5ID0gc3RhdGUudHJhbnNjcmlwdEhpc3Rvcnlbc3RhdGUudHJhbnNjcmlwdEhpc3RvcnkubGVuZ3RoIC0gMV07XG4gICAgcmV0dXJuIGxhc3RFbnRyeS50aW1lc3RhbXAgLSBmaXJzdEVudHJ5LnRpbWVzdGFtcDtcbiAgfSwgW3N0YXRlLnRyYW5zY3JpcHRIaXN0b3J5XSk7XG5cbiAgY29uc3QgaGFzQWN0aXZlVHJhbnNjcmlwdCA9IFJlYWN0LnVzZUNhbGxiYWNrKCgpID0+IHtcbiAgICByZXR1cm4gc3RhdGUuZmluYWxUcmFuc2NyaXB0Lmxlbmd0aCA+IDAgfHwgc3RhdGUuaW50ZXJpbVRyYW5zY3JpcHQubGVuZ3RoID4gMDtcbiAgfSwgW3N0YXRlLmZpbmFsVHJhbnNjcmlwdCwgc3RhdGUuaW50ZXJpbVRyYW5zY3JpcHRdKTtcblxuICBjb25zdCBnZXRMYW5ndWFnZVN0YXRzID0gUmVhY3QudXNlQ2FsbGJhY2soKCkgPT4ge1xuICAgIGNvbnN0IHN0YXRzOiB7IFtsYW5ndWFnZTogc3RyaW5nXTogbnVtYmVyIH0gPSB7fTtcbiAgICBzdGF0ZS50cmFuc2NyaXB0SGlzdG9yeS5mb3JFYWNoKGVudHJ5ID0+IHtcbiAgICAgIGNvbnN0IGxhbmcgPSBlbnRyeS5sYW5ndWFnZUNvZGUgfHwgJ3Vua25vd24nO1xuICAgICAgc3RhdHNbbGFuZ10gPSAoc3RhdHNbbGFuZ10gfHwgMCkgKyAxO1xuICAgIH0pO1xuICAgIHJldHVybiBzdGF0cztcbiAgfSwgW3N0YXRlLnRyYW5zY3JpcHRIaXN0b3J5XSk7XG5cbiAgcmV0dXJuIHtcbiAgICBnZXRDdXJyZW50VHJhbnNjcmlwdCxcbiAgICBnZXRUcmFuc2NyaXB0V29yZENvdW50LFxuICAgIGdldFNlc3Npb25EdXJhdGlvbixcbiAgICBoYXNBY3RpdmVUcmFuc2NyaXB0LFxuICAgIGdldExhbmd1YWdlU3RhdHMsXG4gIH07XG59O1xuXG5leHBvcnQgZGVmYXVsdCBDYXB0aW9uQ29udGV4dDtcbiJdLCJuYW1lcyI6WyJSZWFjdCIsImNyZWF0ZUNvbnRleHQiLCJ1c2VDb250ZXh0IiwidXNlUmVkdWNlciIsImluaXRpYWxTZXR0aW5ncyIsInNwZWVjaExhbmd1YWdlIiwidHJhbnNsYXRpb25FbmFibGVkIiwic291cmNlTGFuZ3VhZ2UiLCJ0YXJnZXRMYW5ndWFnZSIsInR0c0VuYWJsZWQiLCJ0dHNBdXRvUGxheSIsInNlbGVjdGVkVm9pY2UiLCJ0dHNSYXRlIiwidHRzUGl0Y2giLCJ0dHNWb2x1bWUiLCJpbml0aWFsU3RhdGUiLCJmaW5hbFRyYW5zY3JpcHQiLCJpbnRlcmltVHJhbnNjcmlwdCIsInRyYW5zbGF0ZWRUZXh0IiwidHJhbnNjcmlwdEhpc3RvcnkiLCJpc1JlY29yZGluZyIsImlzVHJhbnNsYXRpbmciLCJpc1RUU1BsYXlpbmciLCJzZXR0aW5ncyIsInNwZWVjaEVycm9yIiwidHJhbnNsYXRpb25FcnJvciIsInR0c0Vycm9yIiwic2Vzc2lvbklkIiwibGFzdFVwZGF0ZSIsImNhcHRpb25SZWR1Y2VyIiwic3RhdGUiLCJhY3Rpb24iLCJ0eXBlIiwicGF5bG9hZCIsIkRhdGUiLCJub3ciLCJtYXAiLCJlbnRyeSIsImlkIiwidXBkYXRlcyIsImZpbHRlciIsIkNhcHRpb25Db250ZXh0IiwiQ2FwdGlvblByb3ZpZGVyIiwiY2hpbGRyZW4iLCJkaXNwYXRjaCIsInVzZUVmZmVjdCIsInNhdmVkU2V0dGluZ3MiLCJsb2NhbFN0b3JhZ2UiLCJnZXRJdGVtIiwiSlNPTiIsInBhcnNlIiwiZXJyb3IiLCJjb25zb2xlIiwid2FybiIsInNldEl0ZW0iLCJzdHJpbmdpZnkiLCJsZW5ndGgiLCJzYXZlU2Vzc2lvbiIsInNlc3Npb25EYXRhIiwiaW50ZXJ2YWwiLCJzZXRJbnRlcnZhbCIsImNsZWFySW50ZXJ2YWwiLCJjb250ZXh0VmFsdWUiLCJ1c2VNZW1vIiwiUHJvdmlkZXIiLCJ2YWx1ZSIsInVzZUNhcHRpb24iLCJjb250ZXh0IiwiRXJyb3IiLCJ1c2VDYXB0aW9uQWN0aW9ucyIsImFkZFRyYW5zY3JpcHRFbnRyeSIsInVzZUNhbGxiYWNrIiwidXBkYXRlVHJhbnNjcmlwdEVudHJ5IiwiZGVsZXRlVHJhbnNjcmlwdEVudHJ5IiwiY2xlYXJUcmFuc2NyaXB0SGlzdG9yeSIsInNldEZpbmFsVHJhbnNjcmlwdCIsInRyYW5zY3JpcHQiLCJzZXRJbnRlcmltVHJhbnNjcmlwdCIsInNldFRyYW5zbGF0ZWRUZXh0IiwidGV4dCIsInVwZGF0ZVNldHRpbmdzIiwic2V0UmVjb3JkaW5nU3RhdHVzIiwic2V0VHJhbnNsYXRpbmdTdGF0dXMiLCJzZXRUVFNQbGF5aW5nU3RhdHVzIiwiaXNQbGF5aW5nIiwic2V0U3BlZWNoRXJyb3IiLCJzZXRUcmFuc2xhdGlvbkVycm9yIiwic2V0VFRTRXJyb3IiLCJyZXNldFRyYW5zY3JpcHQiLCJ1c2VDYXB0aW9uU2VsZWN0b3JzIiwiZ2V0Q3VycmVudFRyYW5zY3JpcHQiLCJnZXRUcmFuc2NyaXB0V29yZENvdW50IiwiZnVsbFRyYW5zY3JpcHQiLCJ0cmltIiwic3BsaXQiLCJ3b3JkIiwiZ2V0U2Vzc2lvbkR1cmF0aW9uIiwiZmlyc3RFbnRyeSIsImxhc3RFbnRyeSIsInRpbWVzdGFtcCIsImhhc0FjdGl2ZVRyYW5zY3JpcHQiLCJnZXRMYW5ndWFnZVN0YXRzIiwic3RhdHMiLCJmb3JFYWNoIiwibGFuZyIsImxhbmd1YWdlQ29kZSJdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/contexts/CaptionContext.tsx\n"));

/***/ })

});