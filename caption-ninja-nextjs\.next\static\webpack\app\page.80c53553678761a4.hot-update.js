"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./src/app/page.tsx":
/*!**************************!*\
  !*** ./src/app/page.tsx ***!
  \**************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Home)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var _contexts_CaptionContext__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/contexts/CaptionContext */ \"(app-pages-browser)/./src/contexts/CaptionContext.tsx\");\n/* harmony import */ var _components_SpeechRecognition_SpeechController__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/SpeechRecognition/SpeechController */ \"(app-pages-browser)/./src/components/SpeechRecognition/SpeechController.tsx\");\n/* harmony import */ var _components_VideoPlayer_VideoPlayerWithCaptions__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/VideoPlayer/VideoPlayerWithCaptions */ \"(app-pages-browser)/./src/components/VideoPlayer/VideoPlayerWithCaptions.tsx\");\n/* harmony import */ var _components_VideoPlayer_DeviceSelector__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/VideoPlayer/DeviceSelector */ \"(app-pages-browser)/./src/components/VideoPlayer/DeviceSelector.tsx\");\n/* harmony import */ var _components_Translation_TranslationSettings__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/Translation/TranslationSettings */ \"(app-pages-browser)/./src/components/Translation/TranslationSettings.tsx\");\n/* harmony import */ var _components_ErrorBoundary__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/ErrorBoundary */ \"(app-pages-browser)/./src/components/ErrorBoundary.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\nfunction Home() {\n    _s();\n    const { state } = (0,_contexts_CaptionContext__WEBPACK_IMPORTED_MODULE_1__.useCaption)();\n    const actions = (0,_contexts_CaptionContext__WEBPACK_IMPORTED_MODULE_1__.useCaptionActions)();\n    const selectors = (0,_contexts_CaptionContext__WEBPACK_IMPORTED_MODULE_1__.useCaptionSelectors)();\n    const handleTestTranscript = ()=>{\n        actions.setFinalTranscript(\"Hello, this is a test transcript from CAPTION.Ninja!\");\n        actions.setTranslatedText(\"¡Hola, esta es una transcripción de prueba de CAPTION.Ninja!\");\n    };\n    const handleClearTranscript = ()=>{\n        actions.resetTranscript();\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 p-8\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"max-w-4xl mx-auto\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"header\", {\n                    className: \"text-center mb-8\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                            className: \"text-4xl font-bold text-gray-900 mb-2\",\n                            children: \"CAPTION.Ninja\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive - UGSM-Monarch Business School GmbH\\\\Documents\\\\captionninja\\\\caption-ninja-nextjs\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 28,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-lg text-gray-600\",\n                            children: \"Real-time Speech-to-Text with Translation & Video Streaming\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive - UGSM-Monarch Business School GmbH\\\\Documents\\\\captionninja\\\\caption-ninja-nextjs\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 31,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive - UGSM-Monarch Business School GmbH\\\\Documents\\\\captionninja\\\\caption-ninja-nextjs\\\\src\\\\app\\\\page.tsx\",\n                    lineNumber: 27,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"mb-8\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ErrorBoundary__WEBPACK_IMPORTED_MODULE_6__.VideoPlayerErrorBoundary, {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_VideoPlayer_VideoPlayerWithCaptions__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                            className: \"w-full max-w-4xl mx-auto\",\n                            showControls: true,\n                            autoStart: false,\n                            aspectRatio: \"16:9\",\n                            quality: \"high\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive - UGSM-Monarch Business School GmbH\\\\Documents\\\\captionninja\\\\caption-ninja-nextjs\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 39,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive - UGSM-Monarch Business School GmbH\\\\Documents\\\\captionninja\\\\caption-ninja-nextjs\\\\src\\\\app\\\\page.tsx\",\n                        lineNumber: 38,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive - UGSM-Monarch Business School GmbH\\\\Documents\\\\captionninja\\\\caption-ninja-nextjs\\\\src\\\\app\\\\page.tsx\",\n                    lineNumber: 37,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"grid grid-cols-1 lg:grid-cols-3 gap-6 mb-8\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"lg:col-span-2\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ErrorBoundary__WEBPACK_IMPORTED_MODULE_6__.SpeechRecognitionErrorBoundary, {\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_SpeechRecognition_SpeechController__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {}, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive - UGSM-Monarch Business School GmbH\\\\Documents\\\\captionninja\\\\caption-ninja-nextjs\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 53,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive - UGSM-Monarch Business School GmbH\\\\Documents\\\\captionninja\\\\caption-ninja-nextjs\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 52,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive - UGSM-Monarch Business School GmbH\\\\Documents\\\\captionninja\\\\caption-ninja-nextjs\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 51,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"space-y-6\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"bg-white rounded-lg shadow-md p-6\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                            className: \"text-xl font-semibold text-gray-800 mb-4\",\n                                            children: \"System Status\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive - UGSM-Monarch Business School GmbH\\\\Documents\\\\captionninja\\\\caption-ninja-nextjs\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 61,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"space-y-2 text-sm\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"font-medium\",\n                                                            children: \"Session:\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive - UGSM-Monarch Business School GmbH\\\\Documents\\\\captionninja\\\\caption-ninja-nextjs\\\\src\\\\app\\\\page.tsx\",\n                                                            lineNumber: 66,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        \" \",\n                                                        state.sessionId.split('-').pop()\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive - UGSM-Monarch Business School GmbH\\\\Documents\\\\captionninja\\\\caption-ninja-nextjs\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 65,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"font-medium\",\n                                                            children: \"Recording:\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive - UGSM-Monarch Business School GmbH\\\\Documents\\\\captionninja\\\\caption-ninja-nextjs\\\\src\\\\app\\\\page.tsx\",\n                                                            lineNumber: 69,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        \" \",\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: state.isRecording ? \"text-green-600\" : \"text-red-600\",\n                                                            children: state.isRecording ? \"🔴 Live\" : \"⭕ Stopped\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive - UGSM-Monarch Business School GmbH\\\\Documents\\\\captionninja\\\\caption-ninja-nextjs\\\\src\\\\app\\\\page.tsx\",\n                                                            lineNumber: 70,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive - UGSM-Monarch Business School GmbH\\\\Documents\\\\captionninja\\\\caption-ninja-nextjs\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 68,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"font-medium\",\n                                                            children: \"Translation:\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive - UGSM-Monarch Business School GmbH\\\\Documents\\\\captionninja\\\\caption-ninja-nextjs\\\\src\\\\app\\\\page.tsx\",\n                                                            lineNumber: 75,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        \" \",\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: state.settings.translationEnabled ? \"text-green-600\" : \"text-gray-500\",\n                                                            children: state.settings.translationEnabled ? \"✅ Enabled\" : \"❌ Disabled\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive - UGSM-Monarch Business School GmbH\\\\Documents\\\\captionninja\\\\caption-ninja-nextjs\\\\src\\\\app\\\\page.tsx\",\n                                                            lineNumber: 76,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive - UGSM-Monarch Business School GmbH\\\\Documents\\\\captionninja\\\\caption-ninja-nextjs\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 74,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"font-medium\",\n                                                            children: \"Language:\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive - UGSM-Monarch Business School GmbH\\\\Documents\\\\captionninja\\\\caption-ninja-nextjs\\\\src\\\\app\\\\page.tsx\",\n                                                            lineNumber: 81,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        \" \",\n                                                        state.settings.speechLanguage\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive - UGSM-Monarch Business School GmbH\\\\Documents\\\\captionninja\\\\caption-ninja-nextjs\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 80,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"font-medium\",\n                                                            children: \"History:\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive - UGSM-Monarch Business School GmbH\\\\Documents\\\\captionninja\\\\caption-ninja-nextjs\\\\src\\\\app\\\\page.tsx\",\n                                                            lineNumber: 84,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        \" \",\n                                                        state.transcriptHistory.length,\n                                                        \" entries\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive - UGSM-Monarch Business School GmbH\\\\Documents\\\\captionninja\\\\caption-ninja-nextjs\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 83,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"font-medium\",\n                                                            children: \"Words:\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive - UGSM-Monarch Business School GmbH\\\\Documents\\\\captionninja\\\\caption-ninja-nextjs\\\\src\\\\app\\\\page.tsx\",\n                                                            lineNumber: 87,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        \" \",\n                                                        selectors.getTranscriptWordCount()\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive - UGSM-Monarch Business School GmbH\\\\Documents\\\\captionninja\\\\caption-ninja-nextjs\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 86,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive - UGSM-Monarch Business School GmbH\\\\Documents\\\\captionninja\\\\caption-ninja-nextjs\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 64,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"mt-4 pt-4 border-t border-gray-200\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                    className: \"text-sm font-medium text-gray-700 mb-2\",\n                                                    children: \"Quick Tests\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive - UGSM-Monarch Business School GmbH\\\\Documents\\\\captionninja\\\\caption-ninja-nextjs\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 93,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"space-y-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                            onClick: handleTestTranscript,\n                                                            className: \"w-full px-3 py-2 text-sm bg-blue-500 text-white rounded hover:bg-blue-600 transition-colors\",\n                                                            children: \"Add Test Transcript\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive - UGSM-Monarch Business School GmbH\\\\Documents\\\\captionninja\\\\caption-ninja-nextjs\\\\src\\\\app\\\\page.tsx\",\n                                                            lineNumber: 95,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                            onClick: handleClearTranscript,\n                                                            className: \"w-full px-3 py-2 text-sm bg-red-500 text-white rounded hover:bg-red-600 transition-colors\",\n                                                            children: \"Clear All\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive - UGSM-Monarch Business School GmbH\\\\Documents\\\\captionninja\\\\caption-ninja-nextjs\\\\src\\\\app\\\\page.tsx\",\n                                                            lineNumber: 101,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                            onClick: ()=>actions.updateSettings({\n                                                                    translationEnabled: !state.settings.translationEnabled\n                                                                }),\n                                                            className: \"w-full px-3 py-2 text-sm bg-purple-500 text-white rounded hover:bg-purple-600 transition-colors\",\n                                                            children: \"Toggle Translation\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive - UGSM-Monarch Business School GmbH\\\\Documents\\\\captionninja\\\\caption-ninja-nextjs\\\\src\\\\app\\\\page.tsx\",\n                                                            lineNumber: 107,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive - UGSM-Monarch Business School GmbH\\\\Documents\\\\captionninja\\\\caption-ninja-nextjs\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 94,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive - UGSM-Monarch Business School GmbH\\\\Documents\\\\captionninja\\\\caption-ninja-nextjs\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 92,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive - UGSM-Monarch Business School GmbH\\\\Documents\\\\captionninja\\\\caption-ninja-nextjs\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 60,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"bg-white rounded-lg shadow-md p-6\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                            className: \"text-xl font-semibold text-gray-800 mb-4\",\n                                            children: \"Device Settings\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive - UGSM-Monarch Business School GmbH\\\\Documents\\\\captionninja\\\\caption-ninja-nextjs\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 121,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_VideoPlayer_DeviceSelector__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                            showAudioDevices: true,\n                                            showVideoDevices: true\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive - UGSM-Monarch Business School GmbH\\\\Documents\\\\captionninja\\\\caption-ninja-nextjs\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 124,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive - UGSM-Monarch Business School GmbH\\\\Documents\\\\captionninja\\\\caption-ninja-nextjs\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 120,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"bg-white rounded-lg shadow-md p-6\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ErrorBoundary__WEBPACK_IMPORTED_MODULE_6__.TranslationErrorBoundary, {\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Translation_TranslationSettings__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {}, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive - UGSM-Monarch Business School GmbH\\\\Documents\\\\captionninja\\\\caption-ninja-nextjs\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 133,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive - UGSM-Monarch Business School GmbH\\\\Documents\\\\captionninja\\\\caption-ninja-nextjs\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 132,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive - UGSM-Monarch Business School GmbH\\\\Documents\\\\captionninja\\\\caption-ninja-nextjs\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 131,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive - UGSM-Monarch Business School GmbH\\\\Documents\\\\captionninja\\\\caption-ninja-nextjs\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 58,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive - UGSM-Monarch Business School GmbH\\\\Documents\\\\captionninja\\\\caption-ninja-nextjs\\\\src\\\\app\\\\page.tsx\",\n                    lineNumber: 49,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"bg-white rounded-lg shadow-md p-6\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                            className: \"text-xl font-semibold text-gray-800 mb-4\",\n                            children: \"Transcript History\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive - UGSM-Monarch Business School GmbH\\\\Documents\\\\captionninja\\\\caption-ninja-nextjs\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 141,\n                            columnNumber: 11\n                        }, this),\n                        state.transcriptHistory.length > 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"space-y-3 max-h-96 overflow-y-auto\",\n                            children: [\n                                state.transcriptHistory.slice(-10).reverse().map((entry, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"p-3 bg-gray-50 border border-gray-200 rounded-lg\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-start justify-between mb-2\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center gap-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-xs bg-blue-100 text-blue-800 px-2 py-1 rounded\",\n                                                            children: [\n                                                                \"#\",\n                                                                state.transcriptHistory.length - index\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive - UGSM-Monarch Business School GmbH\\\\Documents\\\\captionninja\\\\caption-ninja-nextjs\\\\src\\\\app\\\\page.tsx\",\n                                                            lineNumber: 151,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-xs text-gray-500\",\n                                                            children: new Date(entry.timestamp).toLocaleTimeString()\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive - UGSM-Monarch Business School GmbH\\\\Documents\\\\captionninja\\\\caption-ninja-nextjs\\\\src\\\\app\\\\page.tsx\",\n                                                            lineNumber: 154,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        entry.languageCode && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-xs bg-green-100 text-green-800 px-2 py-1 rounded\",\n                                                            children: entry.languageCode\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive - UGSM-Monarch Business School GmbH\\\\Documents\\\\captionninja\\\\caption-ninja-nextjs\\\\src\\\\app\\\\page.tsx\",\n                                                            lineNumber: 158,\n                                                            columnNumber: 25\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive - UGSM-Monarch Business School GmbH\\\\Documents\\\\captionninja\\\\caption-ninja-nextjs\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 150,\n                                                    columnNumber: 21\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive - UGSM-Monarch Business School GmbH\\\\Documents\\\\captionninja\\\\caption-ninja-nextjs\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 149,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-gray-800 text-sm leading-relaxed\",\n                                                children: entry.text\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive - UGSM-Monarch Business School GmbH\\\\Documents\\\\captionninja\\\\caption-ninja-nextjs\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 164,\n                                                columnNumber: 19\n                                            }, this),\n                                            entry.translatedText && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-blue-600 text-sm mt-2 italic border-l-2 border-blue-200 pl-2\",\n                                                children: entry.translatedText\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive - UGSM-Monarch Business School GmbH\\\\Documents\\\\captionninja\\\\caption-ninja-nextjs\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 166,\n                                                columnNumber: 21\n                                            }, this)\n                                        ]\n                                    }, entry.id, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive - UGSM-Monarch Business School GmbH\\\\Documents\\\\captionninja\\\\caption-ninja-nextjs\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 148,\n                                        columnNumber: 17\n                                    }, this)),\n                                state.transcriptHistory.length > 10 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-center text-sm text-gray-500 py-2\",\n                                    children: [\n                                        \"Showing last 10 entries of \",\n                                        state.transcriptHistory.length,\n                                        \" total\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive - UGSM-Monarch Business School GmbH\\\\Documents\\\\captionninja\\\\caption-ninja-nextjs\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 173,\n                                    columnNumber: 17\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive - UGSM-Monarch Business School GmbH\\\\Documents\\\\captionninja\\\\caption-ninja-nextjs\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 146,\n                            columnNumber: 13\n                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-center py-8 text-gray-500\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-4xl mb-2\",\n                                    children: \"\\uD83C\\uDFA4\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive - UGSM-Monarch Business School GmbH\\\\Documents\\\\captionninja\\\\caption-ninja-nextjs\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 180,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    children: \"No transcript history yet\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive - UGSM-Monarch Business School GmbH\\\\Documents\\\\captionninja\\\\caption-ninja-nextjs\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 181,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-sm mt-1\",\n                                    children: \"Start recording to see your speech transcribed here\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive - UGSM-Monarch Business School GmbH\\\\Documents\\\\captionninja\\\\caption-ninja-nextjs\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 182,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive - UGSM-Monarch Business School GmbH\\\\Documents\\\\captionninja\\\\caption-ninja-nextjs\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 179,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive - UGSM-Monarch Business School GmbH\\\\Documents\\\\captionninja\\\\caption-ninja-nextjs\\\\src\\\\app\\\\page.tsx\",\n                    lineNumber: 140,\n                    columnNumber: 9\n                }, this),\n                (state.speechError || state.translationError || state.ttsError) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"bg-red-50 border border-red-200 rounded-lg p-4\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                            className: \"font-medium text-red-800 mb-2\",\n                            children: \"Errors:\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive - UGSM-Monarch Business School GmbH\\\\Documents\\\\captionninja\\\\caption-ninja-nextjs\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 190,\n                            columnNumber: 13\n                        }, this),\n                        state.speechError && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-red-700 text-sm\",\n                            children: [\n                                \"Speech: \",\n                                state.speechError\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive - UGSM-Monarch Business School GmbH\\\\Documents\\\\captionninja\\\\caption-ninja-nextjs\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 192,\n                            columnNumber: 15\n                        }, this),\n                        state.translationError && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-red-700 text-sm\",\n                            children: [\n                                \"Translation: \",\n                                state.translationError\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive - UGSM-Monarch Business School GmbH\\\\Documents\\\\captionninja\\\\caption-ninja-nextjs\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 195,\n                            columnNumber: 15\n                        }, this),\n                        state.ttsError && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-red-700 text-sm\",\n                            children: [\n                                \"TTS: \",\n                                state.ttsError\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive - UGSM-Monarch Business School GmbH\\\\Documents\\\\captionninja\\\\caption-ninja-nextjs\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 198,\n                            columnNumber: 15\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive - UGSM-Monarch Business School GmbH\\\\Documents\\\\captionninja\\\\caption-ninja-nextjs\\\\src\\\\app\\\\page.tsx\",\n                    lineNumber: 189,\n                    columnNumber: 11\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"footer\", {\n                    className: \"text-center text-gray-600 text-sm\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            children: \"CAPTION.Ninja Next.js Migration - Context Testing Page\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive - UGSM-Monarch Business School GmbH\\\\Documents\\\\captionninja\\\\caption-ninja-nextjs\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 204,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"mt-1\",\n                            children: \"This page demonstrates the centralized state management system\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive - UGSM-Monarch Business School GmbH\\\\Documents\\\\captionninja\\\\caption-ninja-nextjs\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 205,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive - UGSM-Monarch Business School GmbH\\\\Documents\\\\captionninja\\\\caption-ninja-nextjs\\\\src\\\\app\\\\page.tsx\",\n                    lineNumber: 203,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive - UGSM-Monarch Business School GmbH\\\\Documents\\\\captionninja\\\\caption-ninja-nextjs\\\\src\\\\app\\\\page.tsx\",\n            lineNumber: 26,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive - UGSM-Monarch Business School GmbH\\\\Documents\\\\captionninja\\\\caption-ninja-nextjs\\\\src\\\\app\\\\page.tsx\",\n        lineNumber: 25,\n        columnNumber: 5\n    }, this);\n}\n_s(Home, \"UKz95Ypqnn+5m1AMQ0CsYbmEMtQ=\", false, function() {\n    return [\n        _contexts_CaptionContext__WEBPACK_IMPORTED_MODULE_1__.useCaption,\n        _contexts_CaptionContext__WEBPACK_IMPORTED_MODULE_1__.useCaptionActions,\n        _contexts_CaptionContext__WEBPACK_IMPORTED_MODULE_1__.useCaptionSelectors\n    ];\n});\n_c = Home;\nvar _c;\n$RefreshReg$(_c, \"Home\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/page.tsx\n"));

/***/ })

});