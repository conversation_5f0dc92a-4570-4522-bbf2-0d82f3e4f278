"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./src/hooks/useSpeechRecognition.ts":
/*!*******************************************!*\
  !*** ./src/hooks/useSpeechRecognition.ts ***!
  \*******************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n/**\n * useSpeechRecognition Hook\n * \n * React hook for managing speech recognition functionality.\n * Based on the speech recognition implementation from index.html.\n */ \nconst useSpeechRecognition = function() {\n    let options = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : {};\n    // Memoize default options to prevent recreation on every render\n    const defaultOptions = (0,react__WEBPACK_IMPORTED_MODULE_0__.useMemo)({\n        \"useSpeechRecognition.useMemo[defaultOptions]\": ()=>({\n                language: 'en-US',\n                continuous: true,\n                interimResults: true,\n                maxAlternatives: 1\n            })\n    }[\"useSpeechRecognition.useMemo[defaultOptions]\"], []);\n    // Memoize merged options to prevent infinite re-renders\n    const mergedOptions = (0,react__WEBPACK_IMPORTED_MODULE_0__.useMemo)({\n        \"useSpeechRecognition.useMemo[mergedOptions]\": ()=>({\n                ...defaultOptions,\n                ...options\n            })\n    }[\"useSpeechRecognition.useMemo[mergedOptions]\"], [\n        defaultOptions,\n        options.language,\n        options.continuous,\n        options.interimResults,\n        options.maxAlternatives\n    ]);\n    const [finalTranscript, setFinalTranscript] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)('');\n    const [interimTranscript, setInterimTranscript] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)('');\n    const [isListening, setIsListening] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(false);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(null);\n    const [isSupported, setIsSupported] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(false);\n    const recognitionRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(null);\n    const optionsRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(mergedOptions);\n    const restartTimeoutRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(null);\n    const restartAttemptsRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(0);\n    const isStoppedManuallyRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(false);\n    const MAX_RESTART_ATTEMPTS = 3;\n    const RESTART_DELAY = 1000;\n    // Update options ref when merged options change\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)({\n        \"useSpeechRecognition.useEffect\": ()=>{\n            optionsRef.current = mergedOptions;\n        }\n    }[\"useSpeechRecognition.useEffect\"], [\n        mergedOptions\n    ]);\n    // Initialize speech recognition\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)({\n        \"useSpeechRecognition.useEffect\": ()=>{\n            if (false) {}\n            const SpeechRecognition = window.SpeechRecognition || window.webkitSpeechRecognition;\n            if (!SpeechRecognition) {\n                setIsSupported(false);\n                setError('Speech recognition is not supported in this browser');\n                return;\n            }\n            setIsSupported(true);\n            const recognition = new SpeechRecognition();\n            recognitionRef.current = recognition;\n            // Configure recognition\n            recognition.continuous = optionsRef.current.continuous;\n            recognition.interimResults = optionsRef.current.interimResults;\n            recognition.lang = optionsRef.current.language;\n            recognition.maxAlternatives = optionsRef.current.maxAlternatives;\n            // Event handlers\n            recognition.onstart = ({\n                \"useSpeechRecognition.useEffect\": ()=>{\n                    setIsListening(true);\n                    setError(null);\n                }\n            })[\"useSpeechRecognition.useEffect\"];\n            recognition.onend = ({\n                \"useSpeechRecognition.useEffect\": ()=>{\n                    console.log('Speech recognition ended');\n                    setIsListening(false);\n                    // Auto-restart if not stopped manually and continuous mode is enabled\n                    if (!isStoppedManuallyRef.current && optionsRef.current.continuous) {\n                        if (restartAttemptsRef.current < MAX_RESTART_ATTEMPTS) {\n                            console.log(\"Attempting to restart recognition (attempt \".concat(restartAttemptsRef.current + 1, \")\"));\n                            restartAttemptsRef.current++;\n                            restartTimeoutRef.current = setTimeout({\n                                \"useSpeechRecognition.useEffect\": ()=>{\n                                    try {\n                                        if (recognitionRef.current && !isStoppedManuallyRef.current) {\n                                            recognitionRef.current.start();\n                                        }\n                                    } catch (err) {\n                                        console.error('Failed to restart recognition:', err);\n                                        setError('Failed to restart speech recognition');\n                                    }\n                                }\n                            }[\"useSpeechRecognition.useEffect\"], RESTART_DELAY);\n                        } else {\n                            setError('Maximum restart attempts reached. Please restart manually.');\n                        }\n                    }\n                }\n            })[\"useSpeechRecognition.useEffect\"];\n            recognition.onerror = ({\n                \"useSpeechRecognition.useEffect\": (event)=>{\n                    console.error('Speech recognition error:', event);\n                    // Handle different error types based on original implementation\n                    switch(event.error){\n                        case 'no-speech':\n                            setError('No speech detected. Still listening...');\n                            break;\n                        case 'audio-capture':\n                            setError('No microphone detected. Please check your microphone.');\n                            setIsListening(false);\n                            break;\n                        case 'not-allowed':\n                            setError('Microphone access denied. Please allow microphone access.');\n                            setIsListening(false);\n                            break;\n                        case 'network':\n                            setError('Network error occurred. Will attempt to reconnect...');\n                            break;\n                        case 'aborted':\n                            console.log('Recognition aborted');\n                            if (!isStoppedManuallyRef.current) {\n                                setError('Recognition was aborted unexpectedly');\n                            }\n                            break;\n                        default:\n                            setError(\"Speech recognition error: \".concat(event.error));\n                            setIsListening(false);\n                    }\n                }\n            })[\"useSpeechRecognition.useEffect\"];\n            recognition.onresult = ({\n                \"useSpeechRecognition.useEffect\": (event)=>{\n                    if (typeof event.results === 'undefined') {\n                        console.log('Undefined results in event:', event);\n                        return;\n                    }\n                    let interim = '';\n                    let final = '';\n                    // Process only new results since last event\n                    for(let i = event.resultIndex; i < event.results.length; i++){\n                        const transcript = event.results[i][0].transcript.trim();\n                        if (event.results[i].isFinal) {\n                            final += (final ? ' ' : '') + transcript;\n                        } else {\n                            interim += (interim ? ' ' : '') + transcript;\n                        }\n                    }\n                    setInterimTranscript(interim);\n                    if (final) {\n                        setFinalTranscript({\n                            \"useSpeechRecognition.useEffect\": (prev)=>prev + (prev ? ' ' : '') + final\n                        }[\"useSpeechRecognition.useEffect\"]);\n                        // Reset restart attempts on successful recognition\n                        restartAttemptsRef.current = 0;\n                    }\n                }\n            })[\"useSpeechRecognition.useEffect\"];\n            return ({\n                \"useSpeechRecognition.useEffect\": ()=>{\n                    if (recognition) {\n                        recognition.stop();\n                    }\n                }\n            })[\"useSpeechRecognition.useEffect\"];\n        }\n    }[\"useSpeechRecognition.useEffect\"], []);\n    // Update language when it changes\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)({\n        \"useSpeechRecognition.useEffect\": ()=>{\n            if (recognitionRef.current && options.language) {\n                recognitionRef.current.lang = options.language;\n            }\n        }\n    }[\"useSpeechRecognition.useEffect\"], [\n        options.language\n    ]);\n    const startListening = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useSpeechRecognition.useCallback[startListening]\": ()=>{\n            if (!recognitionRef.current || !isSupported) {\n                setError('Speech recognition is not available');\n                return;\n            }\n            if (isListening) {\n                return;\n            }\n            // Clear any pending restart timeout\n            if (restartTimeoutRef.current) {\n                clearTimeout(restartTimeoutRef.current);\n                restartTimeoutRef.current = null;\n            }\n            isStoppedManuallyRef.current = false;\n            restartAttemptsRef.current = 0;\n            setError(null);\n            try {\n                recognitionRef.current.start();\n                console.log('Speech recognition started');\n            } catch (err) {\n                console.error('Failed to start speech recognition:', err);\n                if (err instanceof Error && err.name === 'InvalidStateError') {\n                    // Recognition is already running, try to stop and restart\n                    try {\n                        recognitionRef.current.stop();\n                        setTimeout({\n                            \"useSpeechRecognition.useCallback[startListening]\": ()=>{\n                                try {\n                                    recognitionRef.current.start();\n                                } catch (retryErr) {\n                                    setError('Failed to start speech recognition after retry');\n                                }\n                            }\n                        }[\"useSpeechRecognition.useCallback[startListening]\"], 100);\n                    } catch (stopErr) {\n                        setError('Failed to restart speech recognition');\n                    }\n                } else {\n                    setError('Failed to start speech recognition');\n                }\n            }\n        }\n    }[\"useSpeechRecognition.useCallback[startListening]\"], [\n        isListening,\n        isSupported\n    ]);\n    const stopListening = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useSpeechRecognition.useCallback[stopListening]\": ()=>{\n            if (!recognitionRef.current) return;\n            isStoppedManuallyRef.current = true;\n            // Clear any pending restart timeout\n            if (restartTimeoutRef.current) {\n                clearTimeout(restartTimeoutRef.current);\n                restartTimeoutRef.current = null;\n            }\n            try {\n                recognitionRef.current.stop();\n                console.log('Speech recognition stopped manually');\n            } catch (err) {\n                console.error('Failed to stop speech recognition:', err);\n                setError('Failed to stop speech recognition');\n            }\n        }\n    }[\"useSpeechRecognition.useCallback[stopListening]\"], []);\n    const resetTranscript = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useSpeechRecognition.useCallback[resetTranscript]\": ()=>{\n            setFinalTranscript('');\n            setInterimTranscript('');\n            setError(null);\n            restartAttemptsRef.current = 0;\n        }\n    }[\"useSpeechRecognition.useCallback[resetTranscript]\"], []);\n    const setLanguage = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useSpeechRecognition.useCallback[setLanguage]\": (language)=>{\n            if (recognitionRef.current) {\n                recognitionRef.current.lang = language;\n                optionsRef.current.language = language;\n            }\n        }\n    }[\"useSpeechRecognition.useCallback[setLanguage]\"], []);\n    // Cleanup on unmount\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)({\n        \"useSpeechRecognition.useEffect\": ()=>{\n            return ({\n                \"useSpeechRecognition.useEffect\": ()=>{\n                    if (restartTimeoutRef.current) {\n                        clearTimeout(restartTimeoutRef.current);\n                    }\n                    if (recognitionRef.current) {\n                        isStoppedManuallyRef.current = true;\n                        try {\n                            recognitionRef.current.stop();\n                        } catch (err) {\n                            console.error('Error stopping recognition on cleanup:', err);\n                        }\n                    }\n                }\n            })[\"useSpeechRecognition.useEffect\"];\n        }\n    }[\"useSpeechRecognition.useEffect\"], []);\n    return {\n        finalTranscript,\n        interimTranscript,\n        isListening,\n        error,\n        isSupported,\n        startListening,\n        stopListening,\n        resetTranscript,\n        setLanguage\n    };\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (useSpeechRecognition);\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/hooks/useSpeechRecognition.ts\n"));

/***/ })

});